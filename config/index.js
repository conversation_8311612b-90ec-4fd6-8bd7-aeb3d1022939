/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const path = require('path');
require('taro-ci/bin/patch.require');

// 特殊配色
const { MODE_ENV, SOURCE_ROOT_PATH = 'src' } = process.env;
const colorThemes = [];
const colorThemesKey = colorThemes.includes(MODE_ENV) ? `.${MODE_ENV}` : '';
const config = {
  framework: 'react',
  projectName: 'miniapp_truck_smarts',
  date: '2019-12-20',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2,
  },
  sourceRoot: SOURCE_ROOT_PATH,
  plugins: [
    'taro-plugin-build',
    [
      '@tarojs/plugin-inject',
      {
        components: {
          Button: {
            'data-info': '',
            'data-page': '',
            'data-image': '',
          },
        },
      },
    ],
  ],
  // 配置混淆插件，打包自动移除console相关代码
  terser: {
    enable: true,
    config: {
      compress: {
        drop_console: process.env.DEBUG_ENV !== 'OPEN',
      },
    },
  },
  // 小程序配置从 weapp 改为 mini，可以删掉很多小配置
  mini: {
    // webpackChain(chain, webpack) {
    //   // 包大小分析
    //   // chain
    //   //   .plugin("analyzer")
    //   //   .use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin, []);
    // },
    imageUrlLoaderOption: {
      limit: 0, // 大小限制，单位为 b
    },
    cssLoaderOption: {},
    postcss: {
      pxtransform: {
        enable: true,
        config: {},
      },
      url: {
        enable: true,
        config: {
          limit: 10240, // 设定转换尺寸上限
        },
      },
    },
    optimizeMainPackage: {
      enable: process.env.NODE_ENV !== 'development',
    },
    miniCssExtractPluginOption: {
      ignoreOrder: true,
    },
  },
  // 可以删掉很多小配置
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    webpackChain(chain, webpack) {},
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
          browsers: ['last 3 versions', 'Android >= 4.1', 'ios >= 8'],
        },
      },
    },
  },
  sass: {
    resource: [
      `src/styles/color${colorThemesKey}.scss`,
      'src/lib/modules_base/styles/variable.scss',
      'src/lib/modules_base/styles/mixins.scss',
    ],
    projectDirectory: path.resolve(__dirname, '..'),
  },
  alias: {
    '~': path.resolve(SOURCE_ROOT_PATH),
    '~base': path.resolve(`${SOURCE_ROOT_PATH}/lib/modules_base`),
    '@': path.resolve(SOURCE_ROOT_PATH),
    '@base': path.resolve(`${SOURCE_ROOT_PATH}/lib/modules_base`),
  },
};

module.exports = function (merge) {
  const MODE_ENV = process.env.MODE_ENV || 'c';
  const {
    NODE_ENV,
    TARO_ENV,
    DEBUG_ENV,
    PLATFORM_ENV,
    APP_VERSION,
    ROOT_PATH = `dist/${TARO_ENV}.${NODE_ENV === 'development' ? 'dev' : 'build'}`,
    PROJECT_ENV,
  } = process.env;
  let mergeConfig = {};
  if (NODE_ENV === 'development') {
    mergeConfig = merge({}, config, require('./dev'));
  } else {
    mergeConfig = merge({}, config, require('./prod'));
  }

  mergeConfig.copy = {
    patterns: [],
    ...mergeConfig.copy,
  };
  mergeConfig.outputRoot = ROOT_PATH; //-alipay：支付宝 | -weapp：微信
  mergeConfig.env.MODE_ENV = `'${MODE_ENV}'`;
  mergeConfig.env.PLATFORM_ENV = `'${PLATFORM_ENV}'`;
  mergeConfig.env.APP_VERSION = `'${APP_VERSION}'`;
  mergeConfig.env.DEBUG_ENV = `'${DEBUG_ENV}'`;
  mergeConfig.env.PROJECT_ENV = `'${PROJECT_ENV}'`;

  const copyUrls = [];

  // 复制加密文件
  if (TARO_ENV === 'weapp') {
    mergeConfig.copy.patterns.push({
      from: `src/lib/config/config.request/crypto.${TARO_ENV}.wasm`,
      to: `${mergeConfig.outputRoot}/crypto.wasm`,
    });

    mergeConfig.copy.patterns.push({
      from: `src/lib/config/config.request/crypto.websocket.wasm`,
      to: `${mergeConfig.outputRoot}/crypto.websocket.wasm`,
    });
  }

  copyUrls.forEach((item) => {
    const [url, root = ''] = item;
    mergeConfig.copy.patterns.push({
      from: `src/${url}`,
      to: `${mergeConfig.outputRoot}${root ? `/${root}` : ''}/${url}`,
    });
  });

  return mergeConfig;
};
