module.exports = {
  extends: ["taro/react"],
  globals: {
    my: true,
    wx: true,
    preval: true,
    swan: true,
    qa: true,
  },
  rules: {
    "react/sort-comp": 0,
    "react/jsx-indent-props": 0,
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "taro/no-spread-in-props": 0,
    "import/first": 1,
    "no-unused-vars": [
      "error",
      {
        ignoreRestSiblings: true,
        varsIgnorePattern: "Taro|React",
      },
    ],
    "react/jsx-filename-extension": [
      1,
      {
        extensions: [".js", ".jsx", ".tsx"],
      },
    ],
    "react/no-deprecated": 0,
    // semi: [0],
    // singleQuote: 1,
    // 'no-extra-bind': 1,
    // eqeqeq: 'off',
    // 'no-nested-ternary': 0,
    // 'no-underscore-dangle': 0,
    "no-param-reassign": 0,
    // 'no-extraneous-dependencies': 0,
    "no-shadow": 0,
    "import/prefer-default-export": 0,
    "taro/this-props-function": 0,
    "react/no-did-update-set-state": "warning",
  },
  parser: "babel-eslint",
  settings: {
    react: {
      version: "detect",
    },
  },
};
