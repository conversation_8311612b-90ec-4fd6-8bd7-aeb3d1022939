image: ${image}

stages:
  - auto
  - deploy_to_test
  - deploy_to_rc
  - deploy_to_prod

cache:
  key: ${CI_PROJECT_NAME}
  paths:
    - node_modules/

before_script:
  - if [[ "${DEBUG}" == "TRUE" ]];then sleep 600;fi
  - if [[ "${CI_PROJECT_NAME}" == "" ]]; then echo "检测 CI_PROJECT_NAME 变量没有定义或为空，任务退出..."; exit 1;fi

# 体验-新
deploy_test:
  stage: deploy_to_test
  script:
    - npm run deploy -- --branch='${CI_COMMIT_BRANCH}' --dd='${dd_access_token},${dd_secret}' --ci='${alipay_tool_id},${alipay_private_key},${swan_token}' --message='${CI_COMMIT_MESSAGE}'
  only:
    - /test_+/
  tags:
    - gitlab-runner-miniapp

# 正式-新
deploy_prod:
  stage: deploy_to_prod
  script:
    - npm run deploy -- --branch='${CI_COMMIT_BRANCH}' --dd='${dd_access_token},${dd_secret}' --privacy='${dd_notice_robot_token}' --ci='${alipay_tool_id},${alipay_private_key},${swan_token}' --message='${CI_COMMIT_MESSAGE}'
  only:
    - /master_+/
  tags:
    - gitlab-runner-miniapp
  when: manual
