/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-address_history {
  &-wrapper {
    flex-wrap: wrap;
    gap: 24px;
    padding-top: $spacing-h-md;
  }
  &-item {
    padding: 8px 28px;
    color: $color-grey-1;
    font-size: $font-size-sm;
    background-color: $color-white;
    border-radius: 28px;
    &--active {
      color: $color-brand;
      border: $width-base solid $color-brand;
    }
  }
  &-more {
    font-size: $font-size-sm !important;
    transform: rotate(90deg);
  }
}
