/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { useState } from 'react';
import { AtIcon } from 'taro-ui';
import { useStorage } from '~base/utils/storage';
import './index.scss';

const HISTORY_MAX = 6;
export const HISTORY_LOCATION_KEY = 'HISTORY_LOCATION_KEY';

const History = (props) => {
  const { onClick, selected } = props;
  const { data, remove } = useStorage(HISTORY_LOCATION_KEY, true);
  const [showMore, setShowMore] = useState(false);

  const handleRemove = () => {
    Taro.kbModal({
      closable: false,
      title: '温馨提示',
      content: [{ className: 'kb-text__center', text: '是否确认删除？' }],
      confirmText: '确认',
      cancelText: '取消',
      onConfirm: remove,
    });
  };

  return (
    <View className='kb-spacing-md'>
      <View className='at-row at-row__align--center at-row__justify--between'>
        <View className='kb-size__lg kb-color__black kb-text__bold'>最近使用</View>
        <AtIcon
          className='kb-size__base kb-color__grey'
          prefixClass='kb-icon'
          value='delete'
          onClick={handleRemove}
        />
      </View>
      <View className='kb-address_history-wrapper at-row at-row__align--center'>
        {data?.data?.slice(0, showMore ? data?.data?.length : HISTORY_MAX).map((item) => (
          <View
            key={item}
            className={classNames('kb-address_history-item', {
              'kb-address_history-item--active':
                selected?.city == item.city && selected?.district == item.district,
            })}
            hoverClass='kb-hover'
            onClick={() => onClick({ ...item, history: true })}
          >
            {item.city}-{item.district}
          </View>
        ))}
        {data?.data?.length > HISTORY_MAX && !showMore && (
          <View
            className='kb-address_history-item'
            hoverClass='kb-hover'
            onClick={() => setShowMore(true)}
          >
            <AtIcon prefixClass='kb-icon' value='arrow' className='kb-address_history-more' />
          </View>
        )}
      </View>
    </View>
  );
};

History.options = {
  addGlobalClass: true,
};
export default History;
