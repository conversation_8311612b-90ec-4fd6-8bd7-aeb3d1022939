/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import { useEffect, useMemo } from 'react';
import { AtButton, AtIcon } from 'taro-ui';
import { useCheckAddressSupport } from '~/components/_pages/index/client/utils';
import { getLocationByLat } from '~/services/user/address';
import { useRequest } from '~base/utils/request/hooks';
import { getLocationDesc, useLocation } from '~base/components/location/_utils/useLocation';
import './index.scss';

const Location = () => {
  const { locationInfo, scope, openSetting, refresh } = useLocation();

  const { data, run } = useRequest(getLocationByLat, {
    manual: true,
  });

  const { inServices } = useCheckAddressSupport(data);

  useEffect(() => {
    if (locationInfo) {
      run({
        lat: locationInfo.latitude,
        lon: locationInfo.longitude,
      });
    }
  }, [locationInfo]);

  const label = useMemo(() => {
    return getLocationDesc(data);
  }, [data]);

  return (
    <View className='kb-location'>
      {!scope ? (
        <AtButton circle size='small' type='primary' onClick={openSetting}>
          请开启定位权限
        </AtButton>
      ) : (
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View className='at-row at-row__align--center'>
            <View>
              <Text>当前定位：</Text>
              <Text className='kb-size__base2'>{label}</Text>
            </View>
            {!inServices && <View className='notInServices'>地区无服务</View>}
          </View>
          <View hoverClass='kb-hover-opacity' onClick={refresh}>
            <AtIcon prefixClass='kb-icon' value='dingwei' className='kb-color__brand kb-size__xl' />
          </View>
        </View>
      )}
    </View>
  );
};

Location.options = {
  addGlobalClass: true,
};

export default Location;
