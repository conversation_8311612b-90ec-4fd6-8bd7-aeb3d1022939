/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import AddressChooseList from '~/components/_pages/address/choose/list';
import { clientLocationKey } from '~/components/_pages/index/client/address/utils';
import KbQueryBar from '~/components/_pages/query-bar';
import { setAddress } from '~/services/user/address';
import { getStorageSync, setStorage } from '~base/utils/storage';
import { debounce } from '~base/utils/utils';
import History, { HISTORY_LOCATION_KEY } from './history';
import './index.scss';
import Location from './location';
import { getLocationDesc } from '~base/components/location/_utils/useLocation';

class Index extends Component {
  // eslint-disable-next-line react/sort-comp
  config = {
    navigationBarTitleText: '选择地址',
  };
  constructor() {
    this.state = {
      active: false,
    };
    this.handleSearch = debounce(this.handleSearch, 300, {
      leading: false,
      trailing: true,
    });
  }

  handleSearch = (v) => {
    this.setState({
      active: {
        keywords: v,
      },
    });
  };

  handleChoose = (item) => {
    const params = {
      ...item,
      landmark: item.name,
    };
    const { params: { page } = {} } = this.$router;
    if (!item.history && page == 'serviceIndex') {
      const { data = [] } = getStorageSync(HISTORY_LOCATION_KEY) || [];

      const _data = [
        ...data.filter((item) => item.city != params.city || item.district != params.district),
        {
          city: params.city,
          district: params.district,
        },
      ];
      _data.reverse();
      setStorage({
        key: HISTORY_LOCATION_KEY,
        data: _data,
      });
      delete params.history;
    }
    if (page == 'addressEdit' || page == 'serviceIndex') {
      Taro.navigator({
        post: {
          type: 'locationChange',
          data: params,
        },
      });
      return;
    }
    setAddress(params).then((res) => {
      if (!res) return;
      Taro.kbToast({
        text: '设置成功',
      });

      setTimeout(() => {
        if (page == 'login') {
          Taro.navigator({
            url: 'index',
            target: 'tab',
          });
        } else {
          Taro.navigator({
            post: {
              type: 'locationChange',
              data: params,
            },
          });
        }
      }, 700);
    });
  };

  render() {
    const { active, ...rest } = this.state;
    const { params: { page, ...locationProps } = {} } = this.$router;

    const location = Taro.kbGetGlobalData(clientLocationKey) || {};
    const { city = '' } = location;

    const _active = active
      ? {
          ...active,
          city,
        }
      : false;

    return (
      <KbPage
        {...rest}
        renderHeader={
          <View className='kb-spacing-md '>
            <KbQueryBar
              placeholder={page == 'serviceIndex' ? '输入市/区/县' : '输入名称'}
              autoClean
              onSearch={this.handleSearch}
              onChange={this.handleSearch}
              theme='white'
              showSearchButton={false}
            />
          </View>
        }
      >
        <View className='kb-address at-row at-row__direction--column'>
          {page == 'serviceIndex' ? (
            <>
              <Location />
              <History selected={locationProps} onClick={this.handleChoose} />
            </>
          ) : city ? (
            <View className='kb-size__base2 kb-spacing-md-l'>
              当前定位城市：{getLocationDesc(location)}
            </View>
          ) : null}
          <View className='kb-flex1'>
            <AddressChooseList active={_active} onClick={this.handleChoose} />
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
