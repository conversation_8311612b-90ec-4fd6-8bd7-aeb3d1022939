/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component } from '@tarojs/taro';
import OrderRenewal from '~/components/_pages/order/renewal';
import KbPage from '~base/components/page';

class OrderRenewalPage extends Component {
  config = {
    navigationBarTitleText: '车辆续租',
  };
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { order_id } = this.$router.params || {};
    return (
      <KbPage layout={false}>
        <OrderRenewal order_id={order_id} />
      </KbPage>
    );
  }
}

export default OrderRenewalPage;
