/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component } from '@tarojs/taro';
import OrderSettle from '~/components/_pages/order/settle';
import KbPage from '~base/components/page';

class OrderSettlePage extends Component {
  config = {
    navigationBarTitleText: '回车结算',
  };
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    const { order_id } = this.$router.params || {};
    return (
      <KbPage layout={false}>
        <OrderSettle order_id={order_id} />
      </KbPage>
    );
  }
}

export default OrderSettlePage;
