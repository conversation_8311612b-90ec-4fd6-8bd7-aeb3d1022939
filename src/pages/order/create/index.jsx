/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component } from '@tarojs/taro';
import OrderCreateIndex from '~/components/_pages/order/create';
import KbPage from '~base/components/page';
import './index.scss';

class OrderDetailPage extends Component {
  config = {
    navigationBarTitleText: '确认订单',
  };

  constructor() {
    this.state = {
      data: {},
    };
  }

  onPostMessage = (key, params) => {
    if (key === 'routerParamsChange') {
      const { params: { data } = {} } = params || {};
      this.setState({
        data,
      });
    }
  };

  render() {
    const { data } = this.state;
    return (
      <KbPage>
        <OrderCreateIndex data={data} />
      </KbPage>
    );
  }
}

export default OrderDetailPage;
