/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component } from '@tarojs/taro';
import RentalOrderCreate from '~/components/_pages/order/create/rental';
import KbPage from '~base/components/page';

class Index extends Component {
  config = {
    navigationBarTitleText: '订单确认',
  };

  render() {
    return (
      <KbPage>
        <RentalOrderCreate />
      </KbPage>
    );
  }
}

export default Index;
