/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import LineDeliveryHistoryTabs from '~/components/_pages/line/delivery/history/tabs';
import KbPage from '~base/components/page';

class PageLineDeliveryHistory extends Component {
  constructor() {
    this.state = {};
  }
  config = {
    navigationBarTitleText: '选择历史配送路线',
    navigationBarBackgroundColor: '#ffffff',
  };

  render() {
    const { params } = this.$router
    return (
      <KbPage
        safeAreaGhost={false}
      >
        <LineDeliveryHistoryTabs params={params} />
      </KbPage>
    );
  }
}

export default PageLineDeliveryHistory;
