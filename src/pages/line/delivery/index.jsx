/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, Text, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { createRef } from 'react';
import { AtButton, AtIcon } from 'taro-ui';
import ContactsEditModal from '~/components/_pages/contacts/edit/modal';
import { orderNext } from '~/components/_pages/index/client/_utils/orderProcess';
import LineEdit from '~/components/_pages/line/edit';
import { formatLineEditForm, reformatLineEditForm } from '~/components/_pages/line/edit/_utils';
import KbPage from '~base/components/page';
import { randomCode } from '~base/utils/utils';

class PageLineDelivery extends Component {
  constructor() {
    this.state = {
      defaultValue: null,
      disabled: true
    };
    this.editRef = createRef();
  }
  config = {
    navigationBarTitleText: '配送路线',
    navigationBarBackgroundColor: '#ffffff',
  };

  onPostMessage = (key, data) => {
    switch (key) {
      case 'selectLineHistory':
        const defaultValue = formatLineEditForm(data?.dock_point_json);
        if (defaultValue) {
          this.setState({
            defaultValue
          });
        }
        break;

      default:
        break;
    }

  }

  handleSubmit = () => {
    const formData = this.editRef.current.getFieldsValue();
    const { stop } = formData;
    if (isArray(stop)) {
      // 选了联系方式，没有选停靠点
      const noPointIndex = stop.findIndex(item => item.contact?.value && !item.point?.value);
      if (noPointIndex >= 0) {
        Taro.kbToast({
          text: `请选择停靠点${1 + noPointIndex}`
        });
        return;
      }
    }
    orderNext({
      url: 'order/create',
      key: randomCode(),
      data: {
        routes: reformatLineEditForm(formData)
      }
    });
  }

  // 历史配送路线
  handleClickHistory = () => {
    Taro.navigator({
      url: 'line/delivery/history',
      options: this.$router.params
    });
  }

  // 表单信息变更
  handleLineChange = ({ disabled }) => {
    this.setState({
      disabled
    });
  }

  render() {
    const { disabled, defaultValue } = this.state;
    const { params } = this.$router;

    return (
      <KbPage
        safeAreaGhost={false}
        renderFooter={
          <View className='kb-spacing-md kb-background__white'>
            <AtButton
              type='primary'
              onClick={this.handleSubmit}
              disabled={disabled}
              circle
            >
              提交
            </AtButton>
          </View>
        }
      >
        <ScrollView className='kb-scrollview' scrollY>
          <LineEdit
            params={params}
            actionRef={this.editRef}
            onChange={this.handleLineChange}
            defaultValue={defaultValue}
            toolsRender={
              <View className="line-edit-form__tools--btn" hoverClass="kb-hover" onClick={this.handleClickHistory}>
                <AtIcon prefixClass="kb-icon" className="kb-color__brand kb-icon-size__base" value='clock' />
                <Text className="kb-icon__text--ml">历史配送路线</Text>
              </View>
            }
          />
        </ScrollView>
        <ContactsEditModal />
      </KbPage>
    );
  }
}

export default PageLineDelivery;
