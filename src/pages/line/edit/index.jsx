/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { createRef } from 'react';
import { AtButton } from 'taro-ui';
import { lineListRefreshKey } from '~/components/_pages/line/_utils';
import LineEdit from '~/components/_pages/line/edit';
import { formatLineEditForm, reformatLineEditForm } from '~/components/_pages/line/edit/_utils';
import { updateLine } from '~/services/line';
import longListRefresherManager from '~base/components/long-list/refresher';
import KbPage from '~base/components/page';

class PageLineEdit extends Component {
  constructor() {
    this.state = {
      loading: false,
      disabled: true,
      vehicle_owner_id: '',
      defaultValue: null
    };
    this.editRef = createRef();
  }
  config = {
    navigationBarTitleText: '',
  };

  setNavigationBarTitle = (params) => {
    const isEdit = !!params?.id;
    Taro.setNavigationBarTitle({
      title: `${isEdit ? '编辑' : '添加'}路线`
    });
  }


  componentDidMount() {
    this.setNavigationBarTitle();
  }

  handleLineEditFormChange = (data) => {
    const { disabled, loading, data: formData } = data;
    const [pointItem] = reformatLineEditForm(formData);
    this.setState({
      loading,
      disabled,
      vehicle_owner_id: pointItem?.vehicle_owner_id,
    });
  }

  handleSubmit = async () => {
    const res = await this.editRef.current.submit();
    const isSuccess = `${res.code}` === '0';
    if (isSuccess) {
      longListRefresherManager.trigger(lineListRefreshKey);
      Taro.navigator();
    }
  }

  onPostMessage = (key, data) => {
    switch (key) {
      case 'routerParamsChange':
        const { params } = data || {};
        this.setNavigationBarTitle(params);
        this.setState({
          defaultValue: {
            id: params?.id,
            name: params?.route_name,
            ...(formatLineEditForm(params?.route_item.map(item => ({
              vehicle_owner_id: params?.vehicle_owner_id,
              ...item,
            }))))
          }
        });

        break;

      default:
        break;
    }
  }

  render() {
    const { defaultValue, disabled, loading, vehicle_owner_id } = this.state;

    return (
      <KbPage
        safeAreaGhost={false}
        renderFooter={
          <View className='kb-spacing-md kb-background__white'>
            <AtButton
              type='primary'
              circle
              disabled={disabled}
              onClick={this.handleSubmit}
              loading={loading}
            >
              保存
            </AtButton>
          </View>
        }
      >
        <ScrollView className='kb-scrollview' scrollY>
          <LineEdit
            enableStartAsEnd
            enableEditName
            action='line-edit'
            onChange={this.handleLineEditFormChange}
            actionRef={this.editRef}
            request={updateLine}
            enableMore={false}
            defaultValue={defaultValue}
            params={{ vehicle_owner_id }}
            labels={{ start: '发车点', end: '返程点' }}
          />
        </ScrollView>
      </KbPage>
    );
  }
}

export default PageLineEdit;
