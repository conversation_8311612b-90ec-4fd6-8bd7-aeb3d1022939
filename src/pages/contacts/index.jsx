import Taro from "@tarojs/taro";
import KbPage from "~base/components/page";
import { Component, useRef } from "react";
import ContactsPage from "~/components/_pages/contacts/page";

class PageContacts extends Component {
    config = {
        navigationBarTitleText: '联系人',
    }

    constructor() {
        super();
    }

    render() {
        return (
            <KbPage layout={false} safeAreaGhost={false}>
                <ContactsPage />
            </KbPage>
        )
    }
}

export default PageContacts;