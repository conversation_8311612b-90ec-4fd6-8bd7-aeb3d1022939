/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtInput } from 'taro-ui';
import KbPage from '~base/components/page';
import { check } from '~base/utils/rules';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '联系电话',
  };

  constructor() {
    this.state = {
      data: null,
    };
  }

  onPostMessage = (key, params) => {
    if (key === 'routerParamsChange') {
      const { params: { data } = {} } = params || {};
      this.setState({
        data,
      });
    }
  };

  onChange = (v) => {
    this.setState({
      data: v,
    });
  };

  onConfirm = () => {
    const { data } = this.state;
    Taro.navigator({
      post: {
        type: 'contact_phone-edit',
        data,
      },
    });
  };

  handleChooseContact = () => {
    Taro.chooseContact({
      success: (res) => {
        this.setState({
          data: res.phoneNumber,
        });
      },
      fail: (err) => {
        console.log(err);
      },
    });
  };

  render() {
    const { data } = this.state;
    const disabled = check('phone', data).code != 0;
    return (
      <KbPage
        renderFooter={
          <View className='kb-spacing-md kb-background__white'>
            <AtButton
              circle
              type='primary'
              disabled={disabled}
              className='kb-button__base'
              onClick={this.onConfirm}
            >
              确定
            </AtButton>
          </View>
        }
      >
        <View className='kb-spacing-md'>
          <View className='kb-box kb-spacing-md-lr'>
            <View className='at-row at-row__align--center at-row__justify--between kb-border-b kb-spacing-md-tb'>
              <View className='kb-size__lg kb-text__bold'>联系电话</View>
              <AtButton
                circle
                size='small'
                className='kb-mobile__btn'
                onClick={this.handleChooseContact}
              >
                通讯录
              </AtButton>
            </View>
            <View>
              <AtInput
                className='kb-input__without-border kb-without__margin'
                cursor={-1}
                type='phone'
                placeholder='请输入'
                value={data}
                onChange={this.onChange}
              />
            </View>
          </View>
          <View className='kb-spacing-md-l kb-size__sm kb-color__orange'>
            会优先通过此号码联系您
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
