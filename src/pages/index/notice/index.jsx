/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbListBox from '@/components/_pages/order/listbox';
import { View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtTextarea } from 'taro-ui';
import KbPage from '~base/components/page';
import './index.scss';

class Index extends Component {
  config = {
    navigationBarTitleText: '订单备注',
  };

  constructor() {
    this.state = {
      data: '',
    };
    this.defaultNotice = [
      {
        label: '有大件物品',
        key: 1,
      },
      {
        label: '有易碎品',
        key: 2,
      },
      {
        label: '有冷链食品',
        key: 3,
      },
    ];
  }

  onPostMessage = (key, params) => {
    if (key === 'routerParamsChange') {
      const { params: { data } = {} } = params || {};
      this.setState({
        data,
      });
    }
  };

  onChange = (v) => {
    this.setState({
      data: v,
    });
  };

  onConfirm = () => {
    const { data } = this.state;
    Taro.navigator({
      post: {
        type: 'note-edit',
        data,
      },
    });
  };
  render() {
    const { data } = this.state;
    return (
      <KbPage
        renderFooter={
          <View className='kb-spacing-md kb-background__white'>
            <AtButton circle type='primary' className='kb-button__base' onClick={this.onConfirm}>
              确定
            </AtButton>
          </View>
        }
      >
        <View className='kb-spacing-md'>
          <View className='kb-box kb-spacing-lg'>
            <View className='kb-size__lg kb-text__bold'>快捷备注</View>
            <KbListBox
              className='kb-package__base kb-notice__list'
              selectedActive='ghost'
              list={this.defaultNotice}
              onChange={(v) => this.onChange(v.label)}
              selectted={data}
            />
          </View>
          <View className='kb-box kb-spacing-lg'>
            <View className='kb-size__lg kb-text__bold'>填写备注</View>
            <View className='kb-spacing-lg-t'>
              <AtTextarea
                value={data}
                className='kb-notice__textarea'
                onChange={(v) => this.onChange(v)}
                placeholder='请输入备注'
                maxLength={200}
              />
            </View>
          </View>
        </View>
      </KbPage>
    );
  }
}

export default Index;
