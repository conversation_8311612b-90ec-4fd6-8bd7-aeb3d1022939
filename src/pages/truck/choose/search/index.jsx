import Taro from '@tarojs/taro';
import { Component } from '@tarojs/components';
import TruckChooseList from '~/components/_pages/truck/choose/list';
import KbPage from '~base/components/page';
import './index.scss';

class TruckChooseSearchPage extends Component {
  config = {
    navigationBarTitleText: '搜索车辆',
  };
  constructor(props) {
    super(props);
  }

  render() {
    const { params } = this.$router;

    return (
      <KbPage
        closeIosSafeArea
      >
        <TruckChooseList search data={params} />
      </KbPage>
    );
  }
}

export default TruckChooseSearchPage;
