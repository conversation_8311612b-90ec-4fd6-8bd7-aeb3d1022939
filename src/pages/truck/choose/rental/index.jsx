/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Component } from '@tarojs/components';
import TruckChooseRentalList from '~/components/_pages/truck/choose/rental/list';
// import TruckChooseRentalTitle from '~/components/_pages/truck/choose/rental/title';
import KbPage from '~base/components/page';
import './index.scss';

class TruckChoosePage extends Component {
  config = {
    navigationBarTitleText: '选择车型',
    // navigationStyle: 'custom',
  };
  constructor(props) {
    super(props);
    this.state = {
      active: {},
      vehicle_owner_id: null,
    };
  }

  updateActive = (active) => {
    this.setState({ active });
  };

  onPostMessage = (key, data) => {
    const { params } = data;
    if (key === 'routerParamsChange') {
      const _data = params?.data || {};
      const {
        car_type,
        vehicle_type,
        goods_volume,

        rental_money,
        truckOwner: { vehicle_owner_id },
      } = _data;
      this.setState({
        active: {
          car_type,
          vehicle_type,
          goods_volume,

          rental_money,
        },
        vehicle_owner_id,
      });
    }
  };

  render() {
    const { active, vehicle_owner_id } = this.state;

    return (
      <KbPage
        className='kb-choose-rental-page'
        // navProps={{
        //   title: <TruckChooseRentalTitle active={active} updateActive={this.updateActive} />,
        // }}
      >
        <TruckChooseRentalList search={active} vehicle_owner_id={vehicle_owner_id} />
      </KbPage>
    );
  }
}

export default TruckChoosePage;
