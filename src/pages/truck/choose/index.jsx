import { Component, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtNoticebar } from 'taro-ui';
import { checkOrderIsRental } from '~/components/_pages/order/list/card/rental/bars/utils';
import KbQueryBar from '~/components/_pages/query-bar';
import TruckChooseList from '~/components/_pages/truck/choose/list';
import TruckCardType from '~/components/_pages/truck/list/item/card/type';
import KbPage from '~base/components/page';
import './index.scss';

class TruckChoosePage extends Component {
  config = {
    navigationBarTitleText: '',
  };
  constructor(props) {
    super(props);
    this.state = {};
  }

  componentDidMount() {
    const { type = '0' } = this.$router.params;
    const typeMap = { 0: '接单车辆', 1: '更换车辆' };

    Taro.setNavigationBarTitle({
      title: typeMap[type],
    });
  }

  handleSearch = () => {
    const { params } = this.$router;
    Taro.navigator({
      url: 'truck/choose/search',
      options: params,
    });
  };

  render() {
    const { params } = this.$router;
    const isRental = checkOrderIsRental(params);

    return (
      <KbPage
        renderHeader={
          <View>
            <View className='kb-spacing-md'>
              <KbQueryBar
                placeholder='请输入车辆信息'
                autoClean
                onClick={this.handleSearch}
                theme='white'
                readonly
              />
            </View>
            <AtNoticebar icon='amaze'>
              <Text className='kb-truck__tips'>
                货主{isRental ? '租赁车型' : '用车要求'}： <TruckCardType data={params} />
              </Text>
            </AtNoticebar>
          </View>
        }
        closeIosSafeArea
      >
        <TruckChooseList data={params} />
      </KbPage>
    );
  }
}

export default TruckChoosePage;
