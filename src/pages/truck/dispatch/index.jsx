import { <PERSON><PERSON>, <PERSON><PERSON>View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { Component } from "react";
import TruckDispatchGrid from "~/components/_pages/truck/dispatch/grid";
import TruckDispatchStatistics from "~/components/_pages/truck/dispatch/statistics";
import KbPage from "~base/components/page"
import { usePostMessage, useUpdate } from "~base/hooks/page";
import './index.scss';

class PageTruckDispatch extends Component {
    config = {
        navigationStyle: 'custom',
        navigationBarTitleText: ''
    }
    constructor() {
        super();
    }

    render() {
        return (
            <KbPage className='truck-dispatch'>
                <ScrollView scrollY className="kb-scrollview">
                    <TruckDispatchStatistics />
                    <TruckDispatchGrid />
                </ScrollView>
            </KbPage>
        )
    }
}


export default PageTruckDispatch;