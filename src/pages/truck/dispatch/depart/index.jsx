/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import KbPage from '~base/components/page';
import TruckDispatchDepartTabs from '~/components/_pages/truck/dispatch/depart/tabs';
import { formatLineEditForm } from '~/components/_pages/line/edit/_utils';

class PageTruckDispatchDepart extends Component {
    config = {
        navigationBarTitleText: '派车',
        navigationBarBackgroundColor: '#ffffff',
    };
    constructor() {
        this.state = {
            defaultValue: null
        }
    }

    componentDidMount() {
        const { vehicle_no } = this.$router.params;
        Taro.setNavigationBarTitle({
            title: `派车：${vehicle_no}`
        });
    }

    onPostMessage = (key, data) => {
        switch (key) {
            case 'routerParamsChange':
                const { task_items } = data?.params || {};
                const formData = formatLineEditForm(data?.params?.task_items);
                this.setState({
                    defaultValue: formData
                });
                break;

            default:
                break;
        }
    }

    render() {
        const { defaultValue } = this.state;
        const { params } = this.$router

        return (
            <KbPage safeAreaGhost={false}>
                <TruckDispatchDepartTabs defaultValue={defaultValue} params={params} />
            </KbPage>
        );
    }
}

export default PageTruckDispatchDepart;
