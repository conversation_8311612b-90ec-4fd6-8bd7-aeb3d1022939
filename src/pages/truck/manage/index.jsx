/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import KbPage from '~base/components/page';
import TruckManageList from '~/components/_pages/truck/manage';
import './index.scss';

class PageTruckManage extends Component {
  config = {
    navigationBarTitleText: '无人车管理',
  };


  render() {


    return (
      <KbPage >
        <TruckManageList />
      </KbPage>
    );
  }
}

export default PageTruckManage;
