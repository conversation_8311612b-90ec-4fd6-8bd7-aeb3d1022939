import { Component } from '@tarojs/taro';
import KbPage from '~base/components/page';
import TruckListItem from '~/components/_pages/truck/list/item';
import './index.scss';

class TruckDetailPage extends Component {
  config = {
    navigationBarTitleText: '车辆详情',
  };
  constructor(props) {
    super(props);
    this.state = {};
  }


  render() {
    const { vehicle_no } = this.$router.params;

    return (
      <KbPage>
        <TruckListItem isDetail active={{ vehicle_no }} />
      </KbPage>
    );
  }
}

export default TruckDetailPage;
