import { Component } from '@tarojs/components';
import TruckListItem from '~/components/_pages/truck/list/item';
import TruckBatch from '~/components/_pages/truck/list/item/batch';
import TruckSearch from '~/components/_pages/truck/list/item/search';
import KbPage from '~base/components/page';

class TruckSearchPage extends Component {
  config = {
    navigationBarTitleText: '搜索车辆',
  };
  constructor(props) {
    super(props);
    this.state = {
      active: { vehicle_no: '', }
    };
  }

  handleReady = ins => { this.listIns = ins; }

  handleRefresh = () => {
    this.listIns?.loader?.();
  }

  handleSearchChange = v => {
    this.setState({ active: { vehicle_no: v } });
  }

  render() {
    const { active } = this.state;

    return (
      <KbPage>
        <TruckSearch value={active.vehicle_no} onChange={this.handleSearchChange}>
          <TruckBatch onSuccess={this.handleRefresh}>
            <TruckListItem onReady={this.handleReady} active={active} />
          </TruckBatch>
        </TruckSearch>
      </KbPage>
    );
  }
}

export default TruckSearchPage;
