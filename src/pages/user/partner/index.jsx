import Taro, { Component } from '@tarojs/taro';
import UserPartnerList from '~/components/_pages/user/partner/list';
import { bindPartnerLabel } from '~/components/_pages/user/partner/_utils';
import KbPage from '~base/components/page';

export default class UserPartnerPage extends Component {
  config = {
    navigationBarTitleText: '',
    disableScroll: true,
  };
  constructor() {
    this.state = {};
  }

  setNavigationBarTitle = () => {
    const { action } = this.$router.params;
    Taro.setNavigationBarTitle({
      title: action === 'choose' ? '选择接单车主' : `合作${bindPartnerLabel}`,
    });
  };

  componentDidMount() {
    this.setNavigationBarTitle();
  }

  render() {
    const { action, orderUseType } = this.$router.params;

    return (
      <KbPage safeAreaGhost={false} layout={false}>
        <UserPartnerList action={action} orderUseType={orderUseType} />
      </KbPage>
    );
  }
}
