import { View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import KbPage from '~base/components/page'
import UserPartnerStopPointsList from '~/components/_pages/user/partner/stopPoints/list';
import { bindPartnerLabel } from '~/components/_pages/user/partner/_utils';

export default class UserPartnerStopPointsPage extends Component {
    config = {
        navigationBarTitleText: '停靠点',
    };
    constructor() {
        this.state = {
            defaultPoints: []
        }
    }

    componentDidMount() {
        this.setNavigationBarTitle();
    }

    setNavigationBarTitle = () => {
        const { name } = this.$router.params;
        Taro.setNavigationBarTitle({ title: name });
    }

    render() {
        const { params } = this.$router;
        return (
            <KbPage
                safeAreaGhost={false}
                layout={false}
            >
                <UserPartnerStopPointsList params={params} />
            </KbPage>
        );
    }
}