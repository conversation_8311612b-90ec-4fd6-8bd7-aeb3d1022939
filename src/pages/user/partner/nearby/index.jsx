import Taro, { Component } from '@tarojs/taro';
import UserPartnerList from '~/components/_pages/user/partner/list';
import Location from '~base/components/location';
import KbPage from '~base/components/page'

export default class UserPartnerNearbyPage extends Component {
    config = {
        navigationBarTitleText: '查询车主',
    };
    constructor() {
        this.state = {}
    }

    render() {

        return (
            <KbPage
                safeAreaGhost={false}
                layout={false}
            >
                <Location>
                    <UserPartnerList action='nearby' />
                </Location>
            </KbPage>
        );
    }
}