/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, Text, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtIcon, AtInput, AtNoticebar } from 'taro-ui';
import { getUserInfoDetail } from '~/services/user/info';
import KbPage from '~base/components/page';
import Popconfirm from '~base/components/popconfirm';
import Form from '~base/utils/form';
import './index.scss';

const maxMoney = 500;

class WalletCash extends Component {
  constructor() {
    this.state = {
      form: {
        loading: false,
        data: {
          amount: '',
        },
        disabled: true,
      },
    };
  }
  config = {
    navigationBarTitleText: '提现',
  };

  componentDidMount() {
    this.createForm();
  }

  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    return new Promise(() => {
      this.formIns = new Form(
        {
          form: {
            amount: { value: '' },
          },
          api: {
            url: '/g_autovd/v2/Vehicle/User/withdraw',
            onThen: (res) => {
              const isSuccess = `${res.code}` === '0';
              if (isSuccess) {
                getUserInfoDetail();
              }
            },
          },
        },
        this,
      );
    });
  };

  handleCashAll = () => {
    const { money = 0 } = this.$router.params;
    if (money > maxMoney) {
      Taro.kbToast({
        text: '单笔超过500',
      });
      return;
    }
    this.formIns?.update?.({ amount: money });
  };

  render() {
    const {
      form: { loading, data: formData, disabled },
    } = this.state;

    return (
      <KbPage
        renderHeader={
          <AtNoticebar icon='amaze'>每日可提现3次，单次微信500元</AtNoticebar>
        }
        className='wallet-cash-page'
      >
        <ScrollView className='kb-scrollview' scrollY>
          <View className='kb-box__group kb-spacing-md'>
            <View className='kb-box kb-spacing-md'>
              <View className='at-row at-row__align--center'>
                <View className='wallet-cash__icon'>
                  <AtIcon prefixClass='kb-icon' value='weapp' />
                </View>
                <View className='kb-spacing-md-l'>
                  <View>提现到微信</View>
                  {/* <View className='kb-size__base kb-color__grey'>xxx</View> */}
                </View>
              </View>
            </View>
            <View className='kb-box'>
              <AtInput
                value={formData.amount}
                onChange={this.onChange_form.bind(this, 'amount')}
                placeholder='请输入'
                type='digit'
                clear
                title='金额（元）'
              >
                <AtButton className='kb-button__link kb-margin-md-r' onClick={this.handleCashAll}>
                  全部提现
                </AtButton>
              </AtInput>
            </View>
            <Popconfirm
              content={
                <View>
                  是否确认，<Text className='kb-color__brand'>提现{formData.amount}元</Text>到微信？
                </View>
              }
              buttonProps={{ loading, disabled, className: 'kb-margin-md-lr' }}
              onConfirm={() => this.onSubmit_form()}
            >
              确认提现
            </Popconfirm>
          </View>
        </ScrollView>
      </KbPage>
    );
  }
}

export default WalletCash;
