/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtInput } from 'taro-ui';
import { bindPhoneLoginApi, getSmsCode } from '~/services/user/user';
import { resetByBindMobile } from '~/utils/qy';
import KbLoginAuthAndBind from '~base/components/login/authAndBind';
import SmsInput from '~base/components/sms-input';
import { utils } from '~base/config';
import request from '~base/utils/request';
import { check } from '~base/utils/rules';
import { serviceAddrCheck } from '../../_utils';
import './index.scss';

@connect(({ global }) => ({
  loginData: global.loginData,
}))
class Index extends Component {
  // eslint-disable-next-line react/sort-comp
  config = {
    navigationBarTitleText: '',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    backgroundColorTop: '#ffffff',
  };
  constructor() {
    this.state = {
      phone: '',
      timer: -1,
      smsCode: '',
    };
    this.length = 6;
  }

  onChange = (value, key) => {
    this.setState({
      [key]: value,
    });
  };

  handleClick = () => {
    const { phone, smsCode } = this.state;
    if (smsCode.length == this.length) {
      this.handleLogin();
      return;
    }

    const { code, msg } = check('phone', phone);
    if (code !== 0) {
      Taro.kbToast({
        text: msg,
      });
      return;
    }

    getSmsCode({ phone }).then(() => {
      Taro.kbToast({
        text: '手机验证码已经发送，请查看手机短信',
      });
      this.setState({
        timer: 60,
      });
      this.timer = setInterval(() => {
        this.setState({
          timer: this.state.timer - 1,
        });
        if (this.state.timer <= 0) {
          clearInterval(this.timer);
        }
      }, 1000);
    });
  };

  handleLogin = () => {
    const { phone, smsCode } = this.state;
    utils.login().then((res) => {
      const { code: j2code } = res;
      if (j2code) {
        request({
          url: bindPhoneLoginApi,
          data: {
            type: '2',
            j2code,
            phone,
            smsCode,
          },
          mastHasMobile: false,
          autoTriggerLoginModal: false,
          onThen: (result) => {
            const { code } = result || {};
            if (code > 0) {
              Taro.kbToast({
                text: result.msg,
              });
              return;
            }
            resetByBindMobile(result, null, () => {
              Taro.kbToast({
                text: '登录成功',
              });
              setTimeout(() => {
                serviceAddrCheck(true);
              }, 700);
            });
          },
        });
      }
    });
  };

  render() {
    const { phone, smsCode, timer, ...rest } = this.state;

    return (
      <KbPage closeIosSafeArea {...rest}>
        <View className='kb-smsLogin'>
          <View className='kb-smsLogin__title'>
            <View>{timer == -1 ? '短信验证码登录' : `输入${this.length}位验证码`}</View>
            {timer != -1 && (
              <View className='kb-size__base kb-color__grey kb-spacing-md-t'>
                已发送到 +86 {phone}
              </View>
            )}
          </View>
          {timer == -1 ? (
            <>
              <View className='at-row at-row__align--center kb-smsLogin__form'>
                <View className='kb-spacing-md-r'>+86</View>
                <View className='kb-smsLogin__form-input-wrapper'>
                  <AtInput
                    className='at-input--without-border kb-smsLogin__form-input'
                    cursor={-1}
                    type='phone'
                    placeholder='请输入手机号码'
                    value={phone}
                    onChange={(v) => this.onChange(v, 'phone')}
                  />
                </View>
              </View>
              <KbLoginAuthAndBind
                linked
                size='normal'
                useOpenType
                className='kb-smsLogin__phone kb-spacing-lg-t'
                onAuthComplete={() => serviceAddrCheck(true)}
              />
            </>
          ) : (
            <SmsInput onChange={(v) => this.onChange(v, 'smsCode')} length={this.length} />
          )}

          <AtButton
            circle
            type='primary'
            className='kb-smsLogin__btn'
            disabled={(!phone || timer > 0) && smsCode.length != this.length}
            onClick={this.handleClick}
          >
            {smsCode.length == this.length
              ? '登录'
              : timer > 0
              ? `重新获取(${timer})`
              : '获取验证码'}
          </AtButton>
        </View>
      </KbPage>
    );
  }
}

export default Index;
