/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { ScrollView, View } from '@tarojs/components';
import Taro, { Component } from '@tarojs/taro';
import { AtButton, AtTextarea } from 'taro-ui';
import Form from '~base/utils/form';
import './index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '意见反馈',
    navigationBarBackgroundColor: '#fff',
  };

  constructor() {
    this.state = {
      form: {
        disabled: true,
        loading: false,
        data: {
          content: '',
        },
      },
    };
  }

  componentDidMount() {
    this.createForm();
  }

  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = () => {
    this.formIns = new Form(
      {
        form: {
          content: { value: '', max: 300 },
        },
        api: {
          url: '/g_autovd/v2/Common/feedback',
        },
      },
      this,
    );
  };

  handleFeedbackRecord = () => {
    Taro.navigator({
      url: 'user/feedback/record',
    });
  };

  render() {
    const {
      form: { data: formData, loading, disabled },
      ...rest
    } = this.state;

    return (
      <KbPage
        safeAreaGhost={false}
        {...rest}
        renderFooter={
          <View className='user-feedback__btn at-row at-row__justify--end'>
            <AtButton
              type='secondary'
              circle
              onClick={this.handleFeedbackRecord}
              className='kb-user-feedback-btn'
            >
              反馈记录
            </AtButton>
            <AtButton
              disabled={disabled}
              loading={loading}
              type='primary'
              circle
              onClick={this.onSubmit_form}
              className='kb-user-feedback-btn'
            >
              发送反馈
            </AtButton>
          </View>
        }
        className='user-feedback-page'
      >
        <ScrollView scrollY className='kb-scrollview'>
          <View className='kb-spacing-lg'>
            <View className='kb-box kb-spacing-lg'>
              <AtTextarea
                value={formData.content}
                placeholder='请输入需要反馈的内容'
                maxLength={300}
                onChange={this.onChange_form.bind(this, 'content')}
                placeholderClass='kb-feedback-placeholder'
                className='kb-feedback-textarea'
              />
            </View>
          </View>
        </ScrollView>
      </KbPage>
    );
  }
}
