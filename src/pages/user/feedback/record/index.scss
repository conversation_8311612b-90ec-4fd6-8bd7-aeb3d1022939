/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-feedback-record {
  padding: 24px;
  overflow: hidden;

  &-item {
    box-sizing: border-box;
    margin-bottom: $spacing-v-md;
    padding: 32px 28px;
    background-color: $color-white;
    border-radius: $border-radius-xxl;

    &:last-child {
      margin-bottom: 0;
    }

    &__btn {
      box-sizing: border-box;
      padding-top: $spacing-v-md;
      .at-button {
        display: inline-block;
        box-sizing: border-box;
        width: 144px;
        height: 48px;
        margin: 0;
        line-height: 48px;
      }
    }
  }

  &-avatar {
    width: 80px;
    height: 80px;
    color: $color-white;
    font-size: $font-size-lg;
    line-height: 80px;
    text-align: center;
    background-color: $color-brand;
    border-radius: 50%;
  }

  .kb-flex-1 {
    flex: 1;
  }
}
