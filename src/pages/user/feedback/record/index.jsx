/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import { Component } from '@tarojs/taro';
import { createLongList } from '~base/components/long-list/hooks';
import Popconfirm from '~base/components/popconfirm';
import { requestPost } from '~base/utils/request/once';
import { dateCalendar } from '~base/utils/utils';
import './index.scss';

export default class Index extends Component {
  config = {
    navigationBarTitleText: '反馈记录',
    navigationBarBackgroundColor: '#fff',
  };

  constructor() {
    this.state = {
      list: [],
    };
    this.listData = createLongList('/g_autovd/v2/Common/feedbackList', (list) => {
      this.setState({ list });
    });
  }

  handleRemove = async (item) => {
    return await requestPost({
      url: '/g_autovd/v2/Common/delFeedback',
      data: { id: item.id },
      toastLoading: false,
    }).then((res) => {
      const isSuccess = `${res.code}` === '0';
      if (isSuccess) {
        this.listData.loader();
      }
      return isSuccess;
    });
  };

  handleNavigator = () => {
    // Taro.navigator({
    //   url: `user/feedback/detail?id=${item.id}`,
    // });
  };

  render() {
    const { list, ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <KbLongList enableMore data={this.listData}>
          <View className='kb-feedback-record'>
            {list.map((item) => (
              <View
                key={item.id}
                className='kb-feedback-record-item at-row at-row__align--center'
                hoverClass='kb-hover'
                onClick={this.handleNavigator.bind(this, item)}
              >
                <View className='kb-flex-1'>
                  <View className='kb-size__lg'>{item.content}</View>
                  <View className='kb-feedback-record-item__btn at-row at-row__align--center at-row__justify--between'>
                    <View className='kb-size__sm kb-color__grey'>
                      {dateCalendar(item.create_at, { timer: true })}
                    </View>
                    <Popconfirm
                      buttonProps={{
                        circle: true,
                        size: 'small',
                        type: '',
                      }}
                      onConfirm={() => this.handleRemove(item)}
                      title='是否确定删除？'
                    >
                      删除
                    </Popconfirm>
                  </View>
                </View>
              </View>
            ))}
          </View>
        </KbLongList>
      </KbPage>
    );
  }
}
