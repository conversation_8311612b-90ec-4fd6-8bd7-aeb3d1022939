/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { Component } from '@tarojs/taro';
import { ScrollView, View } from '@tarojs/components';
import UserInfoCard from '~/components/_pages/user/info/card';
import './index.scss';

class UserInfoPage extends Component {
  config = {
    navigationBarTitleText: '个人信息',
  };

  constructor() {
    this.state = {};
  }

  render() {
    return (
      <KbPage>
        <ScrollView className='kb-scrollview' scrollY>
          <View className='kb-spacing-lg'>
            <UserInfoCard />
          </View>
        </ScrollView>
      </KbPage>
    );
  }
}

export default UserInfoPage;
