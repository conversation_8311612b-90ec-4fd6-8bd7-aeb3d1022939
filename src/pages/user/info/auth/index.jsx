/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import Taro, { Component } from '@tarojs/taro';
import { ScrollView, View } from '@tarojs/components';
import UserInfoCard from '~/components/_pages/user/info/card';
import { AtButton, AtInput, AtNoticebar } from 'taro-ui';
import { authTypes, getCurrentAuthType } from '~/components/_pages/user/info/auth/_utils';
import classNames from 'classnames';
import Form from '~base/utils/form';
import { getUserInfoDetail } from '~/services/user/info';
import './index.scss';

class UserInfoAuthPage extends Component {
  config = {
    navigationBarTitleText: '身份认证',
  };

  constructor() {
    this.state = {
      current: '1',
      form: {
        data: {},
        loading: false,
        disabled: true
      }
    };
  }

  componentDidMount() {
    this.createForm();
  }

  // 切换表单项不同正则
  createFormCfg = (current = this.state.current) => {
    const { form: formLabels } = getCurrentAuthType(current);
    return {
      id_name: {
        tag: formLabels?.id_name
      },
      id_code: formLabels?.id_code_config,
    }
  }

  onChange_form = () => { }
  onSubmit_form = () => { }
  createForm = () => {
    this.formIns = new Form({
      form: this.createFormCfg(),
      checkBySubmit: true,
      api: {
        url: '/g_autovd/v2/Vehicle/User/idConfirm',
        formatRequest: (req) => {
          return {
            ...req,
            owner_type: this.state.current
          }
        },
        onThen: (res) => {
          if (`${res.code}` === '0') {
            getUserInfoDetail().then(() => {
              Taro.navigator();
            });
          }
        }
      },
    })
  }

  // 切换tab
  handleTabsChange = c => {
    this.formIns?.clean?.();
    this.formIns?.resetForm(this.createFormCfg(c));
    this.setState({ current: c })
  }

  render() {
    const { current, form: { data: formData, loading, disabled, errKeys } } = this.state;
    const { form: formLabels } = getCurrentAuthType(current);

    return (
      <KbPage
        renderHeader={
          <AtNoticebar icon='amaze'>
            当前身份信息内容全部完善后才能认证成功哦!
          </AtNoticebar>
        }
        renderFooter={
          <View className='kb-spacing-md kb-background__white'>
            <AtButton className='kb-margin-md-lr' onClick={this.onSubmit_form} loading={loading} disabled={disabled} circle type='primary'>确认</AtButton>
          </View>
        }
      >
        <ScrollView className='kb-scrollview' scrollY>
          <View className='kb-spacing-md'>
            <View className='kb-box user-info-auth__tabs'>
              <View className='tabs-bar'>
                {
                  authTypes.map(item => (
                    <View
                      key={item.key}
                      className={
                        classNames('tabs-bar__item', {
                          'tabs-bar__item-active': item.key === current
                        })
                      }
                      onClick={() => this.handleTabsChange(item.key)}
                      hoverClass='kb-hover'
                    >
                      {item.label}
                    </View>
                  ))
                }
              </View>
              <View className='tabs-form'>
                <View className='tabs-form__item'>
                  <View>
                    {formLabels?.id_name}
                  </View>
                  <View>
                    <AtInput border={false} clear cursor={-1} onChange={this.onChange_form.bind(this, 'id_name')} value={formData.id_name} placeholder={`请输入${formLabels?.id_name}`} />
                  </View>
                  <View className='kb-color__red kb-size__base'>{errKeys?.id_name?.errorMsg}</View>
                </View>
                <View className='tabs-form__item'>
                  <View>
                    {formLabels?.id_code}
                  </View>
                  <View>
                    <AtInput border={false} clear cursor={-1} maxLength={formLabels?.maxLength} type={formLabels?.id_code_type} onChange={this.onChange_form.bind(this, 'id_code')} value={formData.id_code} placeholder={`请输入${formLabels?.id_code}`} />
                  </View>
                  <View className='kb-color__red kb-size__base'>{errKeys?.id_code?.errorMsg}</View>
                </View>
              </View>
            </View>

            <View className='user-info-auth__tips'>
              <View className='tips-item'>说明</View>
              <View className='tips-item'>
                1. 身份选定后，不可更改，如需更改请联系平台客服；
              </View>
              <View className='tips-item'>
                2.身份认证通过后，才可接单。
              </View>
            </View>

          </View>
        </ScrollView>
      </KbPage>
    );
  }
}

export default UserInfoAuthPage;
