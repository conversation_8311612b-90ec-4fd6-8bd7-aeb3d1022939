.user-info-auth {

    &__tabs {
        .tabs {
            &-bar {
                display: flex;
                align-items: center;
                justify-content: space-between;
                height: 90px;
                line-height: 90px;
                text-align: center;
                font-weight: 400;
                position: relative;
                overflow: hidden;

                &::after {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: -50%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(to right, $color-white 0, #EFF7F9 50%, $color-white 100%);
                    z-index: 0;
                }


                &__item {
                    width: 50%;
                    color: #646566;
                    position: relative;
                    z-index: 1;

                    &:first-child {
                        background: linear-gradient(to right, $color-white, #EFF7F9);
                        border-radius: 0 0 1.5 * $border-radius-xxl 0;
                    }

                    &:last-child {
                        background: linear-gradient(to left, $color-white, #EFF7F9);
                        border-radius: 0 0 0 1.5 * $border-radius-xxl;
                    }

                    &-active {
                        color: $color-black-1;
                        background: $color-white !important;
                    }

                    &-active:first-child {
                        border-radius: 0 $border-radius-xxl 0 0;
                    }

                    &-active:last-child {
                        border-radius: $border-radius-xxl 0 0 0;
                    }
                }
            }

            &-form {
                padding: $spacing-v-md $spacing-h-md;

                &__item {
                    border-bottom: $width-base solid $color-border-light;
                    margin-bottom: $spacing-v-md;

                    &:last-child {
                        margin-bottom: 0;
                        border-bottom: 0;
                    }
                }

                .at-input {
                    margin-left: 0;
                }
            }
        }
    }

    &__tips {
        color: #646566;
        font-size: $font-size-base;
        padding-top: $spacing-v-lg;

        .tips {
            &-item {
                margin-bottom: $spacing-v-sm;

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
    }
}