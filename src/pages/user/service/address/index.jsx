/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { View } from '@tarojs/components';
import { Component } from '@tarojs/taro';
import ServerAddress from '~/components/_pages/address/server';
import './index.scss';

class Index extends Component {
  // eslint-disable-next-line react/sort-comp
  config = {
    navigationBarTitleText: '服务地址',
  };
  constructor() {
    this.state = {};
  }

  render() {
    const { address, ...rest } = this.state;

    return (
      <KbPage {...rest}>
        <View className='kb-spacing-md'>
          <ServerAddress page='login' />
        </View>
      </KbPage>
    );
  }
}

export default Index;
