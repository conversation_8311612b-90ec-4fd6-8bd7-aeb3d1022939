/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCheckbox from '@base/components/checkbox';
import KbSwitch from '@base/components/switch';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useMemo } from 'react';
import { AtButton, AtInput } from 'taro-ui';
import { useForm } from '~base/hooks/form';
import { createListener } from '~base/utils/utils';
import { clientAddressForm } from '../../index/client/address/utils';
import './index.scss';

const AddressEdit = (props) => {
  const { data: propsData, onChange } = props;

  const formConfig = useMemo(() => {
    const form = clientAddressForm.reduce((acc, item) => {
      acc[item] = {
        value: '',
        required: true,
      };
      return acc;
    }, {});
    return {
      form: {
        ...form,
        need_notice: {
          value: true,
          required: false,
        },
        save: {
          value: true,
          required: false,
        },
      },
    };
  }, []);
  const {
    data: { data = {} },
    setFieldsValue,
  } = useForm(formConfig);

  useEffect(() => {
    propsData && setFieldsValue(propsData);
  }, [propsData]);

  const handleOpenContact = () => {
    Taro.chooseContact({
      success: (res) => {
        setFieldsValue({
          contact_name: res.displayName,
          contact_phone: res.phoneNumber,
        });
      },
      fail: (err) => {
        console.log(err);
      },
    });
  };

  const handleChangeLocation = () => {
    createListener('locationChange', (value) => {
      setFieldsValue({
        ...value,
      });
    });
    Taro.navigator({
      url: 'address/choose',
      options: {
        page: 'addressEdit',
      },
    });
  };

  const handleChange = (v) => {
    setFieldsValue(v);
  };

  const handleClear = () => {
    const values = clientAddressForm.reduce((acc, item) => {
      acc[item] = '';
      return acc;
    }, {});
    setFieldsValue(values);
  };

  useEffect(() => {
    data && onChange(data);
  }, [data]);

  return (
    <>
      <View className='kb-spacing-md-b'>
        <View className='kb-box kb-spacing-lg'>
          <View className='at-row at-row__align--center at-row__justify--between'>
            {data.landmark ? (
              <View>
                <View className='kb-size__lg kb-text__bold'>{data.landmark}</View>
                <View className='kb-color__greyer kb-size__sm'>
                  {data.province}
                  {data.city}
                  {data.district}
                  {data.address}
                </View>
              </View>
            ) : (
              <View className='kb-size__lg'>请选择地址</View>
            )}
            <AtButton circle size='small' className='kb-address_btn' onClick={handleChangeLocation}>
              {data.landmark ? '更换地址' : '添加地址'}
            </AtButton>
          </View>
        </View>
      </View>
      <View className='kb-box kb-spacing-lg'>
        <View className='at-row at-row__align--center at-row__justify--between'>
          <View className='kb-size__lg kb-text__bold'>联系人</View>
          <AtButton
            circle
            size='small'
            className='kb-address_btn kb-address_btn-sm'
            onClick={handleOpenContact}
          >
            通讯录
          </AtButton>
        </View>
        <AtInput
          value={data.contact_name}
          onChange={(v) => handleChange({ contact_name: v })}
          placeholder='请输入姓名'
          className='kb-addressEdit_input'
        />
        <AtInput
          value={data.contact_phone}
          onChange={(v) => handleChange({ contact_phone: v })}
          placeholder='请输入手机号'
          type='phone'
          className='kb-addressEdit_input'
        />
        {propsData.pageSource == 'index' && (
          <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-t'>
            <View>
              <KbSwitch
                className='kb-addressEdit_switch'
                checked={data.need_notice}
                title='车子到站后短信通知联系人'
                color='#12b8bf'
                onChange={(v) => handleChange({ need_notice: v })}
              />
            </View>
            <View
              className='kb-size__sm kb-color__grey'
              hoverClass='kb-hover-opacity'
              onClick={handleClear}
            >
              清空
            </View>
          </View>
        )}
      </View>
      {propsData.pageSource == 'index' && (
        <KbCheckbox
          labelClassName='kb-size__sm kb-color__grey'
          className='kb-spacing-md'
          checked={data.save}
          label='保存至地址簿'
          size='small'
          onChange={(v) => handleChange({ save: v })}
        />
      )}
    </>
  );
};

AddressEdit.options = {
  addGlobalClass: true,
};

export default AddressEdit;
