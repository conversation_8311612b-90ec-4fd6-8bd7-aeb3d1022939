/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-address_btn {
  height: 48px;
  margin: 0;
  padding: 0 24px;
  color: $color-grey-0;
  font-size: $font-size-sm;
  line-height: 48px;
  border-color: $color-grey-0;
  &-sm {
    height: 40px;
    line-height: 40px;
  }
}

.kb-addressEdit {
  &_input {
    margin-left: 0;
  }
  &_switch {
    display: flex;
    flex-direction: row-reverse;
    margin-left: -16px;
    padding: 0;
    color: $color-grey-1;
    .at-switch__title {
      color: $color-grey-1;
      font-size: $font-size-sm;
    }
    .at-switch__switch {
      margin-right: 0;
    }
    .at-switch__container {
      flex: 1;
    }
  }
}
