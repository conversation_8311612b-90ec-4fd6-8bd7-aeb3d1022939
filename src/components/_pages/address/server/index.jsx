/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { getUserInfoDetail } from '~/services/user/info';
import { createListener } from '~base/utils/utils';
import './index.scss';

const ServerAddress = (props) => {
  const { data: addressProps, page } = props;
  const [address, setAddress] = useState(null);

  const triggerSetAddress = (data) => {
    const { city, landmark } = data;
    setAddress(
      city
        ? {
            city,
            landmark,
          }
        : null,
    );
  };

  const handleChooseAddress = (data) => triggerSetAddress(data);

  const handleNavigator = () => {
    createListener('locationChange', handleChooseAddress);
    Taro.navigator({
      url: 'address/choose',
      options: {
        page,
      },
    });
  };

  useEffect(() => {
    if (addressProps) {
      triggerSetAddress(addressProps);
    } else {
      getUserInfoDetail().then(triggerSetAddress);
    }
  }, [addressProps]);

  return (
    <>
      <View className='kb-box service-address__info'>
        <View className='kb-navigator' hoverClass='kb-hover' onClick={handleNavigator}>
          <View className='kb-navigator__label'>服务地址</View>
          <Text className='kb-navigator__value kb-color__greyer'>请选择</Text>
        </View>
        <View className='kb-navigator kb-navigator-noarrow'>
          <View
            className={classNames({
              'kb-navigator__placeholder': !address,
            })}
          >
            {address ? (
              <>
                <View>所属区域： {address.city}</View>
                <View className='at-row at-row__align--center kb-spacing-xs-t'>
                  <View className='kb-icon kb-icon-location kb-size__xl kb-color__brand kb-spacing-xs-r service-address' />
                  <View className='at-col kb-color__greyer kb-text__ellipsis'>
                    {address.landmark}
                  </View>
                </View>
              </>
            ) : (
              '暂无地址'
            )}
          </View>
        </View>
      </View>
    </>
  );
};

ServerAddress.options = {
  addGlobalClass: true,
};
export default ServerAddress;
