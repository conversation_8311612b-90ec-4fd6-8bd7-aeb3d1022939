/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { searchAddressApi } from '~/services/user/address';
import KbLongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';

const AddressChooseList = ({ active, onClick }) => {
  const { list, config } = useLongList(searchAddressApi, {
    api: {
      pageKey: 'page',
    },
  });

  return (
    <KbLongList data={config} active={active} enableMore>
      {list.map((item) => (
        <View
          key={item.id}
          className='kb-spacing-xl-lr'
          hoverClass='kb-hover'
          onClick={() => onClick(item)}
        >
          <View className='kb-spacing-xl-tb kb-border-b' hoverClass='kb-hover'>
            <View className='kb-size__base2 kb-color__black'>{item.name}</View>
            <View className='kb-size__sm kb-color__grey kb-spacing-sm-t'>
              {item.city}
              {item.address}
            </View>
          </View>
        </View>
      ))}
    </KbLongList>
  );
};

AddressChooseList.options = {
  addGlobalClass: true,
};
export default AddressChooseList;
