/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useRef } from 'react';
import { addressListApi, deleteAddress } from '~/services/user/address';
import KbLongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import { useDidShowCom } from '~base/hooks/page';
import { clientAddressEditKey } from '../../index/client/address/utils';
import './list.scss'

const AddressList = ({ pageSource }) => {
  const listRef = useRef();
  const { list, config } = useLongList(addressListApi, {
    api: {
      pageKey: 'page',
      formatResponse: (res) => {
        const { data: { list } = {} } = res || {};
        if (Array.isArray(list) && list.length) {
          return {
            data: {
              list: list.map((item) => ({
                ...item,
                contact_name: item.name,
                contact_phone: item.phone,
              })),
            },
          };
        }
        return {
          data: void 0,
        };
      },
    },
  });

  const onReady = (ins) => {
    listRef.current = ins;
  };

  const handleRemove = (e, id) => {
    e.stopPropagation();
    Taro.kbModal({
      title: '提示',
      closable: false,
      content: [{ className: 'kb-text__center', text: '是否删除地址？' }],
      confirmText: '确定',
      cancelText: '取消',
      onConfirm: () => {
        deleteAddress({ id }).then((res) => {
          if (!res) return;
          Taro.kbToast({
            text: '删除成功',
          });
          listRef.current?.loader();
        });
      },
    });
  };

  const handleEdit = (e, item) => {
    e.stopPropagation();
    Taro.navigator({
      url: 'address/edit',
      key: 'routerParamsChange',
      options: {
        data: {
          ...item,
          pageSource: 'address',
        },
      },
    });
  };

  const handleChoose = (item) => {
    if (pageSource == 'user') return;
    Taro.navigator({
      post: {
        type: clientAddressEditKey,
        data: item,
      },
    });
  };

  useDidShowCom(() => {
    listRef.current?.loader();
  });

  return (
    <KbLongList data={config} enableMore onReady={onReady} noMoreText='拉到底啦'>
      <View className='kb-spacing-lg'>
        {list.map((item) => (
          <View
            key={item.id}
            className='kb-box kb-spacing-xl2-lr kb-spacing-lg-t'
            hoverClass={pageSource == 'user' ? '' : 'kb-hover'}
            onClick={() => handleChoose(item)}
          >
            <View className='kb-size__base2 kb-address-item-title kb-spacing-sm-b'>{item.landmark}</View>
            <View className='kb-color__greyer kb-size__sm kb-spacing-sm-b'>
              {item.province} {item.city} {item.district} {item.address}
            </View>
            <View className='kb-color__greyer kb-size__sm kb-spacing-sm-b'>
              {item.contact_name} {item.contact_phone}
            </View>
            <View className='kb-border-t at-row at-row__align--center'>
              <View
                className='kb-spacing-md at-col kb-border-r kb-size__sm kb-text__center kb-color__greyer'
                hoverClass='kb-hover-opacity'
                hoverStopPropagation
                onClick={(e) => handleRemove(e, item.id)}
              >
                删除
              </View>
              <View
                className='kb-spacing-md at-col kb-size__sm kb-text__center kb-color__greyer'
                hoverClass='kb-hover-opacity'
                hoverStopPropagation
                onClick={(e) => handleEdit(e, item)}
              >
                编辑
              </View>
            </View>
          </View>
        ))}
      </View>
    </KbLongList>
  );
};

AddressList.options = {
  addGlobalClass: true,
};
export default AddressList;
