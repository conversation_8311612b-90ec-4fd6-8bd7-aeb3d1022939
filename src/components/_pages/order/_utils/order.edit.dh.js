/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { formatRelationInfo } from '@/components/_pages/order/_utils/courier.detail';
import { getRelationConfig } from '@/components/_pages/order/_utils/order.relation';
import request from '@base/utils/request';
import { debounce } from '@base/utils/utils';
import Taro, { getStorage } from '@tarojs/taro';
import merge from 'lodash/merge';
import { requestYjQuotation } from '../edit/dh/brand-info/_utils';
import { createDHServiceDesc } from '../edit/dh/service/_utils/desc';

/**
 * 获取品牌动态的配置，往往是根据地址等信息预下单，获取是否支持下单、下单有什么特殊要求等
 * @param {*} reqData
 */
export const getBrandOrderFrom = (reqData = {}) => {
  return new Promise((resolve) => {
    const loader = debounce(
      () => {
        request({
          url: '/g_wkd/v2/Yj/Order/channelDecisionCondition',
          data: reqData,
          toastLoading: false,
          onThen: (res) => {
            const data =
              res.code == 0 && res.data && res.data.length > 0
                ? {
                    available: 1,
                    ...res.data[0],
                  }
                : {
                    available: 0,
                    unavailable_msg: '暂不支持该地址的收寄服务，请选择其它快递品牌下单，谢谢支持',
                  };
            const {
              need_volume,
              service_info_List,
              min_weight = 30,
              max_weight = 0,
              package_num_limit = 0,
            } = data || {};
            const floor =
              service_info_List &&
              service_info_List.find(
                (item) =>
                  (item.service_code === 'upstairsFee' || item.service_code === 'pickup_way') &&
                  item.is_show,
              );
            // 暂时写死仅百世快运支持动态配置
            if (data && reqData.brand === 'htky') {
              data.dynamicForms = {
                weightLimitMin: min_weight && min_weight * 1 > 0 ? min_weight * 1 : 30,
                weightLimitMax: max_weight * 1 || 0,
                package_num_limit,
                volume: {
                  isShow: true,
                  defaultExpand: !!need_volume,
                },
                floor: {
                  isShow: !!floor,
                },
              };
            }
            resolve(data);
          },
        });
      },
      300,
      { leading: false, trailing: true },
    );
    loader();
  });
};

export const serviceIntroductionMap = [
  {
    label: '包装费',
    value: 'bzf',
    url: 'https://m.kuaidihelp.com/f/packingFee',
  },
  {
    label: '回单费',
    value: 'back_sign_bill',
  },
  {
    label: '上楼费',
    value: 'pickup_way',
  },
  {
    label: '取货爬楼费',
    value: 'pickup_way',
  },
  {
    label: '派货爬楼费',
    value: 'pickup_way',
  },
  {
    label: '特殊区域费用',
    value: 'teshuquyu_fee',
  },
  {
    label: '更改单手续费',
    value: 'ggdsxf',
  },
  {
    label: '拆托服务费',
    value: 'ctfwf',
  },
  {
    label: '收配送区域加收费',
    value: 'spsqyjsf',
  },
  {
    label: '超长费',
    value: 'ccf',
  },
  {
    label: '超重费',
    value: 'czf',
  },
  {
    label: '超区费',
    value: 'cqf',
  },
  {
    label: '拆木架费',
    value: 'cmjf',
  },
  {
    label: '多次派送费',
    value: 'dcpsf',
  },
  {
    label: '等通知派送服务费',
    value: 'dtzpsfwf',
  },
  {
    label: '等通知服务费',
    value: 'dtzfwf',
  },
  {
    label: '修改地址费',
    value: 'xgdzf',
  },
  {
    label: '修改服务费',
    value: 'xgfwf',
  },
  {
    label: '空跑费',
    value: 'kpf',
  },
];

export const getIntroductionConfig = (name, brand) => {
  const item = serviceIntroductionMap.find((i) => i.label.includes(name) || name.includes(i.label));
  if (item && item.label) {
    if (item.url) {
      let url = item.url;
      if (item.label.includes('包装费')) {
        url = `${item.url}${item.url.includes('?') ? '&' : '?'}brand=${brand}`;
      }
      return {
        ...item,
        url,
      };
    }
    // 特殊逻辑
    if (brand == 'htky' && ['pickup_way', 'teshuquyu_fee'].includes(item.value)) {
      return item;
    }
    // 配置的服务介绍存在
    const oDHServiceDesc = createDHServiceDesc({ brand }) || {};
    const arr = oDHServiceDesc[item.value] || [];
    if (arr && arr.length > 0) {
      return item;
    }
  }
  return null;
};

export const formatServiceData = (quotationData) => {
  const { service_price = [] } = quotationData || {};
  const _service_price = [...(service_price || [])];
  // 揽收超区费
  const lscqFeeIndex = _service_price.findIndex((i) => i.service_code === 'lanshouchaoqu');
  let lscqItem = {};
  if (lscqFeeIndex > -1) {
    lscqItem = _service_price[lscqFeeIndex];
  }
  // 派送超区费
  const pscqFeeIndex = _service_price.findIndex((i) => i.service_code === 'cqf_fee');
  let pscqItem = {};
  if (pscqFeeIndex > -1) {
    pscqItem = _service_price[pscqFeeIndex];
  }

  // 保价费
  const insuranceFeeIndex = _service_price.findIndex((i) => i.service_code === 'insuranceFee');
  let insuranceItem = {};
  if (insuranceFeeIndex > -1) {
    insuranceItem = _service_price[insuranceFeeIndex];
  }

  // 增值服务总计
  let service_total_fee = 0;
  if (_service_price && _service_price.length > 0) {
    _service_price.map((item) => {
      service_total_fee = service_total_fee + item.amount * 1 || 0;
    });
    service_total_fee = service_total_fee.toFixed(2);
  }

  return {
    lscqItem,
    pscqItem,
    insuranceItem,
    service_price: _service_price,
    service_total_fee,
  };
};

export const getDHRelationConfig = (data) => {
  return new Promise((resolve) => {
    const { dynamicForms = {} } = data || {};
    let relationInfo = formatRelationInfo(data);
    getRelationConfig(relationInfo, { isDH: true }).then((v) => {
      const { dynamicForms: dynamicForms2 = {} } = v || {};
      v.available = data && data.available;
      v.dynamicForms = merge({}, dynamicForms2, dynamicForms);
      resolve(v);
    });
  });
};

export const getProPrice = (opt = {}) => {
  const { brand } = opt || {};
  return new Promise((resolve) => {
    const loader = debounce(
      () => {
        requestYjQuotation(opt, {
          toastLoading: true,
          quickTriggerThen: true,
        }).then(({ data }) => {
          if (data && data.quotation && data.quotation.length > 0) {
            const quotationData = data.quotation.find((i) => i.brand === brand);
            const { insuranceItem } = formatServiceData(quotationData);
            const val = (insuranceItem && insuranceItem.amount * 1) || 0;
            if (quotationData.unavailable_msg) {
              Taro.kbToast({
                text: quotationData.unavailable_msg,
              });
            } else if (val <= 0) {
              Taro.kbToast({
                text: '暂未获得保价金额',
              });
            }
            const proPrice = (insuranceItem && insuranceItem.amount * 1) || 0;
            resolve(proPrice);
          } else {
            resolve(0);
          }
        });
      },
      1500,
      { leading: true, trailing: true },
    );
    loader();
  });
};

export const getDefaultExtraInfo = () => {
  return new Promise((resolve) => {
    getStorage({
      key: 'extraInfo',
    })
      .then((res) => {
        let extraInfo = (res && res.data && res.data.data) || {};
        resolve(extraInfo);
      })
      .catch(() => {
        resolve({});
      });
  });
};
