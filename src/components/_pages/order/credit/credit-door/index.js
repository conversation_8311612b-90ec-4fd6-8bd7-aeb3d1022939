/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCreditLabel from '@/components/_pages/order/credit/credit-label';
import { isSpringFestival } from '@/components/_pages/order/_utils';
import { openCreditService } from '@/components/_pages/order/_utils/order.credit-pay';
import { Text, View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';

function Index(props) {
  const { open = false, msg = '', showOpenDoor = true } = props;
  const handleOpenCredit = () => {
    openCreditService();
  };
  let msgContent = msg || `享${isSpringFestival ? '春节寄件不打烊' : '专属优惠价'}`;
  return (
    <View>
      {open ? (
        <Fragment>
          <View className='at-row at-row__justify--center at-row__align--center kb-size__sm'>
            <KbCreditLabel mode='mini' msg='微信支付分|信用优享' />
          </View>
        </Fragment>
      ) : (
        <View
          className='at-row at-row__justify--center at-row__align--center kb-size__sm'
          onClick={handleOpenCredit}
          hoverClass='kb-hover-opacity'
        >
          开通“
          {process.env.PLATFORM_ENV == 'alipay' ? (
            <KbCreditLabel mode='mini' logo />
          ) : (
            <Fragment>
              <AtIcon
                prefixClass='kb-icon'
                value='wxpayflag'
                className='kb-icon-size__base kb-color__green kb-margin-xs-b'
              />
              微信支付分
            </Fragment>
          )}
          ”{msgContent}
          {showOpenDoor && <Text className='kb-color__brand kb-margin-md-l'>去开通&gt;</Text>}
        </View>
      )}
    </View>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
