/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import classNames from 'classnames';
import './index.scss';

function Index(props) {
  const { msg, show = 'auto', mode = 'normal', logo } = props || {};
  const showAlipay = (show == 'auto' || show == 'alipay') && process.env.PLATFORM_ENV == 'alipay';
  const showWeapp = (show == 'auto' || show == 'weapp') && process.env.PLATFORM_ENV == 'weapp';
  const zhimaRootCls = classNames('zhima-label', {
    'zhima-label-mini': mode == 'mini',
    'zhima-label-mini__top': logo,
  });
  return (
    <Fragment>
      {showAlipay ? (
        <View className={zhimaRootCls}>
          <View className='zhima-label__tag'>
            <AtIcon
              prefixClass='kb-icon'
              value='zhima'
              size={mode == 'mini' ? 14 : 16}
              className='kb-color__white'
            />
            芝麻先享｜先寄后付
          </View>
          {!logo && <View className='kb-margin-sm-l'>0元下单，寄出再付款</View>}
        </View>
      ) : showWeapp ? (
        <View className='kb-credit-label'>
          <AtIcon
            prefixClass='kb-icon'
            value='wxpayflag'
            className='kb-icon-size__base kb-color__green'
          />
          <Text className='kb-icon__text--ml'>{msg || '微信支付分|450分及以上信用优享'}</Text>
        </View>
      ) : null}
    </Fragment>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
