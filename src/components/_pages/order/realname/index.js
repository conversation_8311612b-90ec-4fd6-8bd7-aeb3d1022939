/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbDownContainer from '@base/components/down-container';
import KbNoticeBar from '@base/components/notice-bar';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment, useCallback, useEffect, useState } from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import { debounce, createListener } from '@base/utils/utils';
import { useUpdate, useDidShowCom } from '@base/hooks/page';
import { getRealnameStatus } from '@/components/_pages/order/_utils';
import { KB_SEND_REALNAMES } from '@/constants/realname';
import './index.scss';

function Index(props) {
  const {
    phone,
    actionRef,
    onRealnameChange = () => {},
    mode = 'normal',
    didShowCheck = false,
  } = props;
  const [isShow, setIsShow] = useState(false);
  //触发更新
  const triggerUpdate = (param, checkPhone) => {
    setIsShow(param);
    onRealnameChange(param, checkPhone);
  };
  useDidShowCom(() => {
    if (didShowCheck) {
      checkRealname(phone);
    }
  });
  // 检测实名状态，useCallback包裹才能使debounce生效
  const checkRealname = useCallback(
    debounce(
      (checkPhone) => {
        const storageRealname = Taro.kbGetGlobalData(KB_SEND_REALNAMES) || [];
        if (storageRealname.includes(checkPhone)) {
          triggerUpdate(!storageRealname);
          return;
        }
        getRealnameStatus(checkPhone)
          .then(({ realnamed }) => {
            if (realnamed && checkPhone) {
              storageRealname.push(checkPhone);
              Taro.kbSetGlobalData(KB_SEND_REALNAMES, storageRealname);
            }
            triggerUpdate(!realnamed, checkPhone);
          })
          .catch(() => {
            triggerUpdate(false);
          });
      },
      300,
      {
        trailing: true,
      },
    ),
    [],
  );
  // 跳转实名认证页面
  const onRealname = () => {
    createListener('realnameBack', () => checkRealname(phone));
    Taro.navigator({
      url: 'realname',
      options: {
        action: 'realname',
        phone,
      },
    });
  };
  // 强制实名时,拦截未实名订单
  const interceptRealname = ({ describe, upload }) => {
    Taro.kbModal({
      content: describe,
      confirmText: upload ? '上传证件照' : '去实名',
      onConfirm: () => {
        onRealname();
      },
    });
  };
  useEffect(() => {
    if (!actionRef) return;
    actionRef.current = {
      interceptRealname,
      checkRealname,
    };
  }, [phone]);

  useUpdate(
    (loginData) => {
      const { logined } = loginData;
      if (!logined) return;
      checkRealname(phone);
    },
    [phone],
  );

  return (
    <Fragment>
      {mode === 'normal' ? (
        <KbDownContainer isOpened={isShow}>
          <KbNoticeBar
            renderMore={
              <View
                className='kb-noticebar__bar'
                hoverClass='kb-hover-opacity'
                onClick={onRealname}
              >
                去实名
              </View>
            }
          >
            <View className='at-row at-row__align--center at-row__justify--between'>
              <View>
                <AtIcon
                  prefixClass='kb-icon'
                  value='help'
                  className='kb-icon-size__base kb-color__red'
                />
                <Text className='kb-icon__text--ml'>根据国家邮政局规定，寄件需实名登记</Text>
              </View>
            </View>
          </KbNoticeBar>
        </KbDownContainer>
      ) : (
        <Fragment>
          {isShow ? <Text className='kb-color__white kb-realname-no'>未实名</Text> : ''}
        </Fragment>
      )}
    </Fragment>
  );
}

Index.defaultProps = {};

Index.options = {
  addGlobalClass: true,
};

export default Index;
