/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getBrandOrderFrom } from '@/components/_pages/order/_utils/order.edit.dh';
import { getDHBrandList } from '@/components/_pages/store-card/_utils';
import apis from '@/utils/apis';
import { useUpdate } from '@base/hooks/page';
import request from '@base/utils/request';
import { debounce, isFullData } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import Taro, { useCallback, useEffect, useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import { transferWkdAddress } from '../../../../_utils';
import { formatAddAddValueService } from '../../addValueService/_utils';

export const getDhQuotationItem = (brand, list) => {
  if (isArray(list) && list.length > 0) {
    return list.find((i) => i.brand == brand) || {};
  }
  return {};
};

export const requestYjQuotation = (params = {}, opt = {}) => {
  return new Promise((resolve) => {
    const { toastLoading = false, quickTriggerThen } = opt;
    const {
      addrData,
      weight,
      volume,
      back_sign_bill,
      package_service,
      pickup_way,
      floor,
      decVal,
      brand,
      delivery_type,
    } = params;
    let vol = {};
    if (volume && volume.checked) {
      vol = volume;
    }
    request({
      url: apis['order.quotation.yj'],
      data: {
        brand,
        ...addrData,
        weight,
        ...vol,
        decVal,
        activity: 'big_package',
        other_service: {
          ...vol,
          back_sign_bill,
          package_service,
          pickup_way,
          floor,
        },
        delivery_type,
      },
      toastLoading,
      quickTriggerThen,
      onThen: async (res, req) => {
        console.log('请求报价单服务端数据===>', res, req);
        resolve(res, req);
      },
    });
  });
};

export const DHBrandDescMap = {
  ky: {
    同城次日: [
      {
        className: 'kb-color__greyer kb-size__base2',
        text: '服务介绍：同一城市内，按小时精准承诺时效，不超过24小时送达的快时效运输服务。',
      },
      {
        className: 'kb-color__greyer kb-size__base2 kb-margin-md-t',
        text: '服务时效：最快16小时，不超过24小时',
      },
      {
        className: 'kb-text__center kb-color__red kb-size__sm kb-margin-md-t',
        text: '注：对于收件地为偏远地区，需要额外的中转时间，时效需增加 0.5-2个工作日',
      },
    ],
    省内次日: [
      {
        className: 'kb-color__greyer kb-size__base2',
        text: '服务介绍：同一省份内互寄及经济圈内互寄（江浙沪皖、京津冀、成渝），按小时精准承诺时效，不超过24小时送达的快时效运输服务。',
      },
      {
        className: 'kb-color__greyer kb-size__base2 kb-margin-md-t',
        text: '服务时效：最快16小时，不超过24小时',
      },
      {
        className: 'kb-text__center kb-color__red kb-size__sm kb-margin-md-t',
        text: '注：对于收件地为偏远地区，需要额外的中转时间，时效需增加 0.5-2个工作日',
      },
    ],
    隔日达: [
      {
        className: 'kb-color__greyer kb-size__base2',
        text: '服务介绍：今发后至，最快只需36小时（跨省运输），满足您低成本时效较快的运输服务。',
      },
      {
        className: 'kb-color__greyer kb-size__base2 kb-margin-md-t',
        text: '服务时效：最快36小时，不超过48小时',
      },
      {
        className: 'kb-text__center kb-color__red kb-size__sm kb-margin-md-t',
        text: '注：对于收件地为偏远地区，需要额外的中转时间，时效需增加 0.5-2个工作日',
      },
    ],
    陆运件: [
      {
        className: 'kb-color__greyer kb-size__base2',
        text: '服务介绍：为您提供高性价比、定制一对一服务的跨省汽运的运输服务。',
      },
      {
        className: 'kb-color__greyer kb-size__base2 kb-margin-md-t',
        text: '服务时效：3-4天',
      },
      {
        className: 'kb-text__center kb-color__red kb-size__sm kb-margin-md-t',
        text: '注：对于收件地为偏远地区，需要额外的中转时间，时效需增加 0.5-2个工作日',
      },
    ],
  },
};

export const useDHBrandInfo = (props) => {
  const {
    data: propsData,
    relationInfo,
    address,
    extraInfo,
    weight,
    volume,
    onChange,
    onQuotationChange,
  } = props;
  const { brand: propsBrand, delivery_type: propsDeliveryType } = propsData || {};
  const { brand, delivery_type } = relationInfo || {};
  const { service } = extraInfo || {};
  const { back_sign_bill, package_service, pickup_way, floor } = service || {};

  const brands = useSelector(({ global }) => global.brands);
  const actionRefCurrent = useRef({});
  const [loading, setLoading] = useState(false);
  const [quotationList, setQuotationList] = useState([]);
  const [scrollViewId, setScrollViewId] = useState('');
  const [openBrandChooseTips, setOpenBrandChooseTips] = useState(false);

  const triggerUpdate = (list = []) => {
    actionRefCurrent.current.count = 0;
    actionRefCurrent.current.hasList = list && list.length > 0;
    actionRefCurrent.current.list = list;
    setQuotationList(list);
    if (onQuotationChange) {
      onQuotationChange(list);
    }
  };

  const triggerRelationUpdate = (v = {}) => {
    // 处理选中逻辑
    if (actionRefCurrent.current.viewIdTimer) {
      clearTimeout(actionRefCurrent.current.viewIdTimer);
    }
    actionRefCurrent.current.viewIdTimer = setTimeout(() => {
      const aList = actionRefCurrent.current.list || [];
      let sId = '';
      if (!v.id && aList.length > 0) {
        const oItem = aList.find((i) => i.brand === (v.brand || brand)) || {};
        sId = oItem.id;
      }
      let viewId = v.id || sId;
      setScrollViewId(viewId);
    }, 100);
    // 触发回调
    if (onChange) {
      onChange({
        brand,
        ...v,
      });
    }
  };

  actionRefCurrent.current.getYjQuotation = (opt = {}) => {
    setLoading(true);
    actionRefCurrent.current.loading = true;
    requestYjQuotation(opt).then(async ({ data }) => {
      setLoading(false);
      actionRefCurrent.current.loading = false;
      let { quotation } = data || {};
      // 接口返回报价单null时，置空价格
      if (actionRefCurrent.current.hasList && !quotation) {
        quotation = actionRefCurrent.current.list.map(({ available, unavailable_msg, brand }) => {
          return {
            available,
            unavailable_msg,
            brand,
          };
        });
      }
      if (isArray(quotation)) {
        const brandList = await getDHBrandList();
        const list = quotation.map((item, index) => {
          item.id = `${item.brand}-${index}`;
          const oBrandBaseConfig = isArray(brandList)
            ? brandList.find((i) => i.brand === item.brand)
            : {};
          const { message } = oBrandBaseConfig || {};
          return {
            message,
            ...item,
          };
        });
        triggerUpdate(list);
      }
    });
  };

  const getYjQuotationDebounce = useCallback(
    debounce(actionRefCurrent.current.getYjQuotation, 500, { trailing: true }),
    [],
  );

  // 获取报价单
  useUpdate(
    ({ logined }) => {
      if (!logined) return;
      if (isEmpty(address)) return;
      let addrData = transferWkdAddress(address, 'address');
      if (!isFullData(addrData)) {
        triggerUpdate([]);
        return;
      }
      getYjQuotationDebounce({
        addrData,
        weight,
        volume,
        back_sign_bill,
        package_service,
        pickup_way,
        floor,
      });
    },
    [address, weight, volume, back_sign_bill, package_service, pickup_way, floor],
  );

  // 更新选中的品牌
  // 优先外部选中、其次报价单第一个品牌
  useEffect(() => {
    if (
      propsBrand &&
      (propsBrand != actionRefCurrent.current.propsBrand ||
        actionRefCurrent.current.propsDeliveryType != propsDeliveryType)
    ) {
      // console.log('外部选中品牌====>')
      actionRefCurrent.current.propsBrand = propsBrand;
      actionRefCurrent.current.propsDeliveryType = propsDeliveryType;
      triggerRelationUpdate({ brand: propsBrand, delivery_type: propsDeliveryType });
    } else if (isArray(quotationList) && quotationList.length > 0) {
      if (!brand) {
        // console.log('默认指定品牌====>')
        const brandItem = quotationList[0] || {};
        if (brandItem.brand) {
          triggerRelationUpdate({ brand: brandItem.brand, delivery_type: brandItem.delivery_type });
        }
      } else {
        const sameBrandArr = quotationList.filter((i) => i.brand === brand);
        if (sameBrandArr && sameBrandArr.length > 0) {
          const brandItem = sameBrandArr[0] || {};
          // console.log('默认指定品牌+默认指定产品类型====>', brandItem)
          triggerRelationUpdate({ brand: brandItem.brand, delivery_type: brandItem.delivery_type });
        }
      }
    }
  }, [propsBrand, propsDeliveryType, brand, quotationList]);
  // 根据地址信息，动态拉取品牌配置数据
  useUpdate(
    async ({ logined }) => {
      if (!logined) return;
      if (!brand) return;
      if (isEmpty(address)) return;
      let addrData = transferWkdAddress(address, 'address');
      // console.log('报价单组件-brand==>', brand);
      let oBrandDynamicsConfig = {};
      oBrandDynamicsConfig = await getBrandOrderFrom({ ...addrData, brand });
      oBrandDynamicsConfig.brand = brand;
      Taro.kbSetGlobalData('DHBrandDynamicsConfig', oBrandDynamicsConfig);
      // console.log('拉取品牌动态配置数据', oBrandDynamicsConfig);
      triggerRelationUpdate(oBrandDynamicsConfig);
    },
    [address, brand],
  );

  const chooseBrand = (item = {}) => {
    triggerRelationUpdate({ brand: item.brand, delivery_type: item.delivery_type });
  };

  const handleChoose = debounce((item) => {
    console.log('选择品牌handleChoose-item===>', item);
    const serviceStr = formatAddAddValueService(service);
    console.log('serviceStr', serviceStr);
    if (!!serviceStr) {
      actionRefCurrent.current.brandItem = item;
      setOpenBrandChooseTips(true);
      return;
    }
    chooseBrand(item);
  }, 600);

  const handleProductDesc = (item = {}) => {
    const { delivery_type_name } = item;
    Taro.kbModal({
      top: false,
      title: delivery_type_name,
      content: DHBrandDescMap[item.brand][delivery_type_name] || [],
      confirmText: '我知道啦',
    });
  };

  const handleBrandChooseTips = (key) => {
    switch (key) {
      case 'close':
        setOpenBrandChooseTips(false);
        break;
      case 'confirm':
        setOpenBrandChooseTips(false);
        chooseBrand(actionRefCurrent.current.brandItem);
        break;
    }
  };

  return {
    brands,
    loading,
    brand,
    delivery_type,
    relationInfo,
    scrollViewId,
    quotationList,
    openBrandChooseTips,
    handleChoose,
    handleProductDesc,
    handleBrandChooseTips,
  };
};
