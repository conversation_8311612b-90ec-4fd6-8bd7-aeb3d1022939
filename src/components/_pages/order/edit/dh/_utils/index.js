/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { transferWkdAddress } from '@/components/_pages/order/_utils';
import { getPage, isFullData } from '@base/utils/utils';
import Taro from '@tarojs/taro';

export const handleProPriceClick = () => {
  const _this = getPage();
  const { relationInfo, extraInfo, form: { data: formData } = {} } = _this.state;
  const { dynamicForms } = relationInfo || {};
  const { service: dynamicService = {} } = dynamicForms || {};
  const address = formData || {};
  let addrData = transferWkdAddress(address, 'address');
  if (!isFullData(addrData)) {
    Taro.kbToast({
      text: '请完善地址信息后再选择保价',
    });
    return;
  }
  Taro.navigator({
    url: `order/edit/service`,
    key: 'routerParamsChange',
    options: {
      title: '保价服务',
      pageSource: 'dh',
      relationInfo,
      address,
      data: extraInfo,
      serviceConfig: dynamicService,
      mode: 'order',
    },
    onArrived: () => {},
  });
};
