/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { transferWkdAddress } from '@/components/_pages/order/_utils';
import { createListener, isFullData } from '@base/utils/utils';
import Taro, { useEffect, useState } from '@tarojs/taro';
import isFunction from 'lodash/isFunction';

export const formatAddAddValueService = (service) => {
  const { back_sign_bill, package_service, pickup_way, floor } = service || {};
  let arr = [];
  if (back_sign_bill) {
    arr.push(back_sign_bill);
  }
  if (package_service) {
    arr.push(package_service);
  }
  if (pickup_way) {
    arr.push(pickup_way);
    if (floor && floor * 1 > 0) {
      arr.push(`${floor}层`);
    }
  }
  return arr.join('/');
};

export const useAddValueService = (props) => {
  const {
    relationInfo,
    dynamicForms,
    extraInfo,
    address,
    weight,
    volume,
    onChange = () => {},
  } = props;
  const { brand } = relationInfo || {};
  const { floor: dynamicFloor = {} } = dynamicForms || {};
  const { service } = extraInfo || {};
  const { floor } = service || {};

  const [formatVal, setFormatVal] = useState();

  const triggerChange = (v = {}) => {
    if (isFunction(onChange)) {
      onChange({
        data: {
          service: {
            ...service,
            ...v,
          },
        },
      });
    }
  };

  useEffect(() => {
    if (!service) return;
    setFormatVal(formatAddAddValueService(service));
  }, [service]);

  const handleFloorChange = () => {
    triggerChange({
      floor: floor == 0 ? 1 : 0,
    });
  };

  const handleClickServiceLabel = () => {
    Taro.navigator({
      url: `order/edit/service/dh/desc`,
      options: {
        brand,
        type: 'pickup_way',
      },
    });
  };

  const handleClick = () => {
    let addrData = transferWkdAddress(address, 'address');
    if (!isFullData(addrData)) {
      Taro.kbToast({
        text: '请完善地址信息后再选择增值服务',
      });
      return;
    }
    createListener('dhService', (_data) => {
      triggerChange(_data);
    });
    Taro.navigator({
      url: `order/edit/service/dh`,
      key: 'routerParamsChange',
      options: {
        relationInfo,
        address,
        extraInfo,
        weight,
        volume,
      },
      onArrived: () => {},
    });
  };

  return {
    brand,
    dynamicFloor,
    floor,
    formatVal,
    handleFloorChange,
    handleClickServiceLabel,
    handleClick,
  };
};
