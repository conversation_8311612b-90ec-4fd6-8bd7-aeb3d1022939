/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { orderAction } from '@/components/_pages/order/_utils';
import { getStorageSync, setStorage } from '@base/utils/utils';
import Taro from '@tarojs/taro';

// 记录上次下单类型
export const lastEditOrderTypeKey = 'lastEditOrderType';
export const lastEditOrderRelationInfoKey = 'lastEditOrderRelationInfo';
export const recordEditOrderType = (type) => {
  const disabledType = ['djj', 'th', 'dh', 'tcjs'];
  if (disabledType.includes(type)) return;
  let _type = type;
  if (_type == 'dak') {
    _type = 'courier';
  }
  Taro.kbSetGlobalData(lastEditOrderTypeKey, _type);
  setStorage({
    key: lastEditOrderTypeKey,
    data: _type,
  });
};

export const getRecordEditOrderType = (type) => {
  const lastEditOrderType = Taro.kbGetGlobalData(lastEditOrderTypeKey);
  if (lastEditOrderType) {
    return lastEditOrderType;
  }
  const res = getStorageSync(lastEditOrderTypeKey);
  const _type = type == 'dak' || type == 'courier' ? type : undefined;
  return (res && res.data) || _type || 'yjkd';
};

export const createSendBarsList = () => {
  const yjkd = {
    key: 'yjkd',
    title: '寄快递',
    desc: '智能比价，品牌寄件',
    url: 'order/edit/send',
    hotTag: '推荐',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/yjkd-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/yjkd-icon-2.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/yjkd-icon-3.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/yjkd-icon.png?v=011',
      value_opacity: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/yjkd-icon_o.png?v=011',
    },
  };
  const djj = {
    key: 'djj',
    title: '德邦大件',
    desc: '寄大件，享运费返现',
    url: 'order/djj',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/djj-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/djj-icon-2.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/djj-icon-2.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/djj-icon.png?v=011',
    },
  };
  const th = {
    key: 'th',
    title: '网购退货',
    desc: '快速识别，低至5元',
    url: 'order/edit/send',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/th-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/th-icon-2.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/th-icon-2.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/th-icon.png?v=011',
    },
  };
  const dh = {
    key: 'dh',
    title: '经济货运',
    desc: '货物30kg，低至49元',
    url: 'order/edit/dh',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dh-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dh-icon-3.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dh-icon-3.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dh-icon.png?v=011',
    },
  };
  const tcjs = {
    key: 'tcjs',
    title: '同城急送',
    desc: '极速沟通，高效送达',
    url: 'order/delivery',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tcjs-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tcjs-icon-3.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tcjs-icon-3.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tcjs-icon.png?v=011',
    },
  };
  const dak = {
    key: 'dak',
    title: '附近驿站',
    desc: '快速定位，安心投递',
    url: 'order/edit/send',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dak-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dak-icon-3.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dak-icon-3.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dak-icon.png?v=011',
    },
  };
  const courier = {
    key: 'courier',
    title: '快递员',
    desc: '专人服务，快速揽收',
    url: 'order/edit/send',
    iconInfo: {
      value1: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/courier-icon-1.png?v=01',
      value2: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/courier-icon-3.png?v=01',
      value3: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/courier-icon-3.png?v=01',
      value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/courier-icon.png?v=011',
      value_opacity: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/courier-icon_o.png?v=011',
      value_opacity_dak: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/dak-icon_o.png?v=011',
    },
  };

  return { yjkd, djj, th, dh, tcjs, dak, courier };
};

export const createSendBars = (type) => {
  const { yjkd, djj, th, dh, tcjs, dak, courier } = createSendBarsList();
  if (type === 'courier') {
    return {
      level1: courier,
      level2: [djj, th],
      level3: [dh, tcjs, dak, yjkd],
    };
  } else {
    return {
      level1: yjkd,
      level2: [djj, th],
      level3: [dh, tcjs, dak, courier],
    };
  }
};

export const handleSendBarClick = (item, relation) => {
  const { key } = item;
  let data = {
    editOrderType: key,
  };
  if (relation && relation.type && relation.type != 'tap') {
    data = {
      relation,
    };
  }
  orderAction({
    action: 'edit',
    data,
  });
};

export const createToolsBars = () => {
  const bars = [
    {
      key: 'price',
      value: '运费比价',
      url: 'user/price',
      iconInfo: {
        value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tools-yfbj.png?v=021',
      },
    },
    {
      key: 'stopArea',
      value: '停发区域',
      url: 'closedArea',
      iconInfo: {
        value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tools-tfqy.png?v=021',
      },
    },
    {
      key: 'postage',
      value: '包装费查询',
      id: 18,
      iconInfo: {
        value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tools-bzfcx.png?v=021',
      },
    },
    {
      key: 'service',
      value: '客服服务',
      url: 'user/service',
      iconInfo: {
        value: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/sub/tools-kf.png?v=021',
      },
    },
  ];

  return bars;
};

export const handleToolsBarClick = (item) => {
  const { url = '', id } = item;
  Taro.navigatorAndDocument({
    url,
    id,
  });
};
