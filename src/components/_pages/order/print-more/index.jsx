/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Component } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbModal from '@base/components/modal';
import KbInputNumber from '@base/components/input-number';
import { orderAction } from '@/components/_pages/order/_utils';
import './index.scss';

class Index extends Component {
  static options = { addGlobalClass: true };
  static defaultProps = {
    data: {},
    onPrintted: () => {},
  };
  constructor(props) {
    super(props);
    this.state = {
      count: 2,
      isOpened_: false,
      bars: [
        {
          key: 'pprint',
          label: '预打印这批订单',
        },
        {
          key: 'print',
          label: '打印这批订单',
        },
      ],
    };
  }

  componentDidUpdate(preProps) {
    const { isOpened } = preProps;
    const { isOpened: nextIsOpened } = this.props;
    if (nextIsOpened !== isOpened) {
      this.setState({
        isOpened_: !!nextIsOpened,
      });
    }
  }

  //   确认
  onConfirm = (item) => {
    this.triggerModalStatus(false, () => {
      const { data, onPrintted, pageType } = this.props;
      const { key: action } = item;
      const { count } = this.state;
      if (pageType === 'orderEdit') {
        onPrintted({ action, count });
        return;
      }
      orderAction({ action, data, count })
        .then((res) => onPrintted(res))
        .catch((err) => console.log(err));
    });
  };

  onChangeInput = (count) => {
    this.setState({
      count,
    });
  };

  triggerModalStatus = (isOpened_, then) => {
    this.setState(
      {
        isOpened_,
      },
      then,
    );
  };

  onClose = () => this.triggerModalStatus(false);

  render() {
    const { count, bars, isOpened_ } = this.state;
    return (
      <KbModal
        isOpened={isOpened_}
        title='选择您要打印的订单数'
        cancelText=''
        confirmText=''
        onConfirm={this.onConfirm}
        onCancel={this.onClose}
        onClose={this.onClose}
        bars={bars}
      >
        <View className='kb-print-box'>
          <View className='kb-print-box__text'>填写您要打印订单的份数</View>
          {isOpened_ && (
            <View>
              <KbInputNumber min={2} max={50} onChange={this.onChangeInput} value={count} />
            </View>
          )}
        </View>
      </KbModal>
    );
  }
}

export default Index;
