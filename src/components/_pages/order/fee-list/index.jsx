/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import './index.scss';

const Index = (props) => {
  const { list, reckonFee } = props;
  return (
    <View className='kb-fee-detail'>
      <View className='kb-fee-detail__content'>
        <View className='kb-fee-detail__list'>
          {list.map((item) => (
            <View className='kb-fee-detail__list--item' key={item.label}>
              <View className='kb-color__grey'>{item.label}</View>
              <View>
                {item.fee ? (
                  <Text>￥{item.fee}</Text>
                ) : item.value ? (
                  <Text>{item.value}</Text>
                ) : (
                  <Text className={`kb-color__${item.desc ? 'black' : 'grey'}`}>
                    {item.desc || '暂无'}
                  </Text>
                )}
              </View>
            </View>
          ))}
        </View>
        <View className='kb-fee-detail__tips'>
          <Text className='kb-color__orange'>注：</Text>
          <Text>包裹实际重量超出填写重量，由驿站线下收取续重费</Text>
        </View>
        <View className='kb-fee-detail__total'>
          <View>合计</View>
          <View className='kb-color__red'>￥{reckonFee}</View>
        </View>
      </View>
    </View>
  );
};

Index.defaultProps = {
  list: [],
  reckonFee: '--',
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
