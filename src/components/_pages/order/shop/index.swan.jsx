/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useEffect, useRef, useScope, useState } from '@tarojs/taro';
import Kb<PERSON>ongList from '@base/components/long-list';
import KbStoreCardInfo from '@/components/_pages/store-card/info';
import KbStoreCardInfoDak from '@/components/_pages/store-card/info-dak';
import KbCheckbox from '@base/components/checkbox';
import KbDragBar from '@base/components/drag-bar';
import { View, Map, CoverView, CoverImage, Image } from '@tarojs/components';
import { formatShopInfo, createRelation } from '@/components/_pages/store-card/_utils';
import { orderAction } from '@/components/_pages/order/_utils';
import { createMapContext } from '@/components/_pages/order/_utils/order.shop';
import { useSelector } from '@tarojs/redux';
import isArray from 'lodash/isArray';
import classNames from 'classnames';
import apis from '@/utils/apis';
import './index.swan.scss';

const Index = (props) => {
  const { dakId, active } = props;
  const $scope = useScope();
  const [list, updateList] = useState([]);
  const [mode, updateMode] = useState('max');
  const [position, updatePosition] = useState(null);
  const [includePoints, updateIncludePoints] = useState(null);
  const [markers, updateMarkers] = useState([]);
  const { relationInfo = {} } = useSelector((state) => state.global);
  const scale = 16;
  const mapId = 'shop-map';
  const actionRef = useRef({});
  const pageKey = process.env.MODE_ENV === 'wkd' ? 'start' : 'page';
  const pageSizeKey = process.env.MODE_ENV === 'wkd' ? 'limit' : 'size';
  const pageSize = 20;

  useEffect(() => {
    if (!active) return;
    mapInit();
  }, [active]);

  // 列表请求配置
  const listData = {
    pageKey,
    api: {
      url: apis['nearDakList'],
      nonceKey: 'lng,lat',
      data: {
        [pageSizeKey]: pageSize,
      },
      nonceKey: process.env.MODE_ENV === 'yz' ? 'lng,lat' : '',
      formatRequest: ({ longitude: lng, latitude: lat, ...rest }) => ({
        lat,
        lng,
        ...rest,
      }),
      formatResponse: ({ data: list }, req) => {
        const hasList = isArray(list) && list.length;
        if (hasList) {
          const formateList = list.map((item) => formatShopInfo(item));
          triggerAddMarkers(formateList, req[pageKey]);
          // 添加地图标记点
          return {
            code: 0,
            data: { list: formateList },
          };
        }
        return {
          data: void 0,
        };
      },
      onThen: (list) => {
        updateList(list);
        createIncludePoints(list);
      },
    },
  };

  // 选择驿站
  const handleSelect = (item) => {
    if (process.env.MODE_ENV === 'wkd') {
      orderAction({
        action: 'edit',
        data: {
          relation: item,
        },
      });
    } else {
      const dak_id = dakId || item.dakId;
      if (process.env.MODE_ENV !== 'third') {
        Taro.kbUpdateRelationInfo(item);
        Taro.navigator({
          target: 'tab',
          url: 'order/edit',
        });
      } else if (dak_id) {
        Taro.navigator({
          target: 'self',
          url: 'query/appointment',
          options: {
            dakId: dak_id,
          },
        });
        // 调用关联关系接口，保证此驿站被变成服务过您的驿站
        createRelation({ dak_id });
      }
    }
  };

  // 切换地图模式
  const switchMapMode = () => {
    // 每次重置为0保证内容长度不足以滚动时也能正常切换为min
    const { scrollTop, scrollDirection } = actionRef.current;
    updateMode(scrollTop >= 20 || scrollDirection === 'top' ? 'min' : 'max');
    actionRef.current.scrollTop = 0;
  };

  // 再次触摸解锁
  const handleTouch = (e) => {
    const { type } = e;
    if (type.toLowerCase() === 'touchend') {
      switchMapMode();
    }
  };

  // 列表滚动
  const handleScroll = (e) => {
    const {
      type,
      currentTarget: { scrollTop = 0 },
    } = e;
    if (type === 'scroll') {
      actionRef.current.scrollDirection =
        scrollTop - actionRef.current.scrollTop > 0 ? 'top' : 'bottom';
      actionRef.current.scrollTop = scrollTop;
    }
  };

  /**
   * 地图相关处理
   */
  // 地图初始化
  const mapInit = () => {
    getLocation();
    // 地图初始化
    actionRef.current.mapContext = createMapContext(mapId, $scope, {
      updateMarkers,
      updateIncludePoints,
      setCenterMarker,
    });
  };

  // 设置中心位置标记：触发获取中心点范围内的驿站
  const setCenterMarker = (position, force = false) => {
    actionRef.current.centerMarker = {
      id: 999,
      ...position,
      iconPath: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/images/map-center.png',
      width: 30,
      height: 30,
    };
    if (!actionRef.current.hasCentered || force) {
      addMarkers([actionRef.current.centerMarker], true);
      updatePosition(position);
      actionRef.current.hasCentered = true;
    } else {
      // 移动到定位点
      actionRef.current.mapContext.moveToLocation({
        ...position,
        success: () => updatePosition(position),
      });
    }
  };

  // 定位
  const getLocation = () => {
    Taro.getLocation({
      type: 'gcj02',
    })
      .then((res) => {
        const { latitude, longitude } = res;
        setCenterMarker({
          longitude,
          latitude,
        });
        return res;
      })
      .catch(() => {});
  };

  const createIncludePoints = (list) => {
    // 缩放地图，包含目前所有点
    const points = list.map(({ latitude, longitude }) => {
      return {
        latitude: `${latitude}`,
        longitude: `${longitude}`,
      };
    });
    if (isArray(points) && points.length > 0) {
      if (actionRef.current.mapContext.includePoints) {
        actionRef.current.mapContext.includePoints({
          points,
        });
      } else {
        updateIncludePoints(includePoints);
      }
    }
  };

  // 统一微信与支付宝
  const addMarkers = (markers, clear) => {
    const { mapContext } = actionRef.current;
    if (process.env.PLATFORM_ENV !== 'alipay') {
      mapContext.addMarkers({
        markers,
        clear,
      });
    } else {
      if (mapContext.addMarkers) {
        mapContext.addMarkers({ markers });
      } else {
        mapContext.changeMarkers({
          add: markers,
        });
      }
    }
  };

  // 创建标记，非翻页的情况下，应该移除之前添加的标记
  const triggerAddMarkers = (list, page = 1) => {
    const { centerMarker, markersLength = 0 } = actionRef.current;
    const clear = page === 1;
    const markers = isArray(list)
      ? list.map((item, index) => {
          const { latitude, longitude, name: title, address } = item;
          const id = 1 + index + (page - 1) * markersLength;
          return {
            id,
            latitude,
            longitude,
            title,
            iconPath: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/images/map-maker.png',
            width: 23,
            height: 30,
            callout: {
              content: process.env.PLATFORM_ENV !== 'alipay' ? `${title}：\n${address}` : address,
              display: 'BYCLICK',
              borderRadius: 5,
              padding: 10,
              anchorY: -5,
            },
          };
        })
      : [];

    // 标记当前中心位置
    if (position && clear) {
      // 首页加载，加入中心位置
      markers.unshift(centerMarker);
    }

    addMarkers(markers, clear);
    actionRef.current.markersLength = markersLength + markers.length;
  };

  // 地图视野变化
  //  const handleRegionChange = e => {
  //     const {
  //       type,
  //       causedBy,
  //       detail: { centerLocation: { latitude, longitude } = {} }
  //     } = e;
  //     if (type === "end" && causedBy === "drag") {
  //       // 拖拽结束触发
  //       if (latitude && longitude) {
  //         setCenterMarker({
  //           longitude,
  //           latitude
  //         });
  //       }
  //     }
  //   };

  const rootCls = classNames('kb-shop', {
    [`kb-shop__${mode}`]: !!mode,
  });

  return (
    <View className={rootCls}>
      <View className='kb-shop__map--wrapper'>
        <Map
          showLocation
          markers={markers}
          includePoints={includePoints}
          className='kb-shop__map'
          scale={scale}
          latitude={position && position.latitude}
          longitude={position && position.longitude}
          // onRegionChange={handleRegionChange}
          id={mapId}
        />
        <CoverView hoverClass='kb-hover' className='kb-map-location' onClick={getLocation}>
          <CoverImage
            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/images/map-location.png'
            className='kb-map-location__image'
          />
        </CoverView>
      </View>

      <View className='kb-shop__list--wrapper'>
        <KbLongList
          data={listData}
          onScroll={handleScroll}
          onTouch={handleTouch}
          enableRefresh={mode === 'max'}
          enableMore
          active={active && position}
        >
          <View className='kb-shop__list'>
            {list &&
              list.map((item) => {
                return (
                  <View
                    key={item.cm_id}
                    className='kb-shop__list--item'
                    hoverClass='kb-hover'
                    onClick={handleSelect.bind(null, item)}
                  >
                    {process.env.MODE_ENV === 'wkd' ? (
                      <View className='at-row at-row__align--center'>
                        <View className='at-col'>
                          <KbStoreCardInfoDak data={item} />
                        </View>
                        <View className='kb-spacing-md-r'>
                          <KbCheckbox
                            checked={item.dak_id === relationInfo.current}
                            onChange={handleSelect.bind(null, item)}
                          />
                        </View>
                      </View>
                    ) : (
                      <KbStoreCardInfo data={item} />
                    )}
                  </View>
                );
              })}
          </View>
        </KbLongList>
        {process.env.MODE_ENV === 'wkd' && (
          <KbDragBar link='https://m.kuaidihelp.com/help/dak_activity'>
            <Image
              className='kb-darg-bar__image'
              mode='widthFix'
              src='https://cdn-img.kuaidihelp.com/wkd/miniApp/dak_open.png?v=1'
            />
          </KbDragBar>
        )}
      </View>
    </View>
  );
};

Index.defaultProps = {
  active: false,
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
