/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-shop {
  position: relative;
  height: 100%;

  &__page.kb-page {
    height: 100%;
    position: static;
  }

  &__map {
    transition: height $animation-duration-slow;
    width: 100%;
    z-index: 1;

    &--wrapper {
      position: relative;
      height: 100%;
    }
  }

  &__list {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &--wrapper {
      background-color: $color-white;
      border-radius: 3 * $border-radius-lg 3 * $border-radius-lg 0 0;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 2;
      transition: height $animation-duration-slow;
    }

    &--item {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        left: $spacing-h-md;
        bottom: 0;
        right: 0;
        border-bottom: $width-base solid $color-grey-5;
      }
    }

  }

  &__max {
    .kb-shop__map {
      height: calc(70% + 60px);
    }

    .kb-shop__list--wrapper {
      height: 30%;
    }

    .kb-map-location {
      bottom: calc(30% + 10px);
    }
  }

  &__min {
    .kb-shop__map {
      height: calc(30% + 60px);
    }

    .kb-shop__list--wrapper {
      height: 70%;
    }

    .kb-map-location {
      bottom: calc(70% + 10px);
    }
  }

}

// 地图气泡
.kb-map {
  &-callout {
    box-sizing: border-box;
    background-color: $color-white;
    border: $width-base solid $color-grey-5;
    border-radius: $border-radius-lg;
    width: 400px;
    height: auto;
    padding: $spacing-v-md $spacing-h-md;

    &__address {
      color: $color-grey-2;
      font-size: $font-size-base;
      word-wrap: break-word;
      white-space: normal;
      padding-top: $spacing-v-sm;
    }
  }

  &-location {
    position: absolute;
    right: $spacing-h-md;
    width: 80px;
    height: 80px;
    background-color: $color-white;
    border-radius: $border-radius-circle;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    z-index: 999;
    transition: bottom $animation-duration-slow;

    &__image {
      width: 60%;
      height: 60%;
      display: inline-block;
      vertical-align: middle;
    }
  }
}

.kb-darg-bar__image {
  width: 228px;
  height: 80px;
  vertical-align: middle;
}
