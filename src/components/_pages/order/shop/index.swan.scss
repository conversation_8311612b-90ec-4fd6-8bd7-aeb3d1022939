/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-shop {
  position: relative;
  height: 100%;

  &__page.kb-page {
    position: static;
    height: 100%;
  }

  &__map {
    z-index: 1;
    width: 100%;
    height: 100%;
    min-height: 100%;
    transition: height $animation-duration-slow;

    &--wrapper {
      position: relative;
      height: 100%;
      overflow: hidden;
    }
  }

  &__list {
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &--wrapper {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 2;
      background-color: $color-white;
      border-radius: 3 * $border-radius-lg 3 * $border-radius-lg 0 0;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      transition: height $animation-duration-slow;
    }

    &--item {
      position: relative;

      &::after {
        position: absolute;
        right: 0;
        bottom: 0;
        left: $spacing-h-md;
        border-bottom: $width-base solid $color-grey-5;
        content: '';
      }
    }
  }

  &__max {
    // .kb-shop__map {
    //   height: 70%;
    // }

    .kb-shop__map--wrapper {
      height: 70%;
    }

    .kb-shop__list--wrapper {
      height: 30%;
    }
  }

  &__min {
    // .kb-shop__map {
    //   height: 30%;
    // }

    .kb-shop__map--wrapper {
      height: 30%;
    }

    .kb-shop__list--wrapper {
      height: 70%;
    }
  }
}

// 地图气泡
.kb-map {
  &-callout {
    box-sizing: border-box;
    width: 400px;
    height: auto;
    padding: $spacing-v-md $spacing-h-md;
    background-color: $color-white;
    border: $width-base solid $color-grey-5;
    border-radius: $border-radius-lg;

    &__address {
      padding-top: $spacing-v-sm;
      color: $color-grey-2;
      font-size: $font-size-base;
      white-space: normal;
      word-wrap: break-word;
    }
  }

  &-location {
    position: absolute;
    right: $spacing-h-md;
    bottom: $spacing-v-md;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: $color-white;
    border-radius: $border-radius-circle;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    transition: bottom $animation-duration-slow;

    &__image {
      display: inline-block;
      width: 60%;
      height: 60%;
      vertical-align: middle;
    }
  }
}

.kb-darg-bar__image {
  width: 228px;
  height: 80px;
  vertical-align: middle;
}
