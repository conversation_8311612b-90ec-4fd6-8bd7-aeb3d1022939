/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, Fragment, useCallback } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import { debounce, noop } from '@base/utils/utils';
import { useUpdate } from '@base/hooks/page';
import request from '@base/utils/request';
import classNames from 'classnames';
import { getIntegralOptimalItem } from './_utils';
import './index.scss';

const Index = (props) => {
  const { isOpened, loginData, className, refresh, mode, dakId: dak_id, onChange, ...rest } = props;
  const [integral, updateIntegral] = useState(0);
  const rootCls = classNames('kb-button__link', className);

  // 跳转积分中心
  const handleToIntegral = () => {
    Taro.navigator({
      url: 'order/integral',
      options: {
        integral,
      },
    });
  };

  // 获取积分开启状态
  const getIntegralStatus = useCallback(
    debounce(
      (dak_id) => {
        request({
          url: '/api/cloudPrint/order/v1/pay/NewVipPoints/userVipPoints',
          data: {
            dak_id,
          },
          nonceKey: process.env.MODE_ENV === 'yz' ? 'dak_id' : '',
          toastLoading: false,
          onThen: ({ data, code }) => {
            if (code === 0) {
              const optimal = getIntegralOptimalItem(data);
              updateIntegral(optimal.points);
              onChange(data);
            }
          },
        });
      },
      500,
      { trailing: true },
    ),
    [],
  );

  useUpdate(
    (loginData) => {
      const { logined } = loginData;
      if (logined && dak_id) {
        getIntegralStatus(dak_id);
      }
    },
    [dak_id, refresh],
  );

  return integral > 0 && isOpened ? (
    mode === 'normal' ? (
      <AtButton className={rootCls} size='small' onClick={handleToIntegral} {...rest}>
        积分
      </AtButton>
    ) : (
      <Fragment>{props.children}</Fragment>
    )
  ) : null;
};

Index.defaultProps = {
  refresh: null,
  onChange: noop,
  mode: 'normal',
  order_id: '',
  isOpened: true,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
