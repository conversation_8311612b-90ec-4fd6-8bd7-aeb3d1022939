/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import numeral from "numeral";

// 积分转换金额
export function integralToMoney(integral) {
  return numeral(integral / 100).format("0.00");
}

// 根据总金额获取可用最大积分
export function getIntegralByMony(integral, money) {
  const max = Math.floor(money * 100);
  return Math.min(max < 0 ? 0 : max, integral);
}
export const getValidPoints = (data, max_points) => {
  const { points = 0 } = data;
  data.points =
    parseFloat(points) < parseFloat(max_points) ? points : max_points;

  return data;
};
// 获取较多积分的一方优先使用
export const getIntegralOptimalItem = data => {
  const { inn, city_shop, max_points } = data || {};
  const { points = 0 } = inn || {};
  const { points: cityPoints = 0 } = city_shop || {};
  let optimal = {};
  if (points >= cityPoints) {
    optimal = { ...inn };
  } else {
    optimal = { ...city_shop };
  }
  const res = getValidPoints(optimal, max_points);
  return res;
};
