/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  checkIsReLogin,
  dateCalendar,
  debounce,
  getStorage,
  noop,
  setStorage,
} from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import { AtFloatLayout, AtIcon, AtInput } from 'taro-ui';

import KbCardBar from '@/components/_pages/order/card-bar';
import { getPayConfig } from '@/components/_pages/order/_utils/order.pay';
import KbCheckbox from '@base/components/checkbox';
import KbDownContainer from '@base/components/down-container';
import KbEmpty from '@base/components/empty';
import KbLoader from '@base/components/loader';

import KbScrollView from '@base/components/scroll-view';
import Form from '@base/utils/form';
import request from '@base/utils/request';
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import numeral from 'numeral';

import classNames from 'classnames';
import isBoolean from 'lodash/isBoolean';
import KbListBox from '../listbox';
import { fixDynamicFormsData, freshList, getFormItem, goodsKeys, serviceKeys } from '../_utils';
import './common.scss';
import KbSendMode from './send-mode';
import KbService from './service';
import { eligibleTimer, goodWeightConfig, initOrderTimer } from './_utils';

@connect(({ global }) => ({ loginData: global.loginData }))
class Index extends Component {
  static defaultProps = {
    data: {},
    storageKey: 'extraInfo',
    isOpened: false,
    isOpenedValue: '',
    dynamicForms: fixDynamicFormsData(),
    onChange: noop,
    onGetted: noop,
    onOpenPay: noop,
    mode: 'order', // 订单模式，包含品牌，现付，增值服务选择 order|address
    unfold: false,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor(props) {
    super(props);
    this.manual = '手动输入';
    this.packageInfoKey = 'packageInfo';
    const { unfold = false } = props;
    this.weightRange = [0.01, 99];
    this.state = {
      loading: false,
      form: { data: {}, disabled: true },
      brands: [],
      packages: [],
      selectted: '',
      package_info_value: '',
      package_info_max: 4,
      isOpened: false,
      unfold_: unfold,
      openPay: false,
      editableMony: true,
      isOpenedTimer: false,
      timerDay: [
        {
          label: '今天',
          checked: true,
        },
        { label: '明天' },
        { label: '后天' },
      ],
      timerSection: null, //上门取件的时间区间
      servicesConfig: {
        courierId: null,
        cost: null, // 保价价费率
        proPriceStart: null, // 保价起点
        proPriceEnd: null, // 保价上限
        isDelivery: null, // 是否开启代收货款0.关闭，1.开启
        isDesPay: null, // 是否开启到付，0.关闭1.开启
        isDecVal: null, // 是否声明价值：0.关闭，1.开启
        isProPrice: null, // 是否开启保价：0.关闭,1.开启
        isToDoor: null, // 是否支持上门取件0.关闭,1.开启
        allowReserveTime: '', //是否支持上门取件预约时间
        isFresh: null, // 是否支持生鲜：0.关闭,1.开启,
        isDeclared: null, //是否开启自定义的声明
        reserve_start_time: '',
        reserve_end_time: '',
      }, //增值服务配置
    };
    this.handleSelectChange = this.handleSelectChange.bind(this);
    this.getPackageInfo = debounce(this.getPackageInfo, 300, {
      trailing: true,
    });
    this.onPackageInfoAction = debounce(this.onPackageInfoAction, 100, {
      trailing: true,
    });
    this.getConfig = debounce(this.getConfig, 300, { trailing: true });
    // refs
    this.serviceRef = createRef();
    this.handleSendModeChange = this.handleSendModeChange.bind(this);
    this.handleDeobunceChange = debounce(this.handleDeobunceChange, 300, {
      trailing: true,
    });
    this.handleServiceChange = debounce(this.handleServiceChange, 300, {
      trailing: true,
    });
  }
  handleSendModeChange = (value) => {
    if (value === 'take') {
      const {
        servicesConfig: { allowReserveTime, isToDoor },
      } = this.state;
      allowReserveTime && isToDoor && this.handleOpenTimer();
      this.formIns.update({ shipper_zipcode_del: '1' });
    } else {
      this.handleOpenTimer(false);
      this.formIns.update({ reserve_time: '', shipper_zipcode_del: '' });
    }
  };

  handleOpenTimer = (bool) => {
    this.setState({
      isOpenedTimer: isBoolean(bool) ? bool : true,
    });
  };
  // 拉取驿站增值服务配置项
  getServicesConfig(relationInfo) {
    const { dakId: dak_id, relation_id, agent_guid } = relationInfo;
    if (!dak_id && !relation_id && !agent_guid) return;
    const url = dak_id
      ? '/api/weixin/mini/minpost/order/getDakValueaddConfig'
      : '/api/weixin/mini/minpost/relation/checkRelation';
    relationInfo &&
      request({
        url: url,
        toastLoading: false,
        data: {
          dak_id,
          relation_id,
          agent_guid,
        },
        nonceKey: process.env.MODE_ENV === 'yz' ? (dak_id ? 'dak_id' : 'relation_id') : '',
        onThen: ({ data }) => {
          const { isToDoor, reserve_start_time, reserve_end_time, allowReserveTime } = data || {};
          const switchKeys = ['allowReserveTime'];
          Object.keys(data).forEach((key) => {
            if (key.includes('is') || switchKeys.includes(key)) {
              data[key] === '1' ? (data[key] = true) : (data[key] = false);
            }
          });
          let state = {};
          if (isToDoor && allowReserveTime) {
            let timer = initOrderTimer({
              start_time: reserve_start_time,
              end_time: reserve_end_time,
            });
            state.timerSection = timer;
            const value = timer[0].length ? timer[0][0].value : '';
            if (value && reserve_start_time && reserve_end_time) {
              const eligible = eligibleTimer(value, [reserve_start_time, reserve_end_time]);
              if (eligible) {
                this.formIns.update({
                  reserve_time: value,
                });
              }
            }
          }
          state.servicesConfig = { ...(isObject(data) ? data : {}) };
          this.setState(state);
          setStorage({
            key: 'servicesConfig',
            data: state.servicesConfig,
          });
        },
      });
  }
  componentDidMount() {
    this.createForm(() => {
      const { relationInfo, dynamicForms } = this.props;
      if (relationInfo) {
        // 动态表单对象中设置了品牌的话不拉取品牌
        if (dynamicForms['brand'].value) return;
        // 补偿获取关系后，重新拉取品牌列表
        this.getPackageInfo('brands', relationInfo);
        this.getServicesConfig(relationInfo);
      }
    });
    this.getConfig();
  }
  componentWillReceiveProps(preProps) {
    const {
      data: nextData,
      relationInfo: nextRelationInfo,
      loginData: nextLoginData,
      unfold: nextUnfold,
      dynamicForms: nextDynamicForms,
    } = this.props;
    const { data, relationInfo, loginData, unfold, dynamicForms } = preProps;
    // 更新信息
    if (nextData && nextData !== data) {
      const { pay_status, orderPrice, ...nextDataRest } = nextData;
      let editableMony = true;
      // orderPrice 此值实现问题：包含空值、0 、大于0
      if (orderPrice > 0) {
        nextDataRest.orderPrice = orderPrice;
        editableMony = pay_status != '1';
      } else if (orderPrice === '') {
        // 清空输入
        nextDataRest.orderPrice = '';
      }
      if (!nextData.service) {
        this.setState({
          serviceClear: { clear: true },
        });
      }
      this.formIns.update(nextDataRest);
      this.setState({
        editableMony,
      });
    }

    if (nextUnfold !== unfold) {
      this.setState({
        unfold_: nextUnfold,
      });
    }
    if (nextRelationInfo && nextRelationInfo !== relationInfo) {
      this.getServicesConfig(nextRelationInfo);
      // 补偿获取关系后，重新拉取品牌列表
      if (nextDynamicForms && !nextDynamicForms['brand'].value) {
        this.getPackageInfo('brands', nextRelationInfo);
      }
    }
    if (nextDynamicForms !== dynamicForms && nextDynamicForms) {
      this.triggerWhetherFormLocked(nextDynamicForms);
      return;
    }

    checkIsReLogin(nextLoginData, loginData, this.getConfig);
  }
  // 是否触发表单锁定为自定义内容
  triggerWhetherFormLocked = (dynamicForms) => {
    let updateData = {};
    if (!dynamicForms) return;
    Object.keys(dynamicForms).forEach((i) => {
      if (dynamicForms[i].value) {
        updateData[i] = dynamicForms[i].value;
      }
    });
    if (Object.keys(updateData).length) {
      this.formIns.update({ ...updateData });
      return updateData;
    }
    return false;
  };

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (cb) => {
    const { mode } = this.props;
    const form = getFormItem({
      keys: goodsKeys,
      data: { goods_name: '日用品' },
      merge: {
        goods_name: { required: true },
      },
    });
    if (mode === 'order') {
      // 订单模式：表单中加入品牌，现付，增值服务
      getFormItem({
        form,
        keys: serviceKeys,
        merge: {
          service: { value: null, storage: false },
          orderPrice: { value: '', storage: false },
        },
      });
    }
    const { storageKey } = this.props;
    this.formIns = new Form(
      {
        form,
        storageKey,
        onChange: ({ value, name, type }) => {
          const weightRange = this.weightRange;
          if (name === 'goods_weight') {
            let val = numeral(
              Math.min(Math.max(weightRange[0], numeral(value).value()), weightRange[1]),
            );
            if (type == 'blur') {
              return value ? val.format('0.00') : '';
            } else {
              return value ? val.value() : '';
            }
          }
        },
        onUpdate: (data) => {
          const {
            data: { service, goods_weight: changeWeight },
            nextData,
            formEvent,
          } = data;
          const { goods_weight } = nextData;
          const { productCode } = service || {};

          if (!!productCode) {
            let range = goodWeightConfig[productCode].split('-');
            this.weightRange = range;
            parseFloat(goods_weight) !== parseFloat(range[0]) &&
              this.formIns.update({ goods_weight: range[0] });
          }
          if (formEvent === 'blur' && changeWeight) {
            return;
          }
          this.props.onChange(data);
        },

        onReady: () => {
          cb && cb();
        },
      },
      this,
    );
  };

  // 展开
  onOpen = () => {
    this.setState({
      unfold_: true,
    });
  };

  // 切换物品类型弹窗状态
  switchFloatLayoutStatus = (isOpened, then) => {
    const { isOpenedValue } = this.state;
    this.setState(
      {
        isOpenedValue: isOpened || isOpenedValue,
        isOpened,
      },
      then,
    );
  };

  // 获取本地物品缓存
  getPackageInfoStorage = (then) => {
    getStorage({
      key: this.packageInfoKey,
      complete: (res) => {
        const { data } = res.data || {};
        then(isArray(data) ? data : []);
      },
    }).catch(() => {});
  };

  // 设置本地物品缓存
  setPackageInfoStorage = (value) => {
    const { packages } = this.state;
    if (!value || packages.findIndex((item) => item.label === value) >= 0) {
      return;
    }
    this.getPackageInfoStorage((list) => {
      list.unshift(value);
      setStorage({
        key: this.packageInfoKey,
        data: list.slice(0, 6),
      });
    });
  };

  // 获取物品类型列表、快递品牌
  getPackageInfo = (key, relationInfo = this.props.relationInfo) => {
    const urls = {
      brands: '/api/weixin/mini/minpost/relation/supportBrand',
    };
    const { onGetted } = this.props;
    if (key === 'packages') {
      // 物品类型使用前端配置
      const data = ['日用品', '数码产品', '衣物', '食物', '文件', '鞋靴'];
      this.getPackageInfoStorage((list) => {
        if (isArray(data)) {
          const packages = [...data.filter((item) => item !== '其它'), ...list].map(
            (item, index) => {
              return {
                label: item,
                key: `i_${index}`,
              };
            },
          );
          packages.push({
            key: 'other',
            label: this.manual,
          });
          this.setState({
            packages,
          });
          onGetted({
            packages,
            brands: this.state.brands,
          });
        }
      });
    } else {
      const { relation_id } = relationInfo || {};
      if (!relation_id) return;
      request(
        {
          url: urls[key],
          toastLoading: false,
          loadingStatusKey: 'loading',
          data: { relation_id },
          onThen: ({ data }) => {
            let brands = [];
            // 只有一个品牌时自动设置为下单品牌
            if (isArray(data) && data.length == 1) {
              this.formIns.update({ brand: data[0].brand });
              onGetted({
                brands,
              });
              return;
            } else if (isArray(data) && data.length >= 2) {
              brands = data.map((item) => {
                return {
                  key: item.brand,
                  label: item.brand_name,
                };
              });
            }
            this.setState(
              {
                brands,
              },
              () => {
                onGetted({
                  packages: this.state.packages,
                  brands,
                });
                const {
                  form: { data },
                } = this.state;
                const [{ key: firstKey = '' } = {}] = brands;
                // 品牌信息更新补偿，及时更新不可用品牌
                const { key: brand = '', label: brand_name } = this.filterBrands(
                  firstKey || data.brand,
                );
                // 表单数据一致，则不更新
                if (brand === data.brand) return;

                this.formIns.update({
                  brand,
                  brand_name,
                });
              },
            );
          },
        },
        this,
      );
    }
  };

  // 匹配当前的品牌
  filterBrands = (by) => {
    const {
      brands,
      form: { data },
    } = this.state;
    const by_ = by || data.brand;
    const [{ key = '', bars = null, label = '' } = {}] = brands.filter(
      (item) => item.key === by_ || item.label === by_,
    );
    const [{ key: brand_extra = '' } = {}] = bars || [];
    return {
      label,
      key,
      bars,
      brand_extra: bars ? data.brand_extra || brand_extra : '',
    };
  };

  // 关闭
  onClose = () => this.switchFloatLayoutStatus(false);

  // 选择物品类型、或者快递品牌
  onPackageInfoAction = (key, value) => {
    let {
      package_info_max,
      isOpened,
      selectted,
      package_info_value,
      form: {
        data: { goods_name = '', brand = '' },
      },
    } = this.state;
    switch (key) {
      case 'disabled':
        return;
      case 'open':
        // 开启获取物品类型
        const { label: brandLabel } = this.filterBrands(brand);
        selectted = value === 'packages' ? goods_name : brandLabel;
        this.setState(
          {
            selectted,
          },
          () => {
            this.switchFloatLayoutStatus(value, () => this.getPackageInfo(value));
          },
        );
        break;
      case 'input':
        // 自定义物品类型
        this.setState({
          package_info_value: value,
        });
        break;
      case 'select':
        // 勾选物品类型
        const { label } = value;
        if (selectted === label) return;
        this.setState(
          {
            selectted: label,
          },
          () => {
            if (label !== this.manual) {
              this.onPackageInfoAction('confirm');
            }
          },
        );
        break;
      case 'cancel':
        // 取消物品选择
        this.onClose();
        break;
      case 'confirm':
        // 确认物品、快递品牌选择
        let updateKey = 'goods_name';
        let extra = null;
        if (isOpened === 'brands') {
          // 选择品牌
          updateKey = 'brand';
          const { key: brandKey, brand_extra, label: brand_name } = this.filterBrands(selectted);
          selectted = brandKey;
          extra = {
            brand_name,
            brand_extra,
          };
        } else {
          if (selectted === this.manual) {
            if (package_info_value.length > package_info_max) {
              package_info_value = `${package_info_value}`.substring(0, package_info_max);
              Taro.kbToast({
                text: `物品信息最多${package_info_max}个字`,
              });
            }
            selectted = package_info_value;
            // 缓存
            this.setPackageInfoStorage(selectted);
          }
        }
        if (!selectted) {
          return;
        }
        this.switchFloatLayoutStatus(false, () => {
          this.formIns.update({
            [updateKey]: selectted,
            ...extra,
          });
        });

        break;
      default:
        break;
    }
  };
  handleSelectChange = (item) => {
    this.onPackageInfoAction('select', item);
  };

  handleSelectTimer = (data, key) => {
    const { timerDay } = this.state;
    let state = {};
    switch (key) {
      case 'disabled':
        return;
        break;
      case 'day':
        if (timerDay[data].checked) return;
        state.timerDay = timerDay.map((item, index) => {
          index !== data ? (item.checked = false) : (item.checked = true);
          return item;
        });
        break;
      case 'time':
        this.formIns.update({
          reserve_time: data.value,
        });
        state.isOpenedTimer = false;
        break;
    }

    this.setState(state);
  };
  // 增值服务变更
  handleServiceChange = (data) => {
    this.formIns.update({
      service: data,
    });
  };

  handleFreshTip = () => {
    Taro.kbToast({
      text: '生鲜订单需要专用生鲜单号进行打印',
    });
  };
  // 品牌额外信息
  onChangeBrandExtra = (key) => {
    this.formIns.update({
      brand_extra: key,
    });
  };
  handleDeobunceChange = (key, value, e) => {
    this.onChange_form(key, e);
  };
  // 获取是否允许支付
  getConfig = () => {
    const { loginData: { logined } = {} } = this.props;
    if (!logined) return;
    getPayConfig().then((openPay) => {
      this.setState({
        openPay,
      });
      this.props.onOpenPay(openPay);
    });
  };

  render() {
    const {
      openPay,
      loading,
      isOpened,
      isOpenedValue,
      unfold_,
      selectted,
      package_info_max,
      package_info_value,
      editableMony,
      servicesConfig,
      form: { data },
      brands,
      isOpenedTimer,
      timerDay,
      timerSection,
      serviceClear,
    } = this.state;
    const { mode, total, relationInfo, reckonFee, dynamicForms } = this.props;
    const allowPay = total <= 1 && openPay;
    const list = this.state[isOpenedValue] || [];
    const hasList = !!list.length;
    const { label: brandLabel, bars: brandBars } = this.filterBrands();
    const orderPriceCls =
      brandBars && brandBars.length > 0
        ? 'item-content item-extra__line kb-spacing-md-r'
        : 'flex-glow-all';
    const checkedDayIndex = timerDay.findIndex((i) => i.checked);

    let timerCheckedAcivity = '';
    timerSection &&
      timerSection.forEach((item) => {
        isArray(item) &&
          item.forEach((i) => {
            const value = i.value;
            const day = value.split(' ')[0];
            if (value === data.reserve_time) {
              timerCheckedAcivity =
                i.label === '2个小时内'
                  ? i.label
                  : value && dateCalendar(day) + value.split(' ')[1];
            }
          });
      });

    let {
      brand: dynamicBrand,
      goods_name: dynamicGoodsName,
      goods_remark: dynamicGoodsRemark,
      service: dynamicService,
      goods_weight: dynamicGoodsWeight,
      card: dynamicCard,
      orderPrice: dynamicOrderPrice,
    } = dynamicForms || {};
    const { placeOrderConfig } = relationInfo || {};
    const brandCls = classNames('kb-navigator', 'kb-navigator-noborder', {
      'kb-clear__pseudo-ele-after': !!dynamicBrand.locked,
    });
    const goods_nameCls = classNames('kb-navigator', {
      'kb-clear__pseudo-ele-after': !!dynamicGoodsName.locked,
    });
    const goodsNameAct = dynamicGoodsName.locked ? 'disabled' : 'open';
    const brandAct = dynamicBrand.locked ? 'disabled' : 'open';
    return (
      <Fragment>
        <View className='kb-extra-info kb-extra-info-common'>
          {!unfold_ && (
            <View className='info-bar' hoverClass='kb-hover' onClick={this.onOpen}>
              <Text className='info-bar__text'>展开</Text>
            </View>
          )}
          <KbDownContainer isOpened={unfold_}>
            <View className='kb-form__group'>
              {unfold_ && (
                <Fragment>
                  {servicesConfig.isToDoor ? (
                    <View className='kb-form'>
                      <View className='kb-form__coustom-item'>
                        <KbSendMode
                          onChange={this.handleSendModeChange}
                          onSelect={this.handleOpenTimer}
                          value={timerCheckedAcivity}
                          allowReserveTime={servicesConfig.allowReserveTime}
                        />
                      </View>
                    </View>
                  ) : (
                    ''
                  )}

                  <View className='kb-form'>
                    <View className='kb-form__item'>
                      <View className='item-content at-col'>
                        <View
                          className={goods_nameCls}
                          hoverClass='kb-hover'
                          onClick={this.onPackageInfoAction.bind(this, goodsNameAct, 'packages')}
                        >
                          <View className='kb-navigator__placeholder'>物品类型</View>
                          <View className='kb-navigator__value'>{data.goods_name}</View>
                        </View>
                      </View>

                      {dynamicGoodsWeight.isShow && (
                        <View className='at-col'>
                          <View className='item-content item-extra__line kb-spacing-md-r'>
                            <View className='at-row at-row__align--center'>
                              <View className='item-title'>重量</View>
                              <AtInput
                                placeholderClass='placeholder'
                                cursor={-1}
                                cursorSpacing={20}
                                className='kb-input-center'
                                placeholder='选填'
                                maxLength={20}
                                value={data.goods_weight}
                                type='digit'
                                onChange={this.handleDeobunceChange.bind(this, 'goods_weight')}
                              />
                              <View className='item-desc'>kg</View>
                            </View>
                          </View>
                        </View>
                      )}
                    </View>
                    <View className='kb-form__item'>
                      <View className='item-title'>{dynamicGoodsRemark.label || '备注'}</View>
                      <View className='item-content item-content__right'>
                        <AtInput
                          placeholderClass='placeholder'
                          cursor={-1}
                          cursorSpacing={20}
                          placeholder={dynamicGoodsRemark.placeholder || '选填，不超过20字'}
                          maxLength={dynamicGoodsRemark.maxLength || 20}
                          value={data.goods_remark}
                          onChange={this.onChange_form.bind(this, 'goods_remark')}
                        />
                      </View>
                    </View>
                  </View>
                  {mode === 'order' && (
                    <View className='kb-form'>
                      <View className='kb-form__item'>
                        {(brands.length >= 2 || dynamicBrand.value) && (
                          <View className='item-content at-col'>
                            <View
                              className={brandCls}
                              hoverClass='kb-hover'
                              onClick={this.onPackageInfoAction.bind(this, brandAct, 'brands')}
                            >
                              <View className='kb-navigator__placeholder'>快递品牌</View>
                              <View className='kb-navigator__value'>
                                {dynamicBrand.desc || brandLabel}
                              </View>
                            </View>
                            {brandBars && brandBars.length ? (
                              <View className='kb-navigator__value--extra'>
                                {brandBars.map((item) => {
                                  const { key: itemKey } = item;
                                  return (
                                    <View className='extra-li' key={itemKey}>
                                      <KbCheckbox
                                        label={item.label}
                                        checked={data.brand_extra === itemKey}
                                        onChange={this.onChangeBrandExtra.bind(this, itemKey)}
                                        className='kb-color__black'
                                      />
                                    </View>
                                  );
                                })}
                              </View>
                            ) : null}
                          </View>
                        )}

                        {allowPay && dynamicOrderPrice.isShow && (
                          <View className='at-col'>
                            <View className={orderPriceCls}>
                              <View className='at-row at-row__align--center'>
                                <View className='item-title'>现付</View>
                                <AtInput
                                  placeholderClass='placeholder'
                                  cursor={-1}
                                  cursorSpacing={20}
                                  className='kb-input-center kb-clear__pseudo-ele-after'
                                  placeholder='选填'
                                  maxLength={20}
                                  value={data.orderPrice}
                                  type='digit'
                                  editable={editableMony}
                                  onChange={this.onChange_form.bind(this, 'orderPrice')}
                                />
                                <View className='item-desc'>元</View>
                              </View>
                            </View>
                          </View>
                        )}
                      </View>
                      <KbService
                        onChange={this.handleServiceChange}
                        actionRef={this.serviceRef}
                        relationInfo={relationInfo}
                        serviceConfig={servicesConfig}
                        brand={data.brand}
                        dynamicService={dynamicService}
                        reckonFee={placeOrderConfig ? reckonFee : false}
                        serviceClear={serviceClear}
                      />
                      {allowPay && dynamicCard.isShow && (
                        <KbCardBar data={relationInfo} action='buy' />
                      )}
                    </View>
                  )}
                </Fragment>
              )}
            </View>
          </KbDownContainer>
        </View>

        <AtFloatLayout
          isOpened={!!isOpened}
          onClose={this.onClose}
          className='kb-extra-info-common'
        >
          {!!isOpened && (
            <Fragment>
              <View className='kb-float-layout__bars'>
                <View
                  className='layout-bars__cancel'
                  hoverClass='kb-hover'
                  onClick={this.onPackageInfoAction.bind(this, 'cancel')}
                >
                  取消
                </View>
                <View className='layout-bars__title'>
                  {isOpenedValue === 'packages' ? '物品类型' : '选择快递品牌'}
                </View>
                <View
                  className='layout-bars__confirm'
                  hoverClass='kb-hover'
                  onClick={this.onPackageInfoAction.bind(this, 'confirm')}
                >
                  确定
                </View>
              </View>
              {hasList ? (
                <View className='kb-package'>
                  {isOpenedValue === 'packages' && servicesConfig.isFresh && (
                    <View className='kb-label'>普通包裹</View>
                  )}
                  <KbListBox list={list} onChange={this.handleSelectChange} selectted={selectted} />
                  {isOpenedValue === 'packages' && servicesConfig.isFresh && (
                    <Fragment>
                      <View className='kb-label'>
                        生鲜物品
                        <AtIcon
                          prefixClass='kb-icon'
                          hoverClass='kb-hover'
                          value='help2'
                          onClick={this.handleFreshTip}
                          className='kb-icon-size__base kb-color__grey kb-align-bottom'
                        />
                      </View>
                      <KbListBox
                        list={freshList}
                        onChange={this.handleSelectChange}
                        selectted={selectted}
                      />
                    </Fragment>
                  )}
                  {this.manual === selectted && (
                    <View className='kb-package-input'>
                      <AtInput
                        placeholderClass='placeholder'
                        focus
                        value={package_info_value}
                        cursor={-1}
                        cursorSpacing={20}
                        border={false}
                        maxLength={10}
                        placeholder={`请输入物品信息（最多${package_info_max}个字）`}
                        className='kb-input--without-background'
                        onChange={this.onPackageInfoAction.bind(this, 'input')}
                        onBlur={this.onPackageInfoAction.bind(this, 'confirm')}
                      />
                    </View>
                  )}
                </View>
              ) : loading ? (
                <KbLoader />
              ) : (
                <View className='kb-extra-info__empty'>
                  <KbEmpty
                    size='small'
                    description={
                      isOpenedValue === 'brands' ? '无可用品牌，联系管理员设置' : '暂无数据'
                    }
                  />
                </View>
              )}
            </Fragment>
          )}
        </AtFloatLayout>
        <AtFloatLayout
          isOpened={!!isOpenedTimer}
          onClose={this.handleOpenTimer.bind(this, false)}
          className='kb-extra-info-common'
        >
          <View className='kb-float-layout__bars'>
            <View className='layout-bars__title'>上门时间</View>
          </View>
          {!!isOpenedTimer && (
            <View className='kb-float-layout-wrap'>
              <View className='kb-spacing-lg-lr kb-spacing-xl-tb kb-float-layout--selector'>
                <View className='kb-float-layout--selector-item'>
                  {timerDay.map((item, index) => {
                    const { checked } = item;
                    const eligible = eligibleTimer(
                      index,
                      [servicesConfig.reserve_start_time, servicesConfig.reserve_end_time],
                      'day',
                    );
                    const itemCls = classNames(
                      'kb-spacing-lg-tb kb-spacing-xxl-r kb-text__r',
                      `kb-background__${checked ? 'white' : 'grey'}`,
                      {
                        'kb-color__brand': checked,
                        'kb-timer-disabled': !eligible,
                      },
                    );
                    return (
                      <View
                        hoverClass={eligible ? 'kb-hover' : ''}
                        onClick={this.handleSelectTimer.bind(
                          this,
                          index,
                          eligible ? 'day' : 'disabled',
                        )}
                        className={itemCls}
                      >
                        {item.label}
                      </View>
                    );
                  })}
                </View>
                <View className='kb-spacing-lg-lr  kb-float-layout--selector-item'>
                  <KbScrollView scrollY className='kb-scrollview'>
                    {timerSection[checkedDayIndex]
                      ? timerSection[checkedDayIndex].map((item) => {
                          const checked = item.value === data.reserve_time;
                          const eligible = eligibleTimer(item.value, [
                            servicesConfig.reserve_start_time,
                            servicesConfig.reserve_end_time,
                          ]);
                          const itemCls = classNames('kb-spacing-lg-b kb-spacing-xxl-l', {
                            'kb-timer-chekced': checked,
                            'kb-timer-disabled': !eligible,
                          });
                          return (
                            <View
                              hoverClass={eligible ? 'kb-hover' : ''}
                              onClick={this.handleSelectTimer.bind(
                                this,
                                item,
                                eligible ? 'time' : 'disabled',
                              )}
                              className={itemCls}
                            >
                              {item.label}
                              {checked && (
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='right'
                                  className='kb-icon-size__base kb-color__brand kb-spacing-lg-l kb-spacing-sm-b'
                                />
                              )}
                            </View>
                          );
                        })
                      : ''}
                  </KbScrollView>
                </View>
              </View>
            </View>
          )}
        </AtFloatLayout>
      </Fragment>
    );
  }
}

export default Index;
