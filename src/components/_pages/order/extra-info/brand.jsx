/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbListBox from '@/components/_pages/order/listbox';
import KbCheckbox from '@base/components/checkbox';
import KbEmpty from '@base/components/empty';
import KbLoader from '@base/components/loader';
import KbTimerPicker from '@base/components/time-picker';
import { extendMemo } from '@base/components/_utils';
import Form from '@base/utils/form';
import request from '@base/utils/request';
import { debounce, noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import dayjs from 'dayjs';
import isArray from 'lodash/isArray';
import isBoolean from 'lodash/isBoolean';
import isEmpty from 'lodash/isEmpty';
import isObject from 'lodash/isObject';
import { AtFloatLayout, AtTabs, AtTabsPane } from 'taro-ui';
import { brandKeys, fixDynamicFormsData, getFormItem } from '../_utils';
import './brand.scss';
import './common.scss';
import { isFormLocked, memoObject, sendOptions } from './_utils';

@connect(
  ({ global }) => ({
    brandMap: global.brands,
    loginData: global.loginData,
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  static defaultProps = {
    rootCls: '',
    data: {},
    storageKey: 'extraInfo',
    isOpened: false,
    dynamicForms: fixDynamicFormsData(),
    onChange: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor(props) {
    super(props);
    this.packageInfoKey = 'packageInfo';
    this.allowUpBrand = true;
    this.state = {
      day: dayjs().format('YYYY-MM-DD'),
      sendModeCurrent: 0,
      loading: false,
      form: { data: {}, disabled: true },
      brands: [],
      selectted: '',
      isOpened: false,
      isOpenedTimer: false,
      _servicesConfig: {}, //增值服务配置
    };
    this.handleSelectChange = this.handleSelectChange.bind(this);
    this.getPackageInfo = debounce(this.getPackageInfo, 100, {
      trailing: true,
    });
    this.updateServicesConfig = debounce(this.updateServicesConfig, 100, {
      trailing: true,
    });
    this.onPackageInfoAction = debounce(this.onPackageInfoAction, 100, {
      trailing: true,
    });

    this.handleSendTabsChange = this.handleSendTabsChange.bind(this);
  }
  handleSendTabsChange = (current) => {
    const { key } = sendOptions[current];
    let state = {};
    key === 'ship'
      ? ((state.shipper_zipcode_del = ''), (state.reserve_time = ''))
      : (state.shipper_zipcode_del = '1');
    this.formIns.update(state);
    this.setState({
      sendModeCurrent: current,
    });
  };
  handleOpenTimer = (bool) => {
    this.setState({
      isOpenedTimer: isBoolean(bool) ? bool : true,
    });
  };
  // 拉取驿站增值服务配置项
  updateServicesConfig(data) {
    let state = {};
    state._servicesConfig = { ...(isObject(data) ? data : {}) };
    this.setState(state);
  }
  componentDidMount() {
    this.props.dispatchGet();
    this.createForm(() => {
      const { relationInfo, dynamicForms, serviceConfig } = this.props;
      if (relationInfo) {
        // 动态表单对象中设置了品牌的话不拉取品牌
        this.updateServicesConfig(serviceConfig);
        if (dynamicForms['brand'] && dynamicForms['brand'].value) return;
        // 补偿获取关系后，重新拉取品牌列表
        this.getPackageInfo(relationInfo);
      }
    });
  }
  componentDidUpdate(preProps) {
    const {
      data: nextData,
      relationInfo: nextRelationInfo,
      dynamicForms: nextDynamicForms,
      serviceConfig: nextServiceConfig,
    } = this.props;
    const { data, relationInfo, dynamicForms, serviceConfig } = preProps;
    // 更新信息
    if (nextData && nextData !== data) {
      const { clean = false, pay_status, ...nextDataRest } = nextData;
      if (clean) {
        this.formIns.clean();
        return;
      }
      const { form: { data: formData } = {} } = this.state;
      const modify = memoObject(formData, nextDataRest);
      if (modify) {
        const { shipper_zipcode_del } = modify;
        let allowUpBrand = 'brand' in modify ? false : true;
        allowUpBrand != this.allowUpBrand && (this.allowUpBrand = allowUpBrand);
        this.formIns.update(modify);
        !shipper_zipcode_del && this.handleSendTabsChange(0);
      }
    }
    if (nextRelationInfo && nextRelationInfo !== relationInfo) {
      // 补偿获取关系后，重新拉取品牌列表
      if (
        !nextDynamicForms ||
        (nextDynamicForms && (!nextDynamicForms['brand'] || !nextDynamicForms['brand'].value))
      ) {
        this.getPackageInfo(nextRelationInfo);
      }
    }
    if (nextServiceConfig !== serviceConfig) {
      this.updateServicesConfig(nextServiceConfig);
    }
    if (nextDynamicForms !== dynamicForms && nextDynamicForms) {
      this.triggerWhetherFormLocked(nextDynamicForms);
      return;
    }
  }
  // 是否触发表单锁定为自定义内容
  triggerWhetherFormLocked = (dynamicForms) => {
    return isFormLocked(dynamicForms, (data) => this.formIns.update(data));
  };

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (cb) => {
    const form = getFormItem({
      keys: [...brandKeys],
      data: {},
    });
    const { storageKey } = this.props;
    this.formIns = new Form(
      {
        form,
        storageKey,
        onUpdate: (data) => {
          const { onChange } = this.props;
          const { nextData } = data || {};
          onChange(data);
          this.resetCleanForm(nextData);
        },
        onReady: () => {
          cb && cb();
        },
      },
      this,
    );
  };

  resetCleanForm(formData = {}) {
    const { brand, product_type } = formData || {};
    this.formIns.resetForm(
      {
        brand: {
          clean: brand,
        },
        product_type: {
          clean: product_type,
        },
      },
      true,
    );
  }

  // 获取物品类型列表、快递品牌
  getPackageInfo = (relationInfo = this.props.relationInfo) => {
    const { relation_id, dak_id, courier_id = dak_id, customer, smjData = {} } = relationInfo || {};
    const { relation_info = {} } = smjData || {};
    const { is_authorized } = relation_info;
    const ship_code = is_authorized == 0 ? {} : { ship_code: relation_info.relation_id };
    const customer_id = customer && customer.id;
    if (!relation_id && !courier_id) {
      this.formIns.update({
        brand: '',
        product_type: '',
      });
      return;
    }
    request(
      {
        url:
          process.env.MODE_ENV === 'wkd'
            ? '/v1/WeApp/getBrandSetting'
            : '/api/weixin/mini/minpost/relation/supportBrand',
        toastLoading: false,
        loadingStatusKey: 'loading',
        data:
          process.env.MODE_ENV === 'wkd'
            ? { courier_id, customer_id, ...ship_code }
            : { relation_id },
        onThen: ({ data }) => {
          let brands = [];
          if (isArray(data) && data.length > 0) {
            brands = data.map((item) => {
              let brandItem = {
                key: item.brand,
                label: item.brand_name,
              };
              brandItem.product_type = [];
              if (!isEmpty(item.product_type)) {
                let product_type_item = [];
                Object.keys(item.product_type).map((i) => {
                  product_type_item.push({
                    key: i,
                    label: item.product_type[i],
                  });
                });
                brandItem.product_type = product_type_item;
              }
              return brandItem;
            });
          }
          Taro.kbSetGlobalData('extraInfo_brandInfo', brands);
          this.setState(
            {
              brands,
            },
            () => {
              // 默认下单品牌和服务类型处理逻辑
              const {
                form: { data },
              } = this.state;
              if (this.allowUpBrand) {
                if (brands.length > 0) {
                  // 缓存上次的品牌和服务类型仍然有效
                  const lastIndex = brands.findIndex((item) => item.key == data.brand);
                  if (lastIndex > -1) {
                    const { product_type = [] } = brands[lastIndex];
                    const lastTypeIndex =
                      product_type && product_type.length > 0
                        ? product_type.findIndex((item) => item.label == data.product_type)
                        : -1;
                    // 移除失效的服务类型
                    if (lastTypeIndex < 0) {
                      this.formIns.update({
                        product_type: '',
                      });
                    }
                    return;
                  }
                  const { key: firstBrand } = brands[0];
                  const {
                    key: brand = '',
                    label: brand_name,
                    product_type,
                  } = this.filterBrands(firstBrand);

                  const { label } =
                    product_type.find((item) => item.label == data.product_type) || {};

                  if (brand === data.brand) return;
                  this.formIns.update({
                    brand,
                    brand_name,
                    product_type: label,
                  });
                } else {
                  this.formIns.update({
                    brand: '',
                    product_type: '',
                  });
                }
              }
            },
          );
        },
      },
      this,
    );
  };

  // 匹配当前的快递品牌信息
  filterBrands = (by) => {
    const {
      brands,
      form: { data },
    } = this.state;
    const by_ = by || data.brand;
    const [{ key = '', bars = null, label = '', product_type = [] } = {}] = brands.filter(
      (item) => item.key === by_ || item.label === by_,
    );
    const [{ key: brand_extra = '' } = {}] = bars || [];
    return {
      label,
      key,
      bars,
      brand_extra: bars ? data.brand_extra || brand_extra : '',
      product_type,
    };
  };

  // 关闭品牌选择弹窗
  handleBrandClose = () =>
    this.setState({
      isOpened: false,
    });

  // 选择快递品牌
  onPackageInfoAction = (key, value) => {
    let {
      selectted,
      form: {
        data: { brand = '' },
      },
    } = this.state;
    switch (key) {
      case 'disabled':
        return;
      case 'open':
        // 开启获取物品类型
        const { label: brandLabel } = this.filterBrands(brand);
        selectted = brandLabel;
        this.setState({
          selectted,
          isOpened: true,
        });
        break;

      case 'select':
        // 勾选物品类型
        const { label } = value;
        if (selectted === label) return;
        this.setState(
          {
            selectted: label,
          },
          () => {
            this.onPackageInfoAction('confirm');
          },
        );
        break;
      case 'cancel':
        // 取消物品选择
        this.handleBrandClose();
        break;
      case 'confirm':
        // 确认物品、快递品牌选择
        let extra = null;
        // 选择品牌
        const { key: brandKey, brand_extra, label: brand_name } = this.filterBrands(selectted);
        selectted = brandKey;
        extra = {
          brand_name,
          brand_extra,
        };
        if (!selectted) {
          return;
        }

        this.formIns.update({
          brand: selectted,
          product_type: '',
          ...extra,
        });

        break;
      default:
        break;
    }
  };
  handleSelectChange = (item) => {
    this.onPackageInfoAction('select', item);
  };
  handleProductTypeChange = (item) => {
    const { data: { product_type } = {} } = this.state.form;
    this.onChange_form('product_type', {
      target: { value: product_type == item.label ? '' : item.label },
      type: 'change',
    });
  };
  handleSelectTimer = (change) => {
    const { value, type } = change || {};
    switch (type) {
      case 'activity':
        this.setState({ timerCheckedActivity: value });
        break;
      case 'day':
        this.setState({
          day: value,
        });
        break;
      case 'time':
        this.formIns.update({
          reserve_time: value,
        });
        break;
    }
  };

  // 品牌额外信息
  onChangeBrandExtra = (key) => {
    this.formIns.update({
      brand_extra: key,
    });
  };

  render() {
    const {
      loading,
      isOpened,
      selectted,
      _servicesConfig,
      form: { data },
      brands,
      isOpenedTimer,
      timerCheckedActivity,
      sendModeCurrent,
      day,
    } = this.state;
    const { dynamicForms, brandMap, rootCls } = this.props;
    const list = this.state.brands || [];
    const hasList = !!list.length;
    const { label: brandLabel, bars: brandBars, product_type } = this.filterBrands();

    let { brand: dynamicBrand = {}, sendWay: dynamicSendWay = {} } = dynamicForms || {};
    const { isToDoor, allowReserveTime } = _servicesConfig;
    const brandCls = classNames('kb-brand-item', {
      'kb-clear__pseudo-ele-after': !!dynamicBrand.locked,
    });
    const brandWrapCls = classNames('at-col', 'kb-separator-top', {
      'kb-separator-bottom': !!_servicesConfig.isToDoor,
    });

    const brandAct = dynamicBrand.locked ? 'disabled' : 'open';
    const dynamicBrandName =
      dynamicBrand.value && brandMap && brandMap[dynamicBrand.value]
        ? brandMap[dynamicBrand.value].name
        : '';
    const brand_name_show = dynamicBrandName || brandLabel;
    const moreBrands = brands.length > 1 || (product_type && product_type.length > 0);

    const extraCommonCls = process.env.MODE_ENV === 'yz' ? 'kb-extra-info-common' : '';
    const mergeRootCls = classNames('kb-extra-info kb-brand-info', rootCls, extraCommonCls);

    return (
      <Fragment>
        <View className={mergeRootCls}>
          <View className='kb-form__group'>
            <Fragment>
              <View className='kb-form'>
                <View className='kb-brand'>
                  {(brands.length > 0 || dynamicBrand.value) && (
                    <View className={brandWrapCls}>
                      <View
                        className={brandCls}
                        onClick={
                          moreBrands
                            ? this.onPackageInfoAction.bind(this, brandAct, 'brands')
                            : noop
                        }
                        hoverClass={moreBrands ? 'kb-hover' : ''}
                      >
                        <View className='kb-color__greyer'>快递品牌</View>
                        <View
                          className={classNames(
                            'kb-navigator kb-navigator-noborder kb-brand-content',
                            {
                              'kb-navigator-noArrow': !moreBrands,
                            },
                          )}
                        >
                          <View>{brand_name_show}</View>
                          {brand_name_show && data.product_type && (
                            <View className='kb-brand-product kb-size__xs'>
                              {data.product_type}
                            </View>
                          )}
                        </View>
                      </View>
                      {brandBars && brandBars.length ? (
                        <View className='kb-navigator__value--extra'>
                          {brandBars.map((item) => {
                            const { key: itemKey } = item;
                            return (
                              <View className='extra-li' key={itemKey}>
                                <KbCheckbox
                                  label={item.label}
                                  checked={data.brand_extra === itemKey}
                                  onChange={this.onChangeBrandExtra.bind(this, itemKey)}
                                  className='kb-color__black'
                                />
                              </View>
                            );
                          })}
                        </View>
                      ) : null}
                    </View>
                  )}
                  {process.env.MODE_ENV !== 'wkd' &&
                    dynamicSendWay &&
                    dynamicSendWay.isShow &&
                    isToDoor && (
                      <View className='kb-brand-item' onClick={this.handleOpenTimer}>
                        <View className='kb-color__greyer'>寄件方式</View>
                        <View className='kb-brand-content kb-navigator ' hoverClass='kb-hover'>
                          {data.shipper_zipcode_del === ''
                            ? '去驿站寄'
                            : timerCheckedActivity
                            ? timerCheckedActivity
                            : data.shipper_zipcode_del == 1
                            ? '上门取件'
                            : ''}
                        </View>
                      </View>
                    )}
                </View>
              </View>
            </Fragment>
          </View>
        </View>
        <AtFloatLayout
          isOpened={!!isOpened}
          onClose={this.handleBrandClose}
          className={extraCommonCls}
        >
          {!!isOpened && (
            <Fragment>
              <View className='kb-float-layout__bars'>
                <View className='layout-bars__title'>选择快递品牌</View>
                <View
                  className='layout-bars__confirm'
                  hoverClass='kb-hover'
                  onClick={this.onPackageInfoAction.bind(this, 'cancel')}
                >
                  确定
                </View>
              </View>

              {hasList ? (
                <View className='kb-package'>
                  <KbListBox
                    itemSize='lg'
                    list={list}
                    onChange={this.handleSelectChange}
                    selectted={selectted}
                    circle={false}
                    selectedActive='extra'
                  />
                </View>
              ) : loading ? (
                <KbLoader />
              ) : (
                <View className='kb-extra-info__empty'>
                  <KbEmpty size='small' description='无可用品牌，联系管理员设置' />
                </View>
              )}
              {selectted && product_type && product_type.length > 0 && (
                <View className=' kb-brand-type'>
                  <View className='kb-size__base kb-color__grey'>品牌类型</View>
                  <KbListBox
                    itemSize='mini'
                    list={product_type}
                    onChange={this.handleProductTypeChange}
                    selectted={data.product_type}
                  />
                </View>
              )}
            </Fragment>
          )}
        </AtFloatLayout>
        <AtFloatLayout
          isOpened={!!isOpenedTimer}
          onClose={this.handleOpenTimer.bind(this, false)}
          className={extraCommonCls}
        >
          <View className='kb-float-layout__bars'>
            <View className='layout-bars__title'>寄件方式</View>
            <View
              className='layout-bars__confirm'
              hoverClass='kb-hover'
              onClick={this.handleOpenTimer.bind(this, false)}
            >
              确定
            </View>
          </View>

          {!!isOpenedTimer && (
            <View className='kb-float-layout-wrap'>
              <AtTabs
                tabList={sendOptions}
                current={sendModeCurrent}
                className='kb-tabs-button kb-tabs-button__header-around'
                onClick={this.handleSendTabsChange.bind(this)}
              >
                <AtTabsPane current={sendModeCurrent} index={0}>
                  <View className='kb-ship-wrap'>
                    <View className='kb-color__grey kb-size__sm kb-spacing-sm kb-tip'>
                      订单提交后，请前往驿站将包裹交由工作人员处理
                    </View>
                  </View>
                </AtTabsPane>
                {allowReserveTime && (
                  <AtTabsPane current={sendModeCurrent} index={1}>
                    <KbTimerPicker
                      section={[
                        _servicesConfig.reserve_start_time,
                        _servicesConfig.reserve_end_time,
                      ]}
                      onChange={this.handleSelectTimer}
                      day={day}
                      time={data.reserve_time}
                    />
                  </AtTabsPane>
                )}
              </AtTabs>
            </View>
          )}
        </AtFloatLayout>
      </Fragment>
    );
  }
}

export default extendMemo(Index);
