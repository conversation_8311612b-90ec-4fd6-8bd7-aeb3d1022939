/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbListBox from '@/components/_pages/order/listbox';
import KbButton from '@base/components/button';
import KbImagePicker from '@base/components/image-picker';
import KbTextarea from '@base/components/textarea';
import Form from '@base/utils/form';
import request from '@base/utils/request';
import { debounce, getStorageSync, noop, setStorageSync } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro, { Component, Fragment } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import numeral from 'numeral';
import { AtIcon, AtInput } from 'taro-ui';
import { fixDynamicFormsData, freshList, getFormItem, goodsKeys } from '../_utils';
import './common.scss';
import { isFormLocked, remarkMaps } from './_utils';

class Index extends Component {
  static defaultProps = {
    data: {},
    storageKey: 'extraInfo',
    isOpenedValue: '',
    dynamicForms: fixDynamicFormsData(),
    onChange: noop,
  };
  static options = {
    addGlobalClass: true,
  };
  constructor(props) {
    super(props);
    this.manual = '自定义';
    this.packageInfoKey = 'packageInfo';
    this.weightRange = [1, 99];
    this.state = {
      form: { data: {}, disabled: true },
      packages: [],
      selectted: '',
      package_info_value: '',
      package_info_max: 4,
      servicesConfig: {},
    };
    this.imagePickerApi = {
      url:
        process.env.MODE_ENV == 'wkd'
          ? '/v1/WeApp/uploadAttachments'
          : '/api/weixin/mini/realname/parse/uploadPackageImages',
      data:
        process.env.MODE_ENV == 'wkd'
          ? {
              type: 'check',
            }
          : {},
      formatRequest: (req) => {
        const { filePath } = req;
        req.filePath = filePath[0];
        return req;
      },
      formatResponse: ({ data, code, msg, ...rest }) => {
        const { package_url, file_path } = data;
        let img = process.env.MODE_ENV == 'wkd' ? file_path : package_url;
        return {
          ...rest,
          data: [
            {
              code,
              data: {
                img,
              },
              msg,
            },
          ],
        };
      },
    };
    this.handleSelectChange = this.handleSelectChange.bind(this);
    this.onPackageInfoAction = debounce(this.onPackageInfoAction, 100, {
      trailing: true,
    });
    this.handleDeobunceChange = debounce(this.handleDeobunceChange, 300, {
      trailing: true,
    });
  }

  componentDidMount() {
    const { formRef, dynamicForms, serviceConfig } = this.props;

    this.createForm(() => {
      const { form } = this.state;
      formRef.current = {
        form,
      };
      this.setPackageInfo();
      serviceConfig && this.updateServiceConfig(serviceConfig);
      if (dynamicForms) {
        this.triggerWhetherFormLocked(dynamicForms);
        return;
      }
    });
  }
  componentDidUpdate(preProps) {
    const {
      data: nextData,
      dynamicForms: nextDynamicForms,
      serviceConfig: nextServiceConfig,
    } = this.props;
    const { data, dynamicForms, serviceConfig } = preProps;
    // 更新信息
    if (nextData && nextData !== data) {
      const { clean = false, pay_status, ...nextDataRest } = nextData;
      if (clean) {
        this.formIns.clean();
        return;
      }
      this.formIns.update(nextDataRest);
    }
    if (nextServiceConfig !== serviceConfig) this.updateServiceConfig(nextServiceConfig);
    if (nextDynamicForms !== dynamicForms && nextDynamicForms) {
      this.triggerWhetherFormLocked(nextDynamicForms);
      return;
    }
  }
  updateServiceConfig = (serviceConfig) => {
    this.setState({
      servicesConfig: serviceConfig,
    });
  };
  // 是否触发表单锁定为自定义内容
  triggerWhetherFormLocked = (dynamicForms) => {
    const { goods_name } = dynamicForms;
    goods_name.value && this.setPackageInfo(() => {}, [goods_name.value]);
    return isFormLocked(dynamicForms, (data) => this.formIns.update(data));
  };

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (cb) => {
    const form = getFormItem({
      keys: [...goodsKeys],
      data: { goods_name: '日用品', goods_weight: 1, package_images: [] },
      merge: {
        goods_name: { required: true },
      },
    });
    const { storageKey } = this.props;
    this.formIns = new Form(
      {
        form,
        storageKey,
        onChange: ({ value, name, type }) => {
          const weightRange = this.weightRange;
          if (name === 'goods_weight') {
            let val = numeral(
              Math.min(Math.max(weightRange[0], numeral(value).value()), weightRange[1]),
            );
            if (type == 'blur') {
              return value ? val.format('0.00') : '';
            } else {
              return value ? val.value() : '';
            }
          }
        },
        onUpdate: (data) => {
          const {
            data: { goods_weight: changeWeight },
            formEvent,
          } = data;
          const { onChange } = this.props;
          if (formEvent === 'blur' && changeWeight) {
            return;
          }
          onChange(data);
        },
        onReady: () => {
          cb && cb();
        },
      },
      this,
    );
  };

  // 拉取远程设定的物品类型列表
  requestGoodsList = () => {
    return new Promise((resolve, reject) => {
      const defaultGoodsList = ['日用品', '数码产品', '衣物', '食物', '文件', '鞋靴'];
      if (process.env.MODE_ENV == 'wkd') {
        let url = '/v1/GrabOrder/packageInfo',
          reqData = {},
          GoodsGlobalDataKey = this.packageInfoKey,
          list = [];
        list = Taro.kbGetGlobalData(GoodsGlobalDataKey);
        if (list && list.length > 0) {
          resolve(list);
          return;
        }
        request({
          url,
          toastLoading: false,
          data: reqData,
          onThen: ({ code, data }) => {
            if (code == 0 && data) {
              list = data;
              resolve(list);
              Taro.kbSetGlobalData(GoodsGlobalDataKey, list);
            } else {
              reject();
            }
          },
        });
      } else {
        resolve(defaultGoodsList);
      }
    });
  };

  // 获取本地物品缓存
  getPackageInfoStorage = (then) => {
    const data = getStorageSync(this.packageInfoKey).data;
    then(isArray(data) ? data : []);
  };
  setPackageInfo = (then, packages = []) => {
    // 物品类型使用前端配置
    this.requestGoodsList().then((GoodList = []) => {
      const data = [...GoodList, ...packages];
      this.getPackageInfoStorage((list) => {
        const packages = [...data.filter((item) => item !== '其它'), ...list].map((item, index) => {
          return {
            label: item,
            key: `i_${index}`,
          };
        });
        packages.push({
          key: 'other',
          label: this.manual,
        });
        this.setState({
          packages,
        });
        then && then(packages);
      });
    });
  };
  // 设置本地物品缓存
  setPackageInfoStorage = (value) => {
    const { packages } = this.state;
    if (!value || packages.findIndex((item) => item.label === value) >= 0) {
      return;
    }
    this.getPackageInfoStorage((list) => {
      list.unshift(value);
      const data = list.slice(0, 6);
      setStorageSync(this.packageInfoKey, data);
      this.setPackageInfo();
    });
  };

  // 匹配当前的品牌
  filterBrands = (by) => {
    const {
      brands,
      form: { data },
    } = this.state;
    const by_ = by || data.brand;
    const [{ key = '', bars = null, label = '' } = {}] = brands.filter(
      (item) => item.key === by_ || item.label === by_,
    );
    const [{ key: brand_extra = '' } = {}] = bars || [];
    return {
      label,
      key,
      bars,
      brand_extra: bars ? data.brand_extra || brand_extra : '',
    };
  };

  // 选择物品类型、或者快递品牌
  onPackageInfoAction = (key, value) => {
    let { package_info_max, selectted, package_info_value } = this.state;
    switch (key) {
      case 'disabled':
        return;
      case 'input':
        // 自定义物品类型
        this.setState({
          package_info_value: value,
        });
        break;
      case 'select':
        // 勾选物品类型
        const { label } = value;
        if (selectted === label) return;
        this.setState(
          {
            selectted: label,
          },
          () => {
            if (label !== this.manual) {
              this.onPackageInfoAction('confirm');
            }
          },
        );
        break;
      case 'confirm':
        // 确认物品
        let extra = null;
        if (selectted === this.manual) {
          if (package_info_value.length > package_info_max) {
            package_info_value = `${package_info_value}`.substring(0, package_info_max);
          }
          selectted = package_info_value;
          // 缓存
          this.setPackageInfoStorage(selectted);
        }

        if (!selectted) {
          return;
        }
        this.setState({
          selectted,
        });
        this.formIns.update({
          goods_name: selectted,
          ...extra,
        });

        break;
      default:
        break;
    }
  };
  handleSelectChange = (item) => {
    this.onPackageInfoAction('select', item);
  };
  handleRemarkSelect = (item) => {
    this.onChange_form('goods_remark', {
      target: { value: item.label },
      type: 'change',
    });
  };

  handleFreshTip = () => {
    Taro.kbToast({
      text: '生鲜订单需要专用生鲜单号进行打印',
    });
  };

  handleDeobunceChange = (key, value) => {
    this.onChange_form(key, { target: { value }, type: 'change' });
  };
  triggerPicturesChange = (files) => {
    this.onChange_form('package_images', {
      target: { value: files },
      type: 'change',
    });
  };
  render() {
    const {
      selectted,
      package_info_max,
      package_info_value,
      servicesConfig,
      form: { data },
      packages,
    } = this.state;
    const { dynamicForms } = this.props;
    let {
      goods_name: dynamicGoodsName,
      goods_remark: dynamicGoodsRemark,
      goods_weight: dynamicGoodsWeight,
    } = dynamicForms || {};

    const goodsNameAct = dynamicGoodsName.locked ? 'disabled' : 'select';

    return (
      <Fragment>
        <View className='kb-extra-info kb-extra-info-common'>
          <View className='kb-form__group'>
            <Fragment>
              <View>
                <View className='item-content'>
                  <View>
                    <View className='kb-form-tag'>物品类型</View>
                    <View className='kb-package'>
                      <KbListBox
                        list={packages}
                        className='kb-spacing-lg-b'
                        onChange={this.onPackageInfoAction.bind(this, goodsNameAct)}
                        selectted={data.goods_name}
                      />
                      {servicesConfig.isFresh && (
                        <Fragment>
                          <View className='kb-label'>
                            生鲜物品
                            <AtIcon
                              prefixClass='kb-icon'
                              hoverClass='kb-hover'
                              value='help2'
                              onClick={this.handleFreshTip}
                              className='kb-icon-size__base kb-color__grey kb-align-bottom'
                            />
                          </View>
                          <KbListBox
                            list={freshList}
                            onChange={this.onPackageInfoAction.bind(this, goodsNameAct)}
                            selectted={data.goods_name}
                          />
                        </Fragment>
                      )}
                      {this.manual === selectted && (
                        <View className='kb-package-input'>
                          <AtInput
                            placeholderClass='placeholder'
                            clear
                            value={package_info_value}
                            cursor={-1}
                            cursorSpacing={150}
                            border={false}
                            maxLength={10}
                            placeholder={`请输入物品信息（最多${package_info_max}个字）`}
                            className='kb-input--without-background'
                            onChange={this.onPackageInfoAction.bind(this, 'input')}
                          >
                            <KbButton
                              hoverClass='kb-hover'
                              size='mini'
                              type='primary'
                              onClick={this.onPackageInfoAction.bind(this, 'confirm')}
                            >
                              确定
                            </KbButton>
                          </AtInput>
                        </View>
                      )}
                    </View>
                  </View>
                </View>

                {dynamicGoodsWeight.isShow && (
                  <View className='item-extra__line kb-spacing-md-r'>
                    <View className='kb-form-tag'>物品重量</View>
                    <View className='at-row at-row-align--center kb-margin-lg-tb'>
                      <View className='kb-flex-grow-all kb-line-input-number kb-color__grey kb-size__sm'>
                        注：实际重量以快递员确定为准
                      </View>
                      <AtInput
                        placeholderClass='placeholder'
                        Number
                        className='kb-input-center '
                        step={1}
                        value={data.goods_weight}
                        type='digit'
                        onChange={this.handleDeobunceChange.bind(this, 'goods_weight')}
                      />
                      <View className='item-desc kb-spacing-sm-l kb-line-input-number'>kg</View>
                    </View>
                  </View>
                )}
              </View>
              <View>
                <View className='kb-form-tag'>{dynamicGoodsRemark.label || '填写备注'}</View>
                <View className='item-content item-content__right'>
                  <KbListBox
                    list={remarkMaps}
                    selectted={data.goods_remark}
                    onChange={this.handleRemarkSelect}
                    itemSize='mini'
                    itemClass='kb-background-grey'
                  />
                  <KbTextarea
                    placeholder={dynamicGoodsRemark.placeholder || '补充说明～'}
                    className='kb-spacing-lg kb-margin-lg-t kb-background-grey'
                    maxLength={dynamicGoodsRemark.maxLength || 20}
                    value={data.goods_remark}
                    onChange={this.onChange_form.bind(this, 'goods_remark')}
                  />
                </View>
              </View>
              <View className='kb-spacing-lg-tb'>
                <View className='kb-form-tag kb-spacing-sm-b'>包裹图片</View>
                <KbImagePicker
                  count={3}
                  onChange={this.triggerPicturesChange}
                  api={this.imagePickerApi}
                  files={data.package_images}
                  path={
                    process.env.MODE_ENV == 'wkd' ? 'https://upload.kuaidihelp.com/graborder/' : ''
                  }
                  custom
                >
                  <View className='kb-image-add' />
                </KbImagePicker>
              </View>
            </Fragment>
          </View>
        </View>
      </Fragment>
    );
  }
}

export default Index;
