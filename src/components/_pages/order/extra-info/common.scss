/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$address-placeholder-height: 150px;
$address-border-radius: $border-radius-lg;

.kb-extra-info {
  overflow: hidden;

  .info-bar {
    padding: $spacing-v-md 0;
    color: $color-grey-1;
    text-align: center;

    &.kb-hover {
      border-radius: $address-border-radius;
    }

    &::after,
    &__text {
      display: inline-block;
      vertical-align: middle;
    }

    &::after {
      width: 0;
      height: 0;
      margin-left: $spacing-h-md;
      border: 15px solid $color-brand;
      border-right-color: transparent;
      border-bottom: 0;
      border-left-color: transparent;
      content: '';
    }
  }

  .kb-form {
    &__item {
      .kb-navigator {
        margin-left: -1 * $spacing-v-md;
      }

      &--content {
        margin: $spacing-h-md;
        padding: $spacing-h-md;
      }
    }

    &__cutosm-item {
      margin-left: 1 * $spacing-v-md;
    }

    .item {
      &-extra__line {
        box-sizing: border-box;
        margin-left: 0;
        color: $color-grey-2;
      }

      &-content {
        &__right {
          text-align: right;
        }
      }
    }

    &-tag {
      position: relative;
      padding-left: $spacing-v-lg;

      &::before {
        position: absolute;
        top: 50%;
        left: 0;
        width: 8px;
        height: 8px;
        overflow: hidden;
        border: 5px solid $color-brand;
        border-radius: $border-radius-arc;
        transform: translateY(-50%);
        content: '';
      }
    }
  }

  &__empty {
    padding: $spacing-v-xl 0;
  }

  .info-bar,
  .kb-form {
    background-color: $color-white;
  }

  .info-bar {
    border-radius: $address-border-radius;
  }

  .kb-form {
    margin-bottom: $spacing-h-md;
    overflow: hidden;
    border-bottom-right-radius: $address-border-radius;
    border-bottom-left-radius: $address-border-radius;
  }
}

.kb-extra-info-common {
  .flex-glow-all {
    .item-desc {
      flex: 1;
      text-align: right;
    }

    padding-right: 20px;
  }

  .flex-all {
    flex: 1;
  }

  .kb-label {
    margin: $spacing-v-md 0 0 $spacing-v-lg;
    font-size: $font-size-sm;
  }

  .kb-full {
    height: 100%;
  }

  .kb-background {
    &__white {
      background-color: $color-white;
    }

    &__grey {
      background-color: $color-grey-7;
    }
  }

  .kb-text {
    &__r {
      text-align: right;
    }
  }

  .kb-spacing-xxl {
    &-r {
      padding-right: $spacing-h-xl * 2;
    }

    &-l {
      padding-left: $spacing-h-xl * 2;
    }
  }

  .kb-navigator__placeholder {
    display: block;
  }

  .kb-line-input-number {
    line-height: 50px;
  }

  .kb-flex {
    display: flex !important;

    &-grow-all {
      flex-grow: 1;
    }

    &-nowrap {
      flex-wrap: nowrap;
    }
  }

  .kb-package {
    padding: $spacing-v-md 2 * $spacing-v-md 2 * $spacing-h-md 0;

    &-input {
      margin: 0 $spacing-v-md $spacing-v-md;
      border: $border-lightest;
      border-radius: $border-radius-arc;

      .at-input {
        padding: $spacing-v-md 0;
      }
    }
  }

  .kb-brand {
    position: relative;
    width: auto !important;

    &-product {
      position: absolute;
      top: 0px;
      right: 24px;
      display: inline-block;
      padding: 2px 6px;
      overflow: hidden;
      color: $color-brand;
      color: $color-brand;
      border: $width-base solid $color-brand;
      border-radius: $border-radius-md;
    }

    .kb-navigator {
      padding-right: 0;
      background-color: transparent;

      &::after {
        text-align: right;
      }
    }

    &-type {
      padding: 0 40px 50px;
    }

    &-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 $spacing-h-md;
    }

    &-content {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .kb-separator {
    &-right,
    &-bottom {
      position: relative;
    }

    &-right {
      &::before {
        position: absolute;
        top: 50%;
        right: 0;
        z-index: 1;
        display: inline-block;
        width: 2px;
        height: 50px;
        background-color: #e6e6e6;
        transform: translateY(-50%);
        content: '';
      }
    }

    &-bottom {
      &::after {
        bottom: 0;
        left: 0;
        z-index: 1;
        display: block;
        margin: 0 20px;
        border-bottom: 1px solid #e6e6e6;
        content: '';
      }
    }

    &-top {
      &::before {
        z-index: 1;
        display: block;
        margin: 0 20px;
        border-bottom: 1px solid #e6e6e6;
        content: '';
      }
    }
  }

  .kb-ship-wrap {
    height: 180px;
    background-color: $color-white;
  }

  .kb-tip {
    position: relative;
    display: inline-block;
    margin-top: 40px;
    margin-left: 80px;
    background-color: $color-grey-8;
    border: $border-lightest;
    border-radius: $border-radius-md;

    &::before {
      position: absolute;
      top: -22px;
      left: 60px;
      display: block;
      width: 0;
      height: 0;
      border: 10px solid transparent;
      border-bottom-color: $color-grey-3 !important;
      content: '';
    }
  }

  .kb-image-add {
    position: relative;
    width: 120px;
    height: 120px;
    border: $border-lightest;

    &::after {
      position: absolute;
      top: 50%;
      left: 50%;
      display: block;
      color: $color-brand;
      font-size: 48px;
      transform: translate(-50%, -50%);
      content: '+';
    }
  }

  .kb-form-box {
    overflow: hidden;
    background-color: $color-white;
    border-radius: $border-radius-md;
  }

  .kb-input-center {
    .at-input-number__btn-subtract,
    .at-input-number__btn-add {
      height: 65px;
      line-height: 65px;
    }

    .at-input-number__input {
      height: 65px;
      line-height: 65px;
    }

    .at-input-number__btn-subtract::before,
    .at-input-number__btn-add::before {
      font-size: $font-size-base;
    }
  }

  .kb-line-height {
    line-height: 1;
  }
}
