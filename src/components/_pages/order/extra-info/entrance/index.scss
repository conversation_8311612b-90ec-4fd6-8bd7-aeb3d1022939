/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-entrance {
  overflow: hidden;
  &-flat {
    // border-bottom-right-radius: 0;
    // border-bottom-left-radius: 0;
  }
  &-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 90px;
    padding: 0 $spacing-h-md;
    &::before {
      position: absolute;
      right: $spacing-h-md;
      bottom: 0;
      left: $spacing-h-md;
      border-bottom: $width-base solid #e6e6e6;
      content: '';
    }
    &:last-child::before {
      border-bottom: none;
    }
    &--isFresh {
      position: relative;
      &::before {
        position: absolute;
        top: 0px;
        right: 0px;
        left: initial;
        width: 50px;
        height: 30px;
        color: $color-white;
        font-size: $font-size-xs;
        text-align: center;
        background-color: $color-orange;
        content: '生鲜';
      }
    }
    &__title {
      width: 130px;
      color: $color-grey-1;
      font-size: $font-size-lg;
    }
    &__content {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: flex-end;
      &--info {
        max-width: 500px;
        overflow: hidden;
        white-space: nowrap;
        text-align: right;
        text-overflow: ellipsis;
      }
      &--info2 {
        display: flex;
        flex-wrap: wrap;
        justify-content: flex-end;
        .btn {
          width: fit-content;
          height: 36px;
          margin-right: 10px;
          margin-bottom: 5px;
          padding: 0 10px;
          color: $color-white;
          font-size: 22px;
          line-height: 36px;
          background: $color-brand;
          border-radius: $border-radius-md;
        }
      }
    }
  }
}
