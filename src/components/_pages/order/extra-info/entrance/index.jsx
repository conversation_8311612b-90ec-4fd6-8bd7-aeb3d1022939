/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  getDynamicVerifyOpt,
  getValidData,
  servicesKeyMap,
} from '@/components/_pages/order/extra-info/_utils';
import { isFresh } from '@/components/_pages/order/_utils';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { useEffect, useLayoutEffect, useRef, useState } from '@tarojs/taro';
import KbBrandInfo from '@/components/_pages/order/extra-info/brand';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { serviceConfig: serviceConfigAuto } = useSelector((state) => state.global);
  const {
    flat,
    actionRef,
    relationInfo,
    reckonFee,
    data,
    dynamicForms,
    mode,
    storageKey,
    onChange,
    custom,
    className,
    serviceConfig: serviceConfigProps,
    total,
    extraInfoData,
  } = props;
  const serviceConfig = serviceConfigProps || serviceConfigAuto;
  const { service } = data || {};
  const dataRef = useRef({ data });
  const [showService, upShowService] = useState(false);
  const [dynamicVerify, upDynamicVerify] = useState({ service: {}, goods: {} });
  const [formatGoods, setFormatGoods] = useState();
  const [isFreshGoods, setIsFresh] = useState(false);

  // 组件加载期间-处理外部调用方法
  useEffect(() => {
    if (actionRef) {
      actionRef.current = {
        openService: () => {
          handleGridClick('service');
        },
      };
    }
  });

  // 保存表单数据
  useLayoutEffect(() => {
    dataRef.current.data = data;
  }, [data]);

  // 处理物品信息显示
  useEffect(() => {
    const { goods_name, goods_weight, goods_remark } = data || {};
    // 物品信息显示
    const { goods_name: { customFresh: dynamicFresh } = {} } = dynamicForms || {};
    let goodFormValue = '';
    if (goods_name) {
      goodFormValue += `${goods_name}/`;
    }
    if (goods_weight > 0) {
      goodFormValue += `${goods_weight}kg/`;
    }
    if (goods_remark) {
      goodFormValue += `${goods_remark}/`;
    }
    goodFormValue = goodFormValue ? goodFormValue.substring(0, goodFormValue.length - 1) : '';
    setFormatGoods(goodFormValue);
    let isFreshGoods = dynamicFresh || isFresh(goods_name);
    setIsFresh(isFreshGoods);
  }, [data, dynamicForms]);

  // 集中处理外部接收的或者是某个特殊下单关系携带的自定义表单值
  useEffect(() => {
    if (!dynamicForms) return;
    let validData = {};
    let dynamicVerify = {};
    let serviceValidData = { ...getValidData(dynamicForms['service']) };
    let serviceDynamicVerify = {
      ...getDynamicVerifyOpt(dynamicForms['service']),
    };
    if (Object.keys(serviceValidData).length) {
      validData = { service: { ...serviceValidData } };
    }
    dynamicVerify = {
      service: { ...serviceDynamicVerify },
      ...getDynamicVerifyOpt(dynamicForms),
    };
    validData = {
      ...validData,
      ...getValidData(dynamicForms),
    };
    upDynamicVerify(dynamicVerify);
    onChange({ formData: validData, dynamicVerify });
  }, [dynamicForms]);

  // 拉取增值服务
  useEffect(() => {
    if (serviceConfig) {
      const { isFresh: freshConfig } = serviceConfig || {};
      const { data: formData } = dataRef.current;
      const { goods_name } = formData || {};
      if (!freshConfig && isFresh(goods_name)) {
        onChange({ formData: { ...formData, goods_name: '日用品' } });
      }
      const serviceList = Object.keys(serviceConfig).filter(
        (key) => servicesKeyMap[key] && serviceConfig[key],
      );
      serviceList && serviceList.length > 0 ? upShowService(true) : upShowService(false);
    } else {
      upShowService(false);
    }
  }, [serviceConfig, dataRef]);

  // 处理点击跳转
  const handleGridClick = (type) => {
    Taro.navigator({
      url: `order/edit/${type}`,
      key: 'routerParamsChange',
      options: {
        relationInfo,
        reckonFee,
        data,
        dynamicForms,
        storageKey,
        serviceConfig,
        dynamicVerify,
        mode,
        total,
      },
      onArrived: () => {},
    });
  };

  const handleChange = (key, e) => {
    const { data } = e || {};
    onChange({ formData: data, type: key });
  };

  const { keep_account = 0, arrive_pay = 0, collection = 0, cost_value = 0 } = service || {};

  const rootCls = classNames('kb-entrance kb-box', className, {
    'kb-entrance-flat': flat,
  });

  return (
    <View>
      {!custom ? (
        <View className={rootCls}>
          {(mode === 'normal' || mode == 'goods') && (
            <View
              className={`kb-entrance-item ${isFreshGoods ? 'kb-entrance-item--isFresh' : ''}`}
              onClick={() => handleGridClick('goods')}
              hoverClass='kb-hover'
            >
              <View className='kb-entrance-item__title'>物品信息</View>
              <View className='kb-entrance-item__content'>
                <View className='kb-entrance-item__content--info'>{formatGoods}</View>
                <AtIcon
                  prefixClass='kb-icon'
                  value='arrow'
                  className='kb-color__grey kb-icon-size__base'
                />
              </View>
            </View>
          )}
          {process.env.MODE_ENV === 'wkd' && mode === 'normal' ? (
            <KbBrandInfo
              rootCls='kb-brand-info__base'
              data={extraInfoData}
              dynamicForms={dynamicForms}
              onChange={handleChange.bind(this, 'brandInfo')}
              serviceConfig={serviceConfig}
              relationInfo={relationInfo}
            />
          ) : null}
          {(mode === 'normal' || mode == 'service') && showService ? (
            <View
              className='kb-entrance-item'
              onClick={() => handleGridClick('service')}
              hoverClass='kb-hover'
            >
              <View className='kb-entrance-item__title'>增值服务</View>
              <View className='kb-entrance-item__content'>
                <View className='kb-entrance-item__content--info2'>
                  {serviceConfig.isDeclared && serviceConfig.declaration ? (
                    <View className='btn'>{serviceConfig.courierId ? '快递员' : '驿站'}声明</View>
                  ) : (
                    ''
                  )}
                  {serviceConfig.isDecVal && (
                    <View className='btn'>
                      声明价值
                      {keep_account > 0 ? `${keep_account}元` : '(必填)'}
                    </View>
                  )}
                  {serviceConfig.isDesPay && arrive_pay > 0 && (
                    <View className='btn'>到付运费</View>
                  )}
                  {serviceConfig.isDelivery && collection > 0 && (
                    <View className='btn'>代收金额{collection}元</View>
                  )}
                  {serviceConfig.isProPrice && cost_value > 0 && (
                    <View className='btn'>保价金额{cost_value}元</View>
                  )}
                </View>
                <AtIcon
                  prefixClass='kb-icon'
                  value='arrow'
                  className='kb-color__grey kb-icon-size__base'
                />
              </View>
            </View>
          ) : (
            ''
          )}
        </View>
      ) : null}
    </View>
  );
};
Index.defaultProps = {
  actionRef: null,
  custom: false,
  mode: 'normal',
  onChange: noop,
  flat: true,
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
