/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import { AtList, AtListItem } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { declarationList = [], serviceTip = [], relationType, current } = props;
  return (
    <Fragment>
      {relationType != 'brand' || current != 'pro_price' ? (
        <Fragment>
          {declarationList.length > 0 && (
            <View className='kb-service__tips kb-spacing-lg-t'>
              <View className='kb-service__tips--title'>自定义声明:</View>
              <View className='kb-service__tips--content'>
                <View>
                  {declarationList.map((text, index) => {
                    const note = `${index + 1}.${text}`;
                    return (
                      <View key={note} className='kb-spacing-sm-tb kb-spacing-lg-lr kb-size__base'>
                        {note}
                      </View>
                    );
                  })}
                </View>
              </View>
            </View>
          )}
          <View className='kb-service__tips kb-spacing-lg-t'>
            <View className='kb-service__tips--title'>温馨提示:</View>
            <View className='kb-service__tips--content'>
              <AtList hasBorder={false}>
                {serviceTip.map((i) => (
                  <AtListItem key={i} className='kb-spacing-sm-tb' hasBorder={false} note={i} />
                ))}
              </AtList>
            </View>
          </View>
        </Fragment>
      ) : (
        <View className='kb-service__tips kb-spacing-lg-t'>
          <View className='kb-service__tips--title'>温馨提示:</View>
          <View className='kb-service__tips--content'>
            <View>
              {serviceTip.map((item, index) => {
                const { label, list } = item;
                const note = `${index + 1}.${label}`;
                return (
                  <View key={label} className='kb-spacing-sm-tb kb-spacing-lg-lr kb-size__base'>
                    <View>{note}</View>
                    <View className='kb-spacing-sm-l'>
                      {list.map((text) => {
                        return <View>{text}</View>;
                      })}
                    </View>
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      )}
    </Fragment>
  );
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
