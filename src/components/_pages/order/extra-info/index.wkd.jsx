/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import KbApponintmentTime from '@/components/_pages/order/appointment-time';
import KbExtraEnter from '@/components/_pages/order/extra-info/entrance';
import KbGoods from '@/components/_pages/order/goods';
import KbServiceType from '@/components/_pages/order/service-type';
import KbValueService from '@/components/_pages/order/value-service';
import { cleanOrderEditFormInfo } from '@/components/_pages/order/_utils/order.edit';
import { defaultGoodsValue, getDefaultGoods } from '@/components/_pages/order/_utils/order.goods';
import Form from '@base/utils/form';
import { checkIsReLogin, debounce, noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { Component, createRef, Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import numeral from 'numeral';
import { AtAvatar, AtIcon, AtInput } from 'taro-ui';
import { fixDynamicFormsData, getFormItem, goodsKeys } from '../_utils';
import './index.wkd.scss';

@connect(
  ({ global }) => ({
    loginData: global.loginData,
    brands: global.brands,
  }),
  {
    dispatchGet: get,
  },
)
class Index extends Component {
  static defaultProps = {
    data: {},
    storageKey: 'extraInfo',
    dynamicForms: fixDynamicFormsData(),
    address: {},
    isOpenCredit: false, //是否开通支付分
    onChange: noop,
    source: 'kd', //tcjs同城急送
    mode: 'order',
    /**
     *  order 订单模式，包含品牌，现付，增值服务选择
     *  address 仅包含物品类型、重量、备注
     *  yjkd 优寄快递模式,整个组件的显示发生变化
     */
  };
  static options = {
    addGlobalClass: true,
  };
  constructor() {
    this.weightRange = [0, 999];
    this.state = {
      form: { data: {}, disabled: true },
      editableMony: true,
      serviceData: {},
    };
    // refs
    this.goodsRef = createRef();
    this.appointmentTimeRef = createRef();
    this.valServiceRef = createRef();
    this.handleDeobunceChange = debounce(this.handleDeobunceChange, 300, {
      trailing: true,
    });
  }

  componentDidMount() {
    this.props.dispatchGet();
    this.createForm();
  }

  componentDidShow() {
    this.dealDefaultValue('change');
  }

  componentWillReceiveProps(nextProps) {
    const {
      data: nextData,
      relationInfo: nextRelationInfo,
      loginData: nextLoginData,
      dynamicForms: nextDynamicForms,
      source,
    } = nextProps;
    const { data, relationInfo, loginData, dynamicForms } = this.props;
    // 处理组件内部data更新
    if (source == 'tcjs' && nextRelationInfo != relationInfo) {
      this.createForm();
    }
    if (nextRelationInfo != relationInfo) {
      this.formIns.update({ service: {} });
    }
    if (nextData && nextData !== data) {
      const { clean, pay_status, orderPrice, ...nextDataRest } = nextData;
      if (this.triggerClean(clean)) {
        return;
      }
      let editableMony = true;
      // orderPrice 此值实现问题：包含空值、0 、大于0
      if (orderPrice > 0) {
        nextDataRest.orderPrice = orderPrice;
        editableMony = pay_status != '1';
      } else if (orderPrice === '') {
        // 清空输入
        nextDataRest.orderPrice = '';
      }
      this.formIns.update(nextDataRest);
      this.setState({
        editableMony,
      });
    }
    // 表单项设置更新
    if (nextDynamicForms !== dynamicForms && nextDynamicForms) {
      this.triggerWhetherFormLocked(nextDynamicForms);
      return;
    }
    // 检查是否重新登录
    checkIsReLogin(nextLoginData, loginData, () => {});
  }

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady) => {
    const { mode, relationInfo: { type: relationType } = {} } = this.props;
    const isTcjs = !!(relationType == 'tcjs');
    const form = getFormItem({
      keys: goodsKeys,
      data: { goods_name: isTcjs ? '' : defaultGoodsValue },
      merge: {
        goods_name: { required: true, clean: defaultGoodsValue },
      },
    });
    if (mode === 'order' || mode == 'yjkd') {
      // 订单模式：表单中加入品牌，现付，增值服务
      getFormItem({
        form,
        keys: [
          'brand',
          'orderPrice',
          'service',
          'reserve_start_time',
          'reserve_end_time',
          'product_code',
          'volume',
          'brand',
          'product_type',
        ],
        merge: {
          service: { value: {}, storage: false, clean: null },
          brand: { value: '' },
          product_type: { value: '' },
          orderPrice: { value: '', storage: false },
          reserve_start_time: {
            value: '1h',
            required: false,
            storage: false,
            clean: '1h',
          },
          reserve_end_time: {
            value: '1h',
            required: false,
            storage: false,
            clean: '1h',
          },
          product_code: {
            value: '',
            required: false,
            storage: false,
            clean: null,
          },
          volume: {
            value: '',
            required: false,
            storage: false,
            clean: null,
          },
        },
      });
    }
    const { storageKey } = this.props;
    this.formIns = new Form(
      {
        form,
        storageKey: isTcjs ? '' : storageKey,
        onChange: ({ value, name, type }) => {
          /**
           * 只有配置了监听表单事件的函数才可以触发
           * type 是事件名 input blur之类的
           */
          if (name == 'goods_weight') {
            return this.formatWeight({ value, name, type });
          }
        },
        onUpdate: (data) => {
          const {
            data: { goods_weight: changeWeight },
            formEvent,
            eventType,
          } = data;
          if (eventType === 'clean') {
            // 清除操作
            this.cleanFormInfo(['serviceData']);
          }
          if (formEvent === 'blur' && changeWeight) {
            return;
          }
          this.props.onChange(data);
        },
        onReady: () => {
          this.dealDefaultValue();
          onReady && onReady();
        },
      },
      this,
    );
  };
  handleDeobunceChange = (key, value, e) => {
    // 为兼容点击加减号更改重量时，失焦事件慢于点击事件，禁用失焦赋值
    if (e && e.type == 'blur') return;
    this.onChange_form(key, e);
  };
  dealDefaultValue = (type = 'init') => {
    // 更改默认物品类型
    if (this.formIns) {
      getDefaultGoods().then((dGoods) => {
        if (type == 'change' && this.dGoods == dGoods) return;
        this.dGoods = dGoods;
        this.formIns.params.form.goods_name.clean = this.dGoods;
      });
    }
  };
  // 触发清理表单
  triggerClean = (clean) => {
    clean && this.formIns.clean();
    return clean;
  };
  // 清除信息
  cleanFormInfo = (keys, replaceData) => {
    this.setState(cleanOrderEditFormInfo(keys, replaceData));
  };
  // 是否触发表单锁定为自定义内容
  triggerWhetherFormLocked = (dynamicForms) => {
    let updateData = {};
    if (!dynamicForms) return;
    Object.keys(dynamicForms).forEach((i) => {
      if (dynamicForms[i].value) {
        updateData[i] = dynamicForms[i].value;
      }
    });
    if (Object.keys(updateData).length) {
      this.formIns.update({ ...updateData });
      return updateData;
    }
    return false;
  };

  //处理子组件数据更新
  handleChange = (key, data) => {
    switch (key) {
      case 'goods':
        this.formIns.update(data);
        break;
      case 'appointment-time':
        let { reserve_start_time, reserve_end_time, expectedTime } = data;
        this.formIns.update({
          reserve_start_time,
          reserve_end_time,
        });
        this.setState({
          expectedTime,
        });
        break;
      case 'product_code':
        this.formIns.update({
          product_code: data,
        });
        break;
      case 'volume':
        this.formIns.update({
          volume: data,
        });
        break;
      case 'service':
        this.formIns.update({
          service: data,
        });
        break;
    }
  };

  //处理重量增减
  onHandleWeight = (type) => {
    const { form: { data: { goods_weight } } = {} } = this.state;
    let value = 1 * goods_weight;
    switch (type) {
      case 'jia':
        value = value + 1;
        break;
      case 'jian':
        value = value - 1;
        break;
    }
    this.formIns.update({
      goods_weight: this.formatWeight({ value, type: 'blur' }),
    });
  };
  //格式化重量
  formatWeight = ({ value, type }) => {
    const weightRange = this.weightRange;
    let val = numeral(Math.min(Math.max(weightRange[0], numeral(value).value()), weightRange[1]));
    if (type == 'blur') {
      return value ? val.format('0.00') : '';
    } else {
      return value ? val.value() : '';
    }
  };

  //触发物品信息弹窗
  triggerGoodsOpen = (key) => {
    switch (key) {
      case 'goods':
        this.goodsRef.current.open();
        break;
      case 'appointment-time':
        const { relationInfo: { dynamicForms = {}, platform } = {} } = this.props;
        const isExpectedTime = dynamicForms.appointmentTime && dynamicForms.appointmentTime.isShow;
        if (platform && !isExpectedTime) {
          Taro.kbModal({
            content: ['若需要预约上门时间，将匹配京东快递为您服务'],
            cancelText: '取消',
            onConfirm: () => {
              Taro.kbUpdateRelationInfo({
                brand: 'jd',
              });
            },
          });
          return;
        }
        this.appointmentTimeRef.onShowPicker();
        break;
    }
  };

  handleExtraInfoUpdate = (data) => {
    const { formData, type = 'service' } = data;
    if (formData) {
      this.formIns && this.formIns.update(formData);
    } else if (this.formIns) {
      if (type === 'service') {
        // 重置增值服务
        this.formIns.update({
          service: {},
        });
      }
    }
  };

  //优寄快递增值服务
  onOpenService = () => {
    this.valServiceRef.current.openService();
  };

  //快递费提示
  onShowPayTips() {
    Taro.kbModal({
      content:
        '未确认金额前，建议不要填写金额，如遇退款问题可在订单详情页提交退款申请，快递员确认后方可退款，如快递员一直未回应或拒绝退款，可联系客服介入',
    });
  }

  //处理页面跳转
  onJumpTo(key) {
    const {
      relationInfo: { brand } = {},
      address: { city_code } = {},
      extraData,
      dynamicForms,
    } = this.props;
    const { form: { data: formData } = {} } = this.state;
    switch (key) {
      case 'serviceBrand':
        Taro.navigator({
          url: 'order/relation',
          options: {
            type: 'tcjs',
            current: brand,
          },
        });
        break;
      case 'extra':
        Taro.kbSetGlobalData('PageGlobaData', { extraData });
        Taro.navigator({
          url: 'order/delivery/extra',
          options: {
            brand,
            city_code,
          },
        });
        break;
      case 'goods':
        Taro.navigator({
          url: 'order/edit/goods',
          key: 'routerParamsChange',
          options: {
            data: formData,
            dynamicForms,
          },
          onArrived: () => {},
        });
        break;
    }
  }

  handleRef = (ref) => {
    this.appointmentTimeRef = ref;
  };

  getFormatGoods(data = {}) {
    let goodFormValue = '';
    if (data.goods_name) {
      goodFormValue += `${data.goods_name}/`;
    }
    if (data.goods_weight > 0) {
      goodFormValue += `${data.goods_weight}kg/`;
    }
    goodFormValue = goodFormValue ? goodFormValue.substring(0, goodFormValue.length - 1) : '';
    return goodFormValue;
  }

  render() {
    const { editableMony, form: { data } = {}, expectedTime, serviceData } = this.state;
    const {
      mode,
      total,
      relationInfo,
      relationInfo: { type: relationType, brand, courier = {}, platform = '' } = {},
      dynamicForms,
      address,
      customerInfo = {},
      brands,
      extraData,
      isOpenCredit,
      data: extraInfoData,
    } = this.props;
    const { service } = data || {};
    const allowPay = total <= 1;
    const orderPriceCls = 'flex-glow-all';
    let {
      goods_name: dynamicGoodsName = {},
      goods_weight: dynamicGoodsWeight = {},
      goods_remark: dynamicGoodsRemark = {},
      service: dynamicService = {},
      orderPrice: dynamicOrderPrice = {},
      appointmentTime: dynamicAppointmentTime = {},
      product_code: dynamicProduct_code = {},
      extra: dynamicExtra = {},
    } = dynamicForms || {};
    const goods_nameCls = classNames('kb-navigator', {
      'kb-clear__pseudo-ele-after': !!dynamicGoodsName.locked,
    });

    return (
      <View className='kb-extra-info kb-extra-info-common'>
        {mode == 'yjkd' ? (
          <Fragment>
            <View className='kb-extra-info_yjkd__other'>
              <View className='yjkd__other at-row kb-size__base'>
                <View
                  className='yjkd__other_item at-row'
                  hoverClass='kb-hover'
                  onClick={this.onJumpTo.bind(this, 'goods')}
                >
                  <View
                    className={`yjkd__other_val at-col ${data.goods_name ? '' : 'kb-color__grey'}`}
                  >
                    {(data && this.getFormatGoods(data)) || '物品信息'}
                  </View>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-color__grey kb-icon-size__sm'
                  />
                </View>
                <View
                  className='yjkd__other_item at-row'
                  hoverClass='kb-hover'
                  onClick={this.onOpenService}
                >
                  <KbExtraEnter
                    actionRef={this.valServiceRef}
                    mode='service'
                    custom
                    relationInfo={relationInfo}
                    // reckonFee={reckonFee}
                    data={data}
                    dynamicForms={dynamicForms}
                    onChange={this.handleExtraInfoUpdate}
                  />
                  <View className='yjkd__other_val at-col'>
                    {service.cost_value > 0 ? '保价' + service.cost_value + '元' : '增值服务'}
                  </View>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-color__grey kb-icon-size__sm'
                  />
                </View>
                <View
                  className='yjkd__other_item at-row'
                  hoverClass='kb-hover'
                  onClick={() => this.triggerGoodsOpen('appointment-time')}
                >
                  <View className='yjkd__other_val at-col'>
                    {expectedTime ? expectedTime : '立即取件'}
                  </View>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-color__grey kb-icon-size__sm'
                  />
                </View>
              </View>
            </View>
          </Fragment>
        ) : mode == 'address' ? (
          <View className='kb-form__group'>
            <View className='kb-form'>
              <View className='kb-form__item'>
                <View className='item-content at-col'>
                  <View
                    className={goods_nameCls}
                    hoverClass='kb-hover'
                    onClick={() => this.triggerGoodsOpen('goods')}
                  >
                    <View className='kb-navigator__placeholder'>物品类型</View>
                    <View className='kb-navigator__value'>{data.goods_name || '请选择'}</View>
                  </View>
                </View>
                <View className='at-col'>
                  <View className='item-content item-extra__line kb-spacing-md-r'>
                    <View className='at-row at-row__align--center'>
                      <View className='item-title'>重量</View>
                      <AtInput
                        cursor={-1}
                        cursorSpacing={20}
                        className='kb-input-center'
                        placeholder='选填'
                        maxLength={20}
                        value={data.goods_weight}
                        type='digit'
                        onChange={this.handleDeobunceChange.bind(this, 'goods_weight')}
                      />
                      <View className='item-desc'>kg</View>
                    </View>
                  </View>
                </View>
              </View>
              <View className='kb-form__item'>
                <View className='item-title'>{dynamicGoodsRemark.label || '备注'}</View>
                <View className='item-content item-content__right'>
                  <AtInput
                    cursor={-1}
                    cursorSpacing={20}
                    placeholder={dynamicGoodsRemark.placeholder || '选填，不超过20字'}
                    maxLength={dynamicGoodsRemark.maxLength || 20}
                    value={data.goods_remark}
                    onChange={this.onChange_form.bind(this, 'goods_remark')}
                  />
                </View>
              </View>
            </View>
          </View>
        ) : (
          <View className='kb-form__group'>
            {relationType == 'tcjs' ? (
              <View className='kb-form'>
                <View className='kb-form__item'>
                  {(dynamicGoodsName.isShow || mode === 'address') && (
                    <View className='item-content at-col'>
                      <View
                        className={goods_nameCls}
                        hoverClass='kb-hover'
                        onClick={() => this.triggerGoodsOpen('goods')}
                      >
                        <View className='kb-navigator__placeholder'>物品类型</View>
                        <View className='kb-navigator__value'>{data.goods_name || '请选择'}</View>
                      </View>
                    </View>
                  )}
                  {(dynamicGoodsWeight.isShow || mode === 'address') && (
                    <View className='at-col'>
                      <View className='item-content item-extra__line kb-spacing-md-r'>
                        <View className='at-row at-row__align--center'>
                          <View className='item-title'>重量</View>
                          <AtInput
                            cursor={-1}
                            cursorSpacing={20}
                            className='kb-input-center'
                            placeholder='选填'
                            maxLength={20}
                            value={data.goods_weight}
                            type='digit'
                            onChange={this.handleDeobunceChange.bind(this, 'goods_weight')}
                          />
                          <View className='item-desc'>kg</View>
                        </View>
                      </View>
                    </View>
                  )}
                </View>
                {((dynamicGoodsRemark.isShow && !dynamicGoodsRemark.only) ||
                  mode === 'address') && (
                  <View className='kb-form__item'>
                    <View className='item-title'>{dynamicGoodsRemark.label || '备注'}</View>
                    <View className='item-content item-content__right'>
                      <AtInput
                        cursor={-1}
                        cursorSpacing={20}
                        placeholder={dynamicGoodsRemark.placeholder || '选填，不超过20字'}
                        maxLength={dynamicGoodsRemark.maxLength || 20}
                        value={data.goods_remark}
                        onChange={this.onChange_form.bind(this, 'goods_remark')}
                      />
                    </View>
                  </View>
                )}
              </View>
            ) : (
              dynamicGoodsName.isShow && (
                <View className='kb-form'>
                  <KbExtraEnter
                    relationInfo={relationInfo}
                    data={data}
                    extraInfoData={extraInfoData}
                    flat={false}
                    dynamicForms={dynamicForms}
                    onChange={this.handleExtraInfoUpdate}
                  />
                </View>
              )
            )}
            <View className='kb-form'>
              {relationType == 'tcjs' && (
                <View className='kb-form__item'>
                  <View className='at-row at-row__justify--between at-row__align--center'>
                    <View className='item-title'>服务品牌</View>
                    <View
                      className='at-row at-row__justify--end at-row__align--center kb-spacing-sm-r'
                      onClick={this.onJumpTo.bind(this, 'serviceBrand')}
                    >
                      <View className='at-row at-row__justify--end at-row__align--center kb-margin-sm-r'>
                        <AtAvatar
                          image={
                            relationInfo.img ||
                            (brands &&
                              brands[relationInfo.brand] &&
                              brands[relationInfo.brand].logo_link)
                          }
                          size='small'
                          circle
                        />
                        <View className='kb-margin-sm-l'>{relationInfo.name}</View>
                      </View>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='arrow'
                        className='kb-color__grey kb-icon-size__base'
                      />
                    </View>
                  </View>
                </View>
              )}
              {dynamicAppointmentTime.isShow && (
                <View
                  className='kb-form__item'
                  hoverClass='kb-hover'
                  onClick={() => this.triggerGoodsOpen('appointment-time')}
                >
                  <View className='at-row at-row__justify--between at-row__align--center'>
                    <View className='item-title'>上门时间</View>
                    <View className='at-row at-row__justify--end at-row__align--center kb-spacing-md-r'>
                      <View className='kb-margin-sm-r'>
                        {expectedTime ? expectedTime : '立即取件'}
                      </View>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='arrow'
                        className='kb-color__grey kb-icon-size__base'
                      />
                    </View>
                  </View>
                </View>
              )}
              {dynamicProduct_code.isShow && (
                <KbServiceType
                  current={data.product_code}
                  type={relationInfo.brand}
                  onChange={(e) => this.handleChange('product_code', e)}
                />
              )}
              {relationType == 'tcjs' && dynamicService.isShow && (
                <KbValueService
                  data={serviceData}
                  relationInfo={relationInfo}
                  extraInfo={data}
                  source={relationType == 'tcjs' ? 'tcjs' : 'kd'}
                  onChange={(e) => this.handleChange('service', e)}
                />
              )}
              {allowPay && dynamicOrderPrice.isShow && (
                <View className='kb-form__item'>
                  <View className='at-col'>
                    <View className={orderPriceCls}>
                      <View className='at-row at-row__align--center'>
                        {process.env.MODE_ENV == 'wkd' ? (
                          <View className='item-title' onClick={this.onShowPayTips}>
                            快递费
                            <AtIcon
                              className='kb-size__lg kb-color__orange kb-margin-sm-l kb-margin-xs-b'
                              prefixClass='kb-icon'
                              value='alert'
                            />
                          </View>
                        ) : (
                          <View className='item-title'>现付</View>
                        )}
                        <View className='at-col'>
                          <AtInput
                            cursor={-1}
                            cursorSpacing={20}
                            className='kb-input-center kb-clear__pseudo-ele-after'
                            placeholder={
                              process.env.MODE_ENV == 'wkd' ? '选填，与快递员确认后填写' : '选填'
                            }
                            maxLength={20}
                            value={data.orderPrice}
                            type='digit'
                            disabled={!!customerInfo.customer_price}
                            editable={editableMony}
                            onChange={this.onChange_form.bind(this, 'orderPrice')}
                          />
                        </View>
                        <View className=''>元</View>
                      </View>
                    </View>
                  </View>
                </View>
              )}
              {dynamicExtra.isShow && (
                <View className='kb-form__item'>
                  <View className='at-row at-row__justify--between at-row__align--center'>
                    <View className='item-title'>额外需求</View>
                    <View
                      className='at-row at-row__justify--end at-row__align--center kb-spacing-sm-r'
                      onClick={this.onJumpTo.bind(this, 'extra')}
                    >
                      <View className='at-row at-row__justify--end at-row__align--center kb-margin-sm-r'>
                        {extraData.extraText || ''}
                      </View>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='arrow'
                        className='kb-color__grey kb-icon-size__base'
                      />
                    </View>
                  </View>
                </View>
              )}
              {dynamicGoodsRemark.isShow && dynamicGoodsRemark.only && (
                <View className='kb-form__item'>
                  <View className='item-title'>{dynamicGoodsRemark.label || '备注'}</View>
                  <View className='item-content item-content__right'>
                    <AtInput
                      cursor={-1}
                      cursorSpacing={20}
                      placeholder={dynamicGoodsRemark.placeholder || '选填，不超过20字'}
                      maxLength={dynamicGoodsRemark.maxLength || 20}
                      value={data.goods_remark}
                      onChange={this.onChange_form.bind(this, 'goods_remark')}
                    />
                  </View>
                </View>
              )}
            </View>
          </View>
        )}
        <KbGoods
          actionRef={this.goodsRef}
          current={data.goods_name}
          mode={relationType == 'tcjs' ? 'tcjs' : 'kd'}
          brand={relationType == 'tcjs' ? brand : ''}
          isFresh={false}
          curRemark={data.goods_remark}
          isRemark={!!(mode == 'yjkd')}
          locked={dynamicGoodsName.locked}
          onChange={(e) => this.handleChange('goods', e)}
        />
        <KbApponintmentTime
          onRef={this.handleRef}
          relationInfo={relationInfo}
          isOpenCredit={isOpenCredit}
          address={address}
          extraInfo={data}
          columns={platform == 'yjkd_courier' && courier ? courier.reserve_time : ''}
          onChange={(e) => this.handleChange('appointment-time', e)}
        />
      </View>
    );
  }
}

export default Index;
