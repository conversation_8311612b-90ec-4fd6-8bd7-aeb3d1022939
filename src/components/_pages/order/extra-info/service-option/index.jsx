/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { serviceTips } from '@/components/_pages/order/extra-info/_utils';
import KbScrollView from '@base/components/scroll-view';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { Fragment, useEffect, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtInput, AtList, AtListItem, AtSwitch } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { servicesConfig, optionConfig, onChange, dynamicValidation, data, dynamicDisabled } =
    props;
  const { proPriceStart, isProPrice, proPriceEnd, cost, isDeclared, declaration } =
    servicesConfig || {};
  const {
    key,
    customKey,
    switch: { key: switchKey, label: switchLabel, cInput } = {},
    required,
    inputConfig,
  } = optionConfig || {};

  const [showInp, updateShowInp] = useState(!cInput);
  const handleInputChange = (key, value) => {
    onChange(key, value);
  };
  const handleSwitchChange = (key, value) => {
    if (cInput && !value) onChange(optionConfig.key, '');
    onChange(key, value ? 1 : 0);
  };
  useEffect(() => {
    const { switch: { cInput } = {} } = optionConfig || {};
    cInput && updateShowInp(data[switchKey]);
  }, [optionConfig, data]);
  const disabled = dynamicDisabled[key];
  const dynamicRequired = dynamicValidation[key] && dynamicValidation[key]['required'];
  const switchDisabled =
    switchKey === 'rate_checked' && parseFloat(data.keep_account) >= parseFloat(proPriceStart);
  const labelCls = classNames('kb-service-form-label kb-spacing-lg-l', {
    'kb-service-form-required': !!required || !!dynamicRequired,
  });
  const currentProPrice = ['keep_account', 'pro_price'].includes(key);
  const hasProPrice = isProPrice && currentProPrice;
  const serviceTip = serviceTips()[key] || [];
  const getDeclarationList = (hasProPrice) => {
    let list = hasProPrice
      ? [
          `根据驿站/快递员/网点声明每笔订单交寄物品价值在 ${parseFloat(
            proPriceStart || 0,
          )}元~${parseFloat(proPriceEnd || 0)}元必须保价，低于 ${parseFloat(
            proPriceStart || 0,
          )}元非必须保价（价值区间是或字段，根据快递员/驿站/网点设置的）`,
          '请按物品实际价值来填写物品价值，我方页面只做下单参考，具体业务请咨询驿站/快递员/驿站/网点',
          '物品是否属于可保价范围请咨询上门驿站/快递员/网点服务人员，我方与驿站/快递员/网点没有从属关系，不对其服务做任何担保和背书',
          '请仔细与驿站/快递员/网点明确服务内容，并当面与驿站/快递员/网点签订增值服务协议且索要服务依据',
        ]
      : [];
    if (isDeclared && declaration) {
      list = [declaration, ...list];
    }
    return list;
  };

  const declarationList = getDeclarationList(hasProPrice);
  const restSwitchLabel = hasProPrice ? `${cost}%` : '';
  const formCls = classNames('kb-form kb-service-form kb-margin-lg-t  kb-spacing-lg', {
    'kb-vertical ': cInput,
  });
  const switchCls = classNames('kb-service-form-item  at-row__justify--between', {
    [`kb-margin-lg-${cInput ? 'b' : 't'}`]: showInp,
  });
  return (
    <Fragment>
      <View className={formCls}>
        {showInp && (
          <View className='kb-service-form-item'>
            <View className={labelCls}>{inputConfig.label}</View>
            <AtInput
              className={`kb-input_${customKey || key}`}
              placeholder='填写金额'
              value={data[customKey || key]}
              disabled={disabled}
              cursor={-1}
              type='digit'
              controlled
              onChange={handleInputChange.bind(null, customKey || key)}
            />
            <View className='kb-spacing-lg-lr'>元</View>
          </View>
        )}
        {(hasProPrice || switchKey) && (
          <View className={switchCls}>
            <View className='kb-service-form-label kb-spacing-lg-l'>
              {switchLabel + ' ' + restSwitchLabel}
            </View>
            <AtSwitch
              onChange={handleSwitchChange.bind(null, switchKey)}
              border={false}
              size='small'
              color='#00a173'
              disabled={switchDisabled}
              checked={!!data[switchKey]}
            />
          </View>
        )}
      </View>
      <View className='kb-tips-scroll'>
        <KbScrollView scrollY className='kb-scrollview' fixIosFooter={false}>
          <View className='kb-spacing-lg-tb'>
            {declarationList.length && (
              <View className='kb-service__tips'>
                <View className='kb-service__tips--title'>自定义声明:</View>
                <View className='kb-service__tips--content'>
                  <View>
                    {declarationList.map((text, index) => {
                      const note = `${index + 1}.${text}`;
                      return (
                        <View
                          key={note}
                          className='kb-spacing-sm-tb kb-spacing-lg-lr kb-size__base'
                        >
                          {note}
                        </View>
                      );
                    })}
                  </View>
                </View>
              </View>
            )}
            <View className='kb-service__tips'>
              <View className='kb-service__tips--title'>温馨提示:</View>
              <View className='kb-service__tips--content'>
                <AtList hasBorder={false}>
                  {serviceTip.map((i) => (
                    <AtListItem key={i} className='kb-spacing-sm-tb' hasBorder={false} note={i} />
                  ))}
                </AtList>
              </View>
            </View>
          </View>
        </KbScrollView>
      </View>
    </Fragment>
  );
};

Index.defaultProps = {
  serviceConfig: {},
  optionConfig: {},
  data: {},
  onChange: noop,
  dynamicDisabled: {},
  dynamicValidation: {},
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
