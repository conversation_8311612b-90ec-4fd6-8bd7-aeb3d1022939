/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-service {
  &__tips {
    background-color: $color-white;

    &--title {
      margin-bottom: $spacing-v-sm;
    }

    &--content {
      color: $color-grey-2;
    }
  }
  &-form {
    background-color: $color-grey-8 !important;
    &-item {
      display: flex;
      align-items: center;
      overflow: hidden;
      border-radius: 200px;
      background-color: $color-white;
    }
    &-label {
      white-space: nowrap;
    }
    &-required {
      position: relative;
      padding-right: $spacing-v-xl * 3 !important;
      &::after {
        position: absolute;
        display: block;
        content: "必填";
        top: 50%;
        transform: translateY(-50%);
        right: 0;
        font-size: $font-size-sm;
        padding: 2px $spacing-v-sm;
        color: $color-red;
        border: $border-lightest;
        border-color: $color-red;
        border-radius: $border-radius-sm;
      }
    }
  }
}
.kb-vertical {
  display: flex;
  flex-direction: column-reverse;
}
