/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from '@tarojs/taro';
import KbCheckbox from '@base/components/checkbox';
import { View, Text } from '@tarojs/components';
import { noop } from '@base/utils/utils';
import './brandCashCheckbox.scss';

const Index = (props) => {
  const { onChange, value: propValue } = props;
  const [checked, updateChecked] = useState(true);
  const handleCheckChange = (value) => {
    updateChecked(value);
    onChange(value);
  };
  useEffect(() => {
    // 将初始值回传
    handleCheckChange(propValue);
  }, [propValue]);
  return (
    <View className='at-row at-row__align--center  kb-spacing-lg-tb kb-form__item '>
      <View className='kb-size__sm'>
        支付0.01元保证金成功,订单成功签收后可获得
        <Text className='kb-color__red'>0.5元现金补贴</Text>
      </View>
      <KbCheckbox className='kb-spacing-sm-l' onChange={handleCheckChange} checked={checked} />
    </View>
  );
};

Index.defaultProps = {
  onChange: noop,
  value: true,
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
