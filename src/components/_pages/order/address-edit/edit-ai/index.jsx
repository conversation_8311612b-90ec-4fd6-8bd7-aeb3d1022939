/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { checkAddress, supportAi } from '@/components/_pages/order/address-edit/utils';
import KbButton from '@base/components/button';
import KbImagePicker from '@base/components/image-picker';
import KbSpeech from '@base/components/speech';
import KbTextarea from '@base/components/textarea';
import { useDidHideCom, useDidShowCom } from '@base/hooks/page';
import addressFormat from '@base/utils/addressFormat';
import { createListener, debounce } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, {
  Fragment,
  useCallback,
  useEffect,
  useLayoutEffect,
  useRef,
  useState,
} from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import once from 'lodash/once';
import qs from 'qs';
import { AtIcon } from 'taro-ui';
import './index.scss';

const aiConfig = {
  ai: {
    key: 'ai',
  },
  camera: {
    key: 'camera',
    icon: 'camera',
  },
  mic: {
    key: 'mic',
    icon: 'mic',
  },
  book: { key: 'book', icon: 'book' },
};

const Index = (props) => {
  const {
    source,
    supportBatch,
    stopAutoAiCopyBoard,
    manualAi,
    className,
    onFormDataUpdate,
    onParseImg,
    type,
    supportAi,
    placeholder,
    actionRef,
    hasManualData,
    multipleNeedConfirm,
    isEdit,
  } = props;

  const [value, upValue] = useState('');
  const [parseData, setParseData] = useState([]);
  const [bars, upBars] = useState(
    supportAi
      .filter((key) => {
        return aiConfig[key];
      })
      .map((key) => {
        return aiConfig[key];
      }),
  );
  const [pageShow, setPageShow] = useState(false);
  const loadRef = useRef({});
  const barsRef = useRef({ bars });
  const valueRef = useRef('');
  const lockRef = useRef({});
  const textAreaRef = useRef({ status: 'blur' });
  const timerRef = useRef({});
  const autoPasted = useRef({});
  const {
    relationInfo = {},
    serviceConfig: { isDesPay: canToPay = false, isDelivery: canCollection = false } = {},
  } = useSelector((state) => state.global);

  const { customer = {} } = relationInfo;
  const { id: customer_id } = customer;

  useDidHideCom(() => {
    setPageShow(false);
    autoPasted.current.pasted = false;
  });

  useDidShowCom(() => {
    setPageShow(true);
  });

  useEffect(() => {
    if (actionRef) {
      actionRef.current = {
        clear: () => {
          upValue('');
          valueRef.current = '';
          dealParseImg();
        },
      };
    }
  }, []);

  useLayoutEffect(() => {
    barsRef.current.bars = bars;
    loadRef.current.load = true;
    timerRef.current.lockTimer && clearTimeout(timerRef.current.lockTimer);
  });

  useEffect(() => {
    if (supportBatch) {
      const { bars } = barsRef.current || {};
      let add = [];
      if (!(process.env.PLATFORM_ENV === 'alipay' && process.env.MODE_ENV === 'third.pro')) {
        add.push({ key: 'excel' });
      }
      let hasAiItem = (name) => bars.find((item) => item.key === name);
      upBars([...bars, ...add.filter((item) => !hasAiItem(item.key))]);
    }
  }, [supportBatch, barsRef]);

  useEffect(() => {
    if (stopAutoAiCopyBoard) {
      return;
    }
    if (
      !valueRef.current &&
      loadRef.current.load &&
      type === 'receive' &&
      pageShow &&
      !autoPasted.current.pasted
    ) {
      autoPasted.current.pasted = true;
      getClipboardData().then((word) => {
        addressFormat(word, {
          multiple: true,
          toastLoading: false,
          toastError: false,
        }).then((data) => {
          if (!data) return;
          if (!supportBatch) {
            data = data.length > 1 ? [data[0]] : data;
          }
          const isFullAddress = checkAddress(data[0]) === 'full';
          if (isFullAddress) {
            upValue(word);
            valueRef.current = word;
            setParseData(data.length > 1 ? data : []);
            if (data.length == 1 && !hasManualData) {
              onFormDataUpdate(data);
            }
          }
        });
      });
    }
  }, [hasManualData, loadRef.current.load, type, pageShow]);

  const getClipboardData = () =>
    new Promise((resolve, reject) => {
      Taro.getClipboardData({
        success() {
          Taro.hideToast();
        },
      })
        .then(({ data: word }) => {
          if (word && word.length > 5 && word.length < 500 && Taro.usedClipboard != word) {
            Taro.usedClipboard = word;
            resolve(word);
          }
        })
        .catch(reject);
    });

  // 智能录入状态
  const switchAiStatus = useCallback((data) => {
    const { value, type } = data;
    upValue(value);
    if (lockRef.current.locked && type === 'blur') {
      lockRef.current.locked = false;
      return;
    }
    if (type !== 'input') {
      dealParseImg();
    }
    if (type !== 'blur') {
      valueRef.current = value;
      dealParseImg();
      if (!manualAi) {
        handleParse();
      }
    }
  }, []);

  const onAiAction = (value, ev) => {
    if (ev.type === 'clear') {
      if (textAreaRef.current.status == 'focus') {
        lockRef.current.locked = true;
      }
      timerRef.current.lockTimer && clearTimeout(timerRef.current.lockTimer);
      timerRef.current.lockTimer = setTimeout(() => {
        upValue('');
        valueRef.current = '';
        dealParseImg();
      }, 300);
      return;
    }
    switchAiStatus({
      value,
      type: ev && ev.type,
    });
  };

  const onFocus = () => {
    textAreaRef.current.status = 'focus';
  };

  // 失焦时智能解析，只执行一次，当页面切出时更新
  const onBlurParse = useCallback(
    once((value) => switchAiStatus({ value })),
    [pageShow],
  );

  const onBlur = () => {
    textAreaRef.current.status = 'blur';
    if (valueRef.current) {
      onBlurParse(valueRef.current);
    }
  };

  // 选择图片
  const onImagePickerChange = (paths, pickType) => {
    const path = paths[0];
    // 监听图片识别
    const changeName = 'parseImage';
    createListener(changeName, (data) => {
      switchAiStatus({
        value: data.text,
      });
      if (isFunction(onParseImg)) {
        onParseImg({ parseImg: data.img });
      }
    });
    Taro.navigator({
      url: `cutting?${qs.stringify({
        path,
        type,
        pickType,
        isEdit: isEdit ? 1 : 0, // 地址编辑
      })}`,
    });
  };

  const dealParseImg = () => {
    if (!valueRef.current) {
      if (isFunction(onParseImg)) {
        onParseImg({ parseImg: '' });
      }
    }
  };

  const onClickBar = (itemKey) => {
    let navigatorOpts;
    let listenerKey;
    switch (itemKey) {
      case 'excel':
        navigatorOpts = {
          url: 'address/import',
          options: {
            canToPay: canToPay ? 1 : 0,
            canCollection: canCollection ? 1 : 0,
          },
        };
        listenerKey = 'importAddress';
        break;
      case 'book':
        let params = {
          action: 'select',
          org: type,
          isBatch: supportBatch ? 1 : 0,
          source: 'ai',
          customer_id,
        };
        let url = 'address';
        if (type != 'send') params.multiple = '1';
        if (source === 'tcjs') {
          url = 'order/delivery/address';
          params.type = type;
        }
        navigatorOpts = {
          url,
          options: {
            ...params,
          },
        };
        listenerKey = 'addressSelect';
        break;
    }
    if (navigatorOpts) {
      createListener(listenerKey, (data) => {
        if (!data) return;
        const formData = data.current || data.list;
        const formDataSource = data.source || '';
        onFormDataUpdate(formData, formDataSource);
      });

      Taro.navigator(navigatorOpts);
    }
  };

  const parseFn = debounce(
    function (value) {
      addressFormat(value, {
        multiple: true,
        toastLoading: false,
        toastError: false,
      }).then((data) => {
        lockRef.current.parseLocked = false;
        if (!data) return;
        if (!supportBatch) {
          data = data.length > 1 ? [data[0]] : data;
        }
        if (multipleNeedConfirm) {
          setParseData(data || []);
        }
        if (data.length == 1 || (data.length > 1 && !multipleNeedConfirm)) {
          onFormDataUpdate(data);
        }
      });
    },
    600,
    {
      leading: false,
      trailing: true,
    },
  );

  const handleParse = () => {
    if (!valueRef.current || valueRef.current.length <= 9) {
      return;
    }
    lockRef.current.parseLocked = true;
    parseFn(valueRef.current);
  };

  const onSpeechChange = ({ result: value, status }) => {
    if (status == 'stop') {
      switchAiStatus({ value });
    }
  };

  const onPaste = (word) => {
    if (textAreaRef.current.status == 'focus') {
      lockRef.current.locked = true;
    }
    switchAiStatus({ value: word });
  };

  const onAiActionTips = () => {
    Taro.navigateToDocument('ai_tips');
  };

  const onBatchConfirm = () => {
    // 批量识别确认
    if (lockRef.current.parseLocked) return;
    if (textAreaRef.current.status == 'focus') {
      lockRef.current.locked = true;
    }
    onFormDataUpdate(parseData);
    switchAiStatus({ value: '' });
    setParseData([]);
  };

  let desc = '',
    parseAddressBtn = null;
  if (valueRef.current && parseData && isArray(parseData) && parseData.length > 1) {
    desc = `识别${parseData.length}条地址`;
    parseAddressBtn = '确定录入';
  }

  const wrapCls = `${className} kb-border-radius-lg kb-edit-ai`;
  return (
    <View className={wrapCls}>
      <View className='item-bar at-row at-row__align--center at-row__justify--end '>
        <View className='kb-flex-grw' onClick={onAiActionTips} hoverClass='kb-hover-opacity'>
          智能填写
          {source === 'tcjs' ? null : (
            <AtIcon
              prefixClass='kb-icon'
              value='arrow2_bottom'
              className='kb-icon-size__sm kb-color__grey kb-margin-sm-l'
            />
          )}
        </View>
        {bars.map((item) => {
          const { key: itemKey } = item;
          const iconCls = classNames('kb-icon-size__lg', `kb-color__brand`);
          return (
            <View key={itemKey} className='item-bar__item'>
              <View onClick={onClickBar.bind(null, itemKey)}>
                {itemKey === 'excel' ? (
                  <KbButton
                    size='mini'
                    type='secondary'
                    className='kb-size__sm kb-border-radius-lg'
                  >
                    Excel导入
                  </KbButton>
                ) : itemKey === 'book' ? (
                  <View className='item-bar__item--icon' hoverStopPropagation hoverClass='kb-hover'>
                    <AtIcon prefixClass='kb-icon' value={item.icon} className={iconCls} />
                  </View>
                ) : itemKey === 'camera' ? (
                  <KbImagePicker onChange={onImagePickerChange} custom>
                    <View
                      className='item-bar__item--icon'
                      hoverStopPropagation
                      hoverClass='kb-hover'
                    >
                      <AtIcon prefixClass='kb-icon' value={item.icon} className={iconCls} />
                    </View>
                  </KbImagePicker>
                ) : itemKey === 'mic' ? (
                  <KbSpeech onChange={onSpeechChange} iconColor='brand' size='mini'>
                    <View
                      className='item-bar__item--icon'
                      hoverStopPropagation
                      hoverClass='kb-hover'
                    >
                      <AtIcon prefixClass='kb-icon' value={item.icon} className={iconCls} />
                    </View>
                  </KbSpeech>
                ) : null}
              </View>
            </View>
          );
        })}
      </View>

      <View className='kb-item-wrap kb-margin-lg-tb'>
        <View className='kb-background__grey '>
          <KbTextarea
            clear
            count={false}
            paste
            height='130'
            keep
            iconCls='kb-color__grey'
            maxLength={-1}
            desc={desc}
            placeholder={placeholder}
            value={value}
            onPaste={onPaste}
            onFocus={onFocus}
            onBlur={onBlur}
            onChange={onAiAction}
            renderBars={
              <Fragment>
                {parseAddressBtn ? (
                  <KbButton
                    size='mini'
                    circle
                    type='primary'
                    onClick={onBatchConfirm}
                    className='kb-margin-sm-lr'
                  >
                    {parseAddressBtn}
                  </KbButton>
                ) : (
                  ''
                )}
                {manualAi && value ? (
                  <KbButton
                    size='mini'
                    circle
                    type='primary'
                    onClick={handleParse}
                    className='kb-margin-sm-lr'
                  >
                    识别地址
                  </KbButton>
                ) : null}
              </Fragment>
            }
          />
        </View>
      </View>
    </View>
  );
};

Index.defaultProps = {
  source: '',
  supportBatch: false,
  supportAi: supportAi,
  stopAutoAiCopyBoard: false, // 阻止自动识别剪切板
  manualAi: false, // 手动点击按钮识别地址
  multipleNeedConfirm: false, // 识别出多条需要手动点击确认
  placeholder: '输入或粘贴文本，自动识别姓名、电话和地址',
  hasManualData: true, // 手动输入框是否有数据
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
