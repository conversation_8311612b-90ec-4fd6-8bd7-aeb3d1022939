/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo, Fragment, useState, useRef } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import KbBars from '@base/components/bars';
import KbModal from '@base/components/modal';
import KbCountDown from '@base/components/count-down';
import { AtInput } from 'taro-ui';
import { noop } from '@base/utils/utils';
import request from '@base/utils/request';
import { createBars } from '../_utils/order.detail';
import { orderAction, fillOrderArrivePayPrice } from '../_utils';
import './index.scss';

const Index = (props) => {
  const {
    serviceData,
    data,
    buttonType,
    type,
    orderType,
    orderDate,
    onUpdate,
    onUpdateServiceData,
  } = props;
  // data.canPrint = true;
  const [isOpened, updateIsOpened] = useState(false);
  const [code, updateCode] = useState('');
  const [sended, updateSended] = useState(false);
  const bars = useMemo(() => createBars(data, { type, orderType }), [data]);
  const actionRef = useRef();
  const { receive_mobile: mobile = '' } = data;
  // 关闭手机号验证弹窗
  const handleClose = () => updateIsOpened(false);
  // 发送验证码
  const handleSendCode = () => {
    return new Promise((resolve, reject) => {
      updateSended(false);
      request({
        url: '/v1/WeApp/clasp',
        data: {
          mobile,
        },
        toastError: true,
        quickTriggerThen: true,
        onThen: ({ code, msg }) => {
          if (code == 0) {
            updateSended(true);
            resolve();
          } else {
            reject(new Error(msg));
          }
        },
      });
    });
  };
  // 输入验证码
  const handleInput = (value) => updateCode(value);
  // 提交验证
  const handleConfirm = () => {
    request({
      url: '/v1/WeApp/clasp',
      form: {
        code: {
          tag: '请先输入验证码',
        },
      },
      toastError: true,
      toastSuccess: true,
      data: {
        code,
        mobile,
      },
      onThen: ({ code }) => {
        if (code == 0) {
          onUpdate('checkPhone');
          handleClose();
        }
      },
    });
  };
  const handleClick = (action) => {
    if (action === 'checkPhone') {
      // 验证手机号
      updateIsOpened(true);
      actionRef.current.start();
      return;
    }
    const { order_id } = data || {};
    fillOrderArrivePayPrice(
      {
        action,
        data: { ...data, serviceData },
        order_id,
      },
      () => {
        onUpdateServiceData({ order_id });
      },
    ).then(() => {
      orderAction({
        action,
        data: {
          ...data,
          orderTypeAndDate: {
            type: orderType,
            date: orderDate,
          },
        },
      })
        .then(() => {
          console.info('orderAction ===>then');
          onUpdate(action);
        })
        .catch((err) => console.log(err));
    });
  };

  return (
    <Fragment>
      <KbBars
        type={buttonType}
        options={bars}
        onClick={handleClick}
        renderBefore={
          <View>
            {type == 'detail' && data.pay_status === 'refundCard' ? (
              <View className='kb-size__base kb-margin-md-l'>
                <Text className='kb-tool-bar__tips'>
                  抱歉，您购买的权益次卡两次未能正常使用，可点击【我要退卡】，快递员将按权益次卡未使用次数对应购卡金额折算后退款给您
                </Text>
              </View>
            ) : (
              <View>{props.children}</View>
            )}
          </View>
        }
      />
      {process.env.MODE_ENV === 'wkd' && type === 'detail' && mobile && (
        <KbModal
          top='验证手机号'
          isOpened={isOpened}
          onClose={handleClose}
          onCancel={handleClose}
          onConfirm={handleConfirm}
        >
          <View className='kb-color__grey kb-spacing-md-b'>
            短信验证码{sended ? '已' : '将'}发送至尾号为{mobile.slice(-4)}的手机
          </View>
          <View className='kb-tool-bar__code'>
            <View className='kb-tool-bar__code-input'>
              {isOpened && (
                <AtInput
                  placeholderClass='placeholder'
                  placeholder='请输入验证码'
                  border={false}
                  cursor={-1}
                  type='number'
                  maxLength={8}
                  onChange={handleInput}
                  value={code}
                />
              )}
            </View>
            <View>
              <KbCountDown onClick={handleSendCode} actionRef={actionRef} />
            </View>
          </View>
        </KbModal>
      )}
    </Fragment>
  );
};

Index.defaultProps = {
  data: {},
  buttonType: '',
  orderType: '',
  orderDate: '',
  type: 'list', // list - 列表页 detail - 详情页
  onUpdate: noop,
  onUpdateServiceData: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
