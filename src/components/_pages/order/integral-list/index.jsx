/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import KbCheckBox from '@base/components/checkbox';
import { AtButton, AtIcon } from 'taro-ui';
import classNames from 'classnames';
import './index.scss';

const month = new Date().getMonth();

const Index = (props) => {
  const { list, action, checked, onItemClick } = props;
  // 跳转积分说明
  const handleToDetail = (item) => {
    const { points, dak_id: dakId } = item;
    Taro.navigator({
      url: 'order/integral/detail',
      options: {
        points,
        dakId,
      },
    });
  };
  // 去寄件
  const handleUserPoints = (item) => {
    const { dak_id: dakId } = item;
    if (dakId) {
      Taro.kbUpdateRelationInfo({
        dakId,
      });
      Taro.navigator({
        url: 'order/edit',
        target: 'tab',
      });
    } else {
      Taro.navigator({
        url: 'order/shop',
        target: 'self',
      });
    }
  };
  const handleItemClick = (data) => {
    onItemClick(data);
  };
  const isSelect = action === 'select';
  const itemCls = classNames(
    'kb-list--wrapper at-row at-row__justify--between at-row__align--center kb-margin-sm-lr kb-margin-lg-tb ',
    {
      'kb-spacing-xl': isSelect,
    },
  );
  const itemHoverCls = isSelect ? 'kb-hover' : '';
  return (
    <View className='kb-list'>
      {list.length &&
        list.map((item) => {
          const { expire_points = 0 } = item;
          const name = isSelect
            ? item.integral_name
            : item.inn_name || item.integral_name || '快宝加盟商';
          return (
            <View
              key={isSelect ? item.points_kind : item.dak_id}
              className={itemCls}
              hoverClass={itemHoverCls}
              onClick={handleItemClick.bind(null, item)}
            >
              <View className='kb-color__brand kb-spacing-xl-lr kb-text__center'>
                <View className='kb-size__xxl kb-spacing-sm-b'>{item.points}</View>
                <View>积分</View>
              </View>
              <View className=''>
                <View className='kb-spacing-sm-tb kb-size__blod kb-size__xl'>{name}</View>
                <View className='kb-color__grey kb-size__sm'>有效期至次年的1月31日</View>
                {isSelect ? (
                  <Fragment>
                    {item.max_points && item.max_points != 99999 ? (
                      <View className='kb-spacing-sm-t kb-size__base kb-color__grey'>
                        {`单笔最多可用积分为${item.max_points}`}
                      </View>
                    ) : (
                      ''
                    )}
                  </Fragment>
                ) : (
                  <AtButton
                    className='kb-color__brand kb-size__sm kb-button__link'
                    onClick={handleToDetail.bind(null, item)}
                  >
                    积分说明 {'>'}
                  </AtButton>
                )}
                {expire_points && expire_points != '0' && !month && (
                  <View className='kb-spacing-sm-tb kb-color__orange'>
                    <AtIcon value='alert-circle' className='kb-color__orange kb-size__sm' />
                    <Text className='kb-spacing-sm-l kb-size__sm '>
                      您有{expire_points}积分将在本月底过期
                    </Text>
                  </View>
                )}
              </View>
              {isSelect ? (
                <KbCheckBox
                  onChange={handleItemClick.bind(null, item)}
                  checked={item.points_kind === checked.points_kind}
                />
              ) : (
                <View
                  hoverClass='kb-hover'
                  className='kb-spacing-lg-tb kb-send-btn'
                  onClick={handleUserPoints.bind(null, item)}
                >
                  <View className='kb-text__writing-vertical kb-color__white kb-button__link'>
                    去寄件
                  </View>
                </View>
              )}
            </View>
          );
        })}
    </View>
  );
};
Index.defaultProps = { list: [] };
Index.options = {
  addGlobalClass: true,
};
export default Index;
