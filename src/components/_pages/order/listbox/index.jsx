/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import './index.scss';

const rowNumMap = {
  sm: 3,
  lg: 3,
  mini: 6,
};
const Index = (props) => {
  const {
    list,
    onChange,
    selectted,
    className,
    itemClass,
    itemSize,
    circle,
    selectedActive,
    supportDelete,
    onDelete,
    multi,
  } = props;

  // 判断是否为多选模式：优先检查multi参数，否则根据selectted类型判断
  const isMultiMode = multi !== undefined ? multi : Array.isArray(selectted);

  // 辅助函数：判断项目是否被选中
  const isItemSelected = (item) => {
    if (isMultiMode) {
      // 多选模式：检查数组中是否包含该项的value
      if (Array.isArray(selectted)) {
        const itemValue = item.value || item.key;
        return selectted.includes(itemValue);
      }
      return false;
    }
    // 单选模式：保持原有逻辑
    return selectted === item.label;
  };

    const handelSelect = (item) => {
    if (isMultiMode) {
      // 多选模式：处理数组操作，使用value而不是label
      const currentSelected = Array.isArray(selectted) ? selectted : [];
      const itemValue = item.value || item.key;
      const isSelected = currentSelected.includes(itemValue);
      let newSelected;

      if (isSelected) {
        // 取消选中：从数组中移除
        newSelected = currentSelected.filter(value => value !== itemValue);
      } else {
        // 选中：添加到数组
        newSelected = [...currentSelected, itemValue];
      }

      onChange(newSelected);
    } else {
      // 单选模式：保持原有逻辑
      onChange(item);
    }
  };
  const handleDel = (item) => {
    Taro.kbModal({
      content: '是否要删除该物品类型？',
      onConfirm: () => {
        onDelete(item);
      },
    });
  };
  const boxCls = classNames('kb-package-list', className);
  const rowNum = rowNumMap[itemSize];
  const circleKey = circle ? 'circle' : 'primary';
  return (
    <View className={boxCls}>
      {list.map((item) => {
        const itemKey = item.key || item.value;
        const itemWrapCls = classNames(
          'kb-package-list__item',
          `kb-package-list__item-${itemSize}`,
        );
        const itemSelected = isItemSelected(item);
        const itemCls = classNames(
          'kb-package-list__item--text',
          `kb-package-list__item--text-${itemSize}`,
          `kb-package-list__item--text-border-${circleKey}`,
          itemClass,
          {
            [`kb-package-list__item--active-${selectedActive}`]: itemSelected,
          },
        );
        const hoverClass = itemSelected ? 'none' : `kb-hover kb-hover-${circleKey}`;
        return (
          <Fragment key={itemKey}>
            {item.label && (
              <View key={itemKey} className={itemWrapCls} style={{ flex: `0 0 ${100 / rowNum}%` }}>
                <View
                  hoverClass={hoverClass}
                  className={itemCls}
                  onClick={() => handelSelect(item)}
                  onLongTap={supportDelete && item.isCanDel == 1 ? () => handleDel(item) : () => {}}
                  hoverStopPropagation
                >
                  <Text className='kb-package-list__item--text-info'>{item.label}</Text>
                </View>
              </View>
            )}
          </Fragment>
        );
      })}
    </View>
  );
};
Index.defaultProps = {
  onChange: noop,
  list: [],
  itemSize: 'sm',
  selectedActive: 'primary',
  circle: true,
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
