/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-package {
  padding: 2 * $spacing-v-md 2 * $spacing-h-md;
  padding-top: $spacing-v-md;
  padding-left: 0;

  &-list {
    display: flex;
    flex-wrap: wrap;
    &__item {
      box-sizing: border-box;
      &-sm,
      &-lg {
        padding: $spacing-v-md 0 0 2 * $spacing-h-md;
      }
      &-mini {
        padding: $spacing-v-md 0 0 $spacing-h-sm;
      }
      &--text {
        &-sm {
          padding: 0 $spacing-h-md;
          font-size: $font-size-sm;
          line-height: 52px;
        }
        &-mini {
          padding: 0 $spacing-h-sm;
          font-size: 20px;
          line-height: 32px;
        }
        &-lg {
          padding: 0 $spacing-h-sm;
          font-size: $font-size-sm;
          line-height: 70px;
        }
        display: flex;
        align-items: center;
        justify-content: center;
        box-sizing: border-box;
        height: auto !important;
        overflow: hidden;

        text-align: center;

        border: $border-lightest;
        &-border {
          &-circle {
            border-radius: $border-radius-arc;
          }
          &-primary {
            border-radius: $border-radius-md;
          }
        }
        &,
        &.kb-hover {
          &-circle {
            border-radius: $border-radius-arc;
          }
          &-primary {
            border-radius: $border-radius-md;
          }
        }

        &-info {
          display: inline-block;
          max-width: 130px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }

      &--active {
        &-extra {
          position: relative;
          color: $color-brand;
          background-color: $color-brand-lightest !important;
          border-color: $color-brand;
          &::before {
            position: absolute;
            top: 0;
            right: 0;
            display: inline-block;
            width: 44px;
            height: 50px;
            overflow: hidden;
            color: $color-white;
            font-size: 16px;
            font-family: 'kb-icon' !important;
            background-color: $color-brand;
            border-radius: $border-radius-arc;
            transform: translate(28%, -47%);
            content: '\e625';
          }
        }
        &-primary {
          color: $color-white;
          background-color: $color-brand !important;
          border-color: $color-brand;
        }

        &-ghost {
          color: $color-brand !important;
          background-color: $color-white !important;
          border-color: $color-brand !important;
        }

        &.kb-hover {
          background-color: $color-brand !important;
          opacity: $opacity-active;
        }
      }
    }
  }

  &-input {
    margin: 2 * $spacing-v-md 0 0 2 * $spacing-h-md;
    background-color: $color-grey-8;
    border-radius: $border-radius-arc;
  }
  &__base {
    gap: $spacing-h-md;
    .kb-package-list__item {
      flex: unset !important;
      padding-left: 0 !important;
      &--text-sm {
        padding: 0 28px;
      }
    }
  }
}
