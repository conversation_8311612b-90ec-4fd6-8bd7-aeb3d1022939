/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useCallback } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { createListener } from '@base/utils/utils';
import request from '@base/utils/request';

function Index(props) {
  const { actionRef, phone, onChange } = props;
  const [coupon, setCoupon] = useState({});
  const [noUse, setNoUse] = useState(false);

  useEffect(() => {
    if (actionRef) {
      actionRef.current = {
        getYjDefaultCoupon,
      };
    }
  }, []);

  useEffect(() => {
    phone && getYjDefaultCoupon();
  }, [phone]);

  const getYjDefaultCoupon = () => {
    request({
      url: '/v1/WeApp/getYjDefaultCoupon',
      data: {
        phone,
      },
      toastLoading: false,
      onThen: ({ code, data }) => {
        if (code == 0) {
          setDefaultCoupon('init', data);
        }
      },
    });
  };

  const setDefaultCoupon = (type = 'init', data) => {
    let oCoupon = data || {};
    if (type == 'select' && !oCoupon.id) {
      setNoUse(true);
    }
    setCoupon(oCoupon);
    onChange(oCoupon);
  };

  const triggerChange = (e) => {
    setDefaultCoupon('select', e);
  };

  const handleClick = useCallback(
    (e) => {
      e.stopPropagation();
      createListener('cardSelect', triggerChange);
      Taro.navigator({
        url: 'order/card',
        options: {
          action: 'select',
          current: coupon.id,
          type: 'yj_coupon',
        },
      });
    },
    [phone, coupon],
  );

  return (
    <View className='kb-color__red' onClick={handleClick} hoverStopPropagation>
      {coupon.id ? '已优惠' + coupon.cost + '元>' : noUse ? '不使用券' : ''}
    </View>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
