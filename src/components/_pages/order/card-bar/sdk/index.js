/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { dateCalendar } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';

/**
 * @description 权益次卡描述文案
 */
export const cardPayTips = '权益次卡已抵扣首重运费';

function mergeObjAndParams(obj) {
  const { $router: { params } = {} } = obj || {};
  return { ...obj, ...params };
}
/**
 * @description 获取请求配置
 */
export function getCardListApi(obj, _selfPage) {
  return {
    url: () => {
      const { action: paramsAction, card_id } = mergeObjAndParams(obj);
      const userListKeys = ['select', 'normal'];
      const { state } = _selfPage || {};
      // state中的action有扫码进入异步变化的场景
      let action = (state && state.action) || paramsAction;
      const userSelf = userListKeys.includes(action);
      let url = '';
      if (process.env.MODE_ENV === 'wkd') {
        if (action == 'invalid') {
          url = '/v1/WeApp/expiredRightsCardCouponList';
        } else if (action == 'buy') {
          if (card_id) {
            url = '/v1/WeApp/getEquityCardInfo';
          } else {
            url = '/v1/WeApp/getEquityCardList';
          }
        } else {
          url = '/v1/WeApp/getCouponList';
        }
      } else {
        if (action === 'invalid') {
          url = '/api/weixin/mini/minpost/CourierEquityCard/getExpiredCoupon';
        } else {
          url = `/api/weixin/mini/minpost/CourierEquityCard/${
            userSelf ? 'userEquityCardList' : 'courierEquityCardList'
          }`;
        }
      }
      return url;
    },
    formatRequest: (req) => {
      const { customer_id } = req || {};
      if (process.env.MODE_ENV === 'wkd') {
        // 微快递
        const {
          action,
          card_id,
          s_id,
          s_type,
          order_number,
          order_id = order_number,
          type,
          phone,
          shipping_province,
          price,
        } = mergeObjAndParams(obj);
        let addReq = null;
        if (action == 'buy') {
          if (card_id) {
            addReq = {
              s_id,
              s_type: s_type || 's',
              card_id,
            };
          } else {
            addReq = {
              phone,
            };
          }
        } else {
          switch (type) {
            case 'coupon':
              req = order_id ? {} : req;
              addReq = {
                type: 'coupon',
                order_id,
                freight: price,
              };
              break;
            case 'discount_card':
              addReq = {
                type: 'card',
                phone,
                shipping_province,
              };
              break;
            case 'yj_coupon':
              addReq = {
                type: 'yj',
              };
              break;
          }
        }
        return {
          ...req,
          ...addReq,
          customer_id,
        };
      } else {
        const { dak_id, courier_id, ...rest } = req;
        if (dak_id) return { dak_id, ...rest };
        return { courier_id, ...rest };
      }
    },
    formatResponse: ({ data, data: { list, cardList } = {} }) => {
      const { type } = obj;
      list = list || cardList || (data.id && [data]);
      const listFilter = isArray(list)
        ? list.filter((item) => !type || !item.card_type || item.card_type === type)
        : [];
      if (listFilter.length) {
        return {
          code: 0,
          data: {
            list: listFilter.map(
              ({
                regional_restriction,
                invalid_time,
                start_time,
                discount_fee,
                card_type,
                disabledDec,
                ...rest
              }) => {
                if (regional_restriction == 'all') {
                  regional_restriction = ['all'];
                }
                rest.card_id = rest.card_id || rest.id;
                return {
                  regional_restriction_text: isArray(regional_restriction)
                    ? regional_restriction
                        .map((item) => (item === 'all' ? '全国' : item))
                        .join('、')
                    : null,
                  regional_restriction,
                  invalid_time: dateCalendar(invalid_time, {
                    format: 'YYYY年MM月DD日',
                    auto: false,
                  }),
                  start_time: start_time
                    ? dateCalendar(start_time, {
                        format: 'YYYY年MM月DD日',
                        auto: false,
                      })
                    : '',
                  discount_fee,
                  disabled:
                    process.env.MODE_ENV == 'wkd' && (card_type == 'discount_card' || disabledDec)
                      ? disabledDec
                        ? true
                        : rest.hasOwnProperty('is_display')
                        ? !rest.is_display
                        : false
                      : false,
                  card_type,
                  disabledDec,
                  ...rest,
                };
              },
            ),
          },
        };
      }
      return {
        data: void 0,
      };
    },
  };
}

/**
 * @description 获取支付权益次卡配置
 */
export function getPayCardApi(obj) {
  return {
    url: () => {
      let url = '/api/weixin/mini/minpost/Pay/buyEquitySign';
      if (process.env.MODE_ENV == 'wkd') {
        url = '/g_order_core/v2/PaymentStatus/wxPayEquityCard';
      }
      return url;
    },
    formatRequest: (req) => {
      if (process.env.MODE_ENV === 'wkd') {
        const { userInfo: { openid: open_id } = {} } = Taro.kbLoginData || {};
        const { list, selected } = obj.state;
        let { card_id, price, kb_id } = list.find((item) => item.card_id == selected) || {};
        let addReq = {
          open_id,
          pay_method: 'applet_rights',
          equity_cardId: card_id,
          price,
          s_id: kb_id,
        };
        return {
          ...req,
          ...addReq,
        };
      }
    },
  };
}

/**
 * @description 获取有效的权益次卡或优惠券
 */
export function getAvailableCardList(opts = {}, api) {
  return new Promise((resolve) => {
    const { orderPrice, type = '', action = 'select', card_id, ...rest } = opts;
    request({
      toastLoading: false,
      ...api,
      ...getCardListApi({ action, card_id, type, price: orderPrice, ...rest }),
      data: {
        page_size: 1,
        ...rest,
      },
      onThen: ({ data: { list, cardList } = {} }) => {
        let availableList = [];
        let n_list = list || cardList;
        if (isArray(n_list)) {
          availableList = n_list.filter((item) => {
            return (
              (!type || !item.card_type || item.card_type === type) &&
              (!orderPrice || !item.disabled)
            );
          });
        }
        resolve(availableList);
      },
    });
  });
}

/**
 *
 * @description 获取默认优惠券
 * @param {*} data
 * @returns
 */
export function getDefaultCard(data) {
  return new Promise((resolve) => {
    request({
      url: '/v1/WeApp/getDefaultCoupon',
      data,
      toastLoading: false,
      onThen: ({ code, data }) => {
        resolve(code == 0 ? data : null);
      },
    });
  });
}

/**
 * @description 检查快递员是否开放购买权益次卡
 */
export const checkEquityCardList = ({ phone }) => {
  return new Promise((resolve) => {
    request({
      url: '/v1/WeApp/getEquityCardList',
      data: {
        phone,
      },
      toastLoading: false,
      onThen: ({ data: { cardList } = {} }) => {
        resolve(isArray(cardList) && cardList.length > 0);
      },
    });
  });
};

/**
 * @description 获取默认的权益次卡
 */
export const getDefaultEquityCard = (data) => {
  return new Promise((resolve) => {
    request({
      url: '/v1/WeApp/getDefaultEquityCard',
      data,
      toastLoading: false,
      onThen: (res) => {
        if (res.code == 0) {
          resolve(res.data);
        }
      },
    });
  });
};
