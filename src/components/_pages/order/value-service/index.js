/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { serviceConfig } from '@/components/_pages/order/_utils/order.service';
import apis from '@/utils/apis';
import KbDownContainer from '@base/components/down-container';
import KbSwitch from '@base/components/switch';
import request from '@base/utils/request';
import { createListener, debounce, isFullData, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import { AtButton, AtIcon } from 'taro-ui';
import './index.scss';

const MustPriceArrService = ['proPrice', 'collection', 'toPay'];

class Index extends Taro.Component {
  static options = {
    addGlobalClass: true,
  };

  static defaultProps = {
    source: 'kd', //tcjs同城急送
    custom: false,
    extraInfo: {},
    relationInfo: {},
    showProPrice: false,
    serviceText: '',
    clean: false,
    onChange: noop,
  };

  constructor() {
    super(...arguments);
    this.state = {
      decVal: '', //物品价值
      isShowServiceList: false, //展开收缩开关
      isOpenGoodsValue: false, //强制声明物品价值
      serviceList: [],
    };
    this.getServiceList = debounce(this.getServiceList, 500, {
      trailing: true,
      leading: false,
    });
  }

  componentDidMount() {
    this.getServiceList();
  }

  componentDidUpdate(prevProps) {
    const { relationInfo, data } = prevProps;
    const { relationInfo: nextRelationInfo, data: nextData } = this.props;
    if (nextRelationInfo != relationInfo) {
      this.getServiceList();
    }
    if (nextData && nextData != data) {
      nextData.clean && this.getServiceList('reset');
    }
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer);
  }

  onPostMessage = (data) => {
    const { name_en, InputMoney, costMoney } = data;
    if (name_en == 'goodsValue' || name_en == 'proPrice') {
      this.setState(
        {
          isShowServiceList: true,
          decVal: InputMoney,
        },
        () => {
          this.dealService('set', {
            goodsValue: {
              price: InputMoney,
              isChecked: InputMoney ? true : false,
            },
            proPrice: {
              decVal: InputMoney,
              price: costMoney || '',
              isChecked: costMoney ? true : false,
            },
          });
        },
      );
    } else {
      this.dealService('set', {
        [name_en]: {
          price: InputMoney,
          isChecked: true,
        },
      });
    }
  };

  getServiceList = () => {
    const {
      source,
      relationInfo: { type: relationType, courier_id, brand, platform, courier = {} },
    } = this.props;
    let reqData = {};
    if (relationType == 'courier') {
      reqData = {
        courier_id,
      };
    } else if (relationType == 'brand') {
      if (platform == 'yjkd_courier') {
        reqData = {
          courier_id: courier.courier_id,
        };
      } else if (['sf', 'sfky'].includes(brand)) {
        reqData = {
          brand: 'sf',
        };
      } else if (platform == 'yjkd_brand') {
        //优寄快递除优寄快递员外，均拉取默认京东增值服务
        reqData = {
          brand: 'jd',
        };
      }
    } else if (source == 'tcjs') {
      reqData = {
        brand,
      };
    }
    var params = {
      isShowServiceList: false,
      isOpenGoodsValue: false,
      isOnlyGoodsValue: false,
      decVal: '',
    };
    const disabledServiceList = Taro.kbGetGlobalDataOnce('disabledServiceList');
    if (disabledServiceList) return;
    if (isEmpty(reqData) || !isFullData(reqData)) {
      this.setState(params);
      this.setServiceListData({ serviceList: [] });
      return;
    }
    request({
      url: apis[`service.${source || 'kd'}.list`],
      data: reqData,
      toastLoading: false,
      onThen: (res) => {
        var serviceList = [];
        if (res.code == 0 && res.data && res.data.length > 0) {
          res.data.map((item) => {
            item.isChecked = false;
            item = Object.assign(item, serviceConfig[item.name_en] || {});
            if (item.name_en == 'goodsValue') {
              params.isOpenGoodsValue = true;
            }
            if (item.name_en == 'proPrice') {
              item.proPriceEnd = item.proPriceEnd <= 0 ? '20000' : item.proPriceEnd;
            }
            serviceList.push(item);
          });
          //仅开启了声明物品价值
          if (serviceList.length == 1 && serviceList[0].name_en == 'goodsValue') {
            params.isOnlyGoodsValue = true;
          }
        }
        this.setState(params, () => {
          this.setServiceListData({ serviceList });
        });
      },
    });
  };

  dealService = (action, service = {}) => {
    /**
     * 操作增值服务列表
     * action  操作方式
     * service 某项服务英文名
     * @returns
     *  get 有就返回对象，无则返回空，可用于判断是否含某项服务
     *  set 无返回;
     */
    //todo:此处存在引用赋值问题，待优化
    let { serviceList = [] } = this.state;
    let list = [...serviceList];
    if (action == 'set') {
      for (var key in service) {
        var item = service[key] || '';
        for (var i = 0; i < serviceList.length; i++) {
          let oItem = { ...serviceList[i] };
          if (oItem.name_en == key) {
            for (var key2 in item) {
              oItem[key2] = item[key2];
            }
            list[i] = oItem;
            break;
          }
        }
      }
      this.setServiceListData({ serviceList: list });
    } else if (action == 'get') {
      var arr =
        (serviceList.length > 0 && serviceList.filter((item) => item.name_en == service)) || [];
      return arr[0] || '';
    }
  };

  setServiceListData = (data = {}) => {
    const { serviceList = [] } = data || {};
    const { isOpenGoodsValue, decVal } = this.state;
    if (this.checkCollectionAndToPay(serviceList)) return;
    this.setState({
      serviceList,
    });
    let vas_money = 0,
      vas_ids = [],
      serviceTextArr = [],
      oService = {},
      vas_list = [];
    serviceList.map((item) => {
      if (item.isChecked) {
        vas_ids.push(item.id);
        serviceTextArr.push(item.name);
        if (item.isActualcost && item.price) {
          vas_money += 1 * item.price;
          vas_list.push(item);
        }
        //检查派件电联
        if (item.name_en == 'tel') {
          oService.telIco = item.price || 2;
        }
        //检查保价增值服务
        if (item.name_en == 'proPrice') {
          oService.proPrice = item.price;
        }
        //检查声明物品价值
        oService.decVal = decVal;
        //检查代收货款增值服务
        if (item.name_en == 'collection') {
          oService.collection_amount = item.price;
        }
        //检查到付增值服务
        if (item.name_en == 'toPay') {
          oService.to_pay_amount = item.price;
        }
        //检查签收确认服务
        if (item.name_en == 'sign') {
          oService.needReturnTrackingNo = 1;
        }
        //检查号码保护服务
        if (item.name_en == 'privacyNumber') {
          oService.privacyNumber = 1;
        }
        //检查取货码服务
        if (item.name_en == 'finish_code') {
          oService.finish_code = 1;
        }
      }
    });
    let detail = {
      serviceList,
      decVal,
      isOpenGoodsValue,
      vas_ids: vas_ids.join(','),
      vas_money,
      vas_list,
      serviceText: serviceTextArr.join(','),
      oService,
    };
    this.props.onChange(detail);
  };

  checkCollectionAndToPay = (serviceList) => {
    /**
     * @description 代收货款和到付不能同时选择
     */
    let collection = serviceList.find((item) => item.name_en == 'collection' && item.isChecked);
    let toPay = serviceList.find((item) => item.name_en == 'toPay' && item.isChecked);
    if (collection && toPay) {
      this.timer && clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        Taro.kbModal({
          closeTriggerCancel: false,
          content: '代收货款与到付不能同时选择',
          confirmText: '选择到付',
          cancelText: '选择代收',
          onConfirm: () => {
            this.dealService('set', {
              collection: {
                isChecked: false,
                price: '',
              },
              toPay: {
                isChecked: true,
                price: toPay.price,
              },
            });
          },
          onCancel: (target) => {
            if (target == 'button') {
              this.dealService('set', {
                collection: {
                  isChecked: true,
                  price: collection.price,
                },
                toPay: {
                  isChecked: false,
                  price: '',
                },
              });
            } else {
              this.dealService('set', {
                collection: {
                  isChecked: false,
                  price: '',
                },
                toPay: {
                  isChecked: false,
                  price: '',
                },
              });
            }
          },
        });
      }, 800);
      return true;
    }
    return false;
  };

  onOpen = () => {
    const { isShowServiceList } = this.state;
    this.setState({
      isShowServiceList: !isShowServiceList,
    });
  };

  onSwitch = (ev) => {
    let { service } = ev;
    const { name_en, isChecked, price, proPriceStart } = service || {};
    //保价、代收货款、到付等需要填写价格的服务点击时，若无价格，则跳转详情页
    let MustPriceIndex = MustPriceArrService.findIndex((k) => k == name_en);
    if (MustPriceIndex > -1 && !price) {
      this.onJumpTo({
        service,
      });
      return;
    }
    //当填写的物品价值高于配置的最低保价金额时，必须参与保价
    if (name_en == 'proPrice') {
      let goodsValueService = this.dealService('get', 'goodsValue');
      let goodsValue = goodsValueService ? goodsValueService.price : '';
      if (proPriceStart && goodsValue >= proPriceStart) {
        Taro.kbToast({
          text: `您声明的物品价值超过${proPriceStart}元，必须参与保价`,
        });
        return;
      }
    }
    this.dealService('set', {
      [name_en]: {
        isChecked: !isChecked,
      },
    });
  };

  onJumpTo = (ev) => {
    let { service = {}, action } = ev || {};
    let url = 'order/service',
      target = 'blank',
      options = {};
    const {
      relationInfo,
      extraInfo: { product_code },
    } = this.props;
    let { decVal, isOpenGoodsValue } = this.state;
    const { name_en } = service || {};
    if (action == 'goodsValue') {
      service = this.dealService('get', action);
      let proPriceService = this.dealService('get', 'proPrice') || {};
      service.cost = proPriceService.cost;
      service.proPriceStart = proPriceService.proPriceStart;
      service.proPriceEnd = proPriceService.proPriceEnd;
      service.proPriceSwitch = proPriceService.isChecked ? 1 : 0;
    }
    if (name_en == 'goodsValue' || name_en == 'proPrice') {
      service.decVal = decVal; //物品价值
      service.isOpenGoodsValue = isOpenGoodsValue; //是否开启强制声明物品价值
    }
    options = {
      product_code,
      ...service,
    };
    //跳转H5
    if (service.h5) {
      target = 'webview';
      url = service.h5;
    }
    Taro.kbSetGlobalData('PageGlobaData', {
      relationInfo,
    });
    createListener('serviceSelect', this.onPostMessage);
    Taro.navigator({
      url,
      target,
      options,
    });
  };

  render() {
    const { isShowServiceList, isOpenGoodsValue, isOnlyGoodsValue, decVal, serviceList } =
      this.state;
    const { serviceText, showProPrice, custom } = this.props;

    return (
      <Fragment>
        {custom
          ? this.props.children
          : serviceList.length > 0 && (
              <View className='kb-value-service'>
                <View className='kb-form__item' onClick={this.onOpen}>
                  <View className='at-row at-row__justify--between at-row__align--center'>
                    <View className='item-title'>增值服务</View>
                    <View className='at-row at-row__justify--end at-row__align--center kb-spacing-sm-r'>
                      <View className='kb-margin-sm-r'>
                        {serviceText ? (
                          <View class='kb-value-service__title--text'>{serviceText}</View>
                        ) : isOpenGoodsValue ? (
                          <View class='at-row at-row__align--center'>
                            <Text class='kb-color__grey kb-size__base kb-margin-sm-r'>
                              先声明物品价值(必填)
                            </Text>
                            <AtButton
                              type='primary'
                              className='kb-button__mini'
                              circle
                              onClick={(ev) => {
                                ev.stopPropagation();
                                this.onJumpTo({
                                  action: 'goodsValue',
                                });
                              }}
                            >
                              {decVal ? '￥' + decVal : '点击此处'}
                            </AtButton>
                          </View>
                        ) : (
                          showProPrice && (
                            <View className='kb-color__brand kb-size__base kb-margin-sm-r'>
                              请选择保价(必填)
                            </View>
                          )
                        )}
                      </View>
                      <AtIcon
                        prefixClass='kb-icon'
                        value='arrow'
                        className='kb-color__grey kb-icon-size__base'
                      />
                    </View>
                  </View>
                </View>
                <KbDownContainer isOpened={isShowServiceList && !isOnlyGoodsValue}>
                  {serviceList &&
                    serviceList.map((item) => {
                      return (
                        <Fragment>
                          {item.name_en != 'goodsValue' && (
                            <View key={item.id} className='kb-value-service__item'>
                              <View
                                class='at-col'
                                onClick={() => this.onJumpTo({ service: item })}
                                hoverClass='kb-hover-opacity'
                              >
                                <View class='at-row at-row__align--center'>
                                  <View>{item.name}</View>
                                  <View class='kb-margin-sm-l'>
                                    <AtIcon
                                      prefixClass='kb-icon'
                                      value='help2'
                                      color='#999'
                                      size='18'
                                    />
                                  </View>
                                </View>
                                <View class='kb-size__sm kb-color__grey kb-margin-sm-t'>
                                  {item.price ? `费用:${item.price || '--'}元` : item.desc}
                                </View>
                              </View>
                              <View onClick={() => this.onSwitch({ service: item })}>
                                <KbSwitch
                                  onChange={() => this.onSwitch({ service: item })}
                                  checked={item.isChecked}
                                  color='#0099ff'
                                  mask
                                  ghost
                                />
                              </View>
                            </View>
                          )}
                        </Fragment>
                      );
                    })}
                </KbDownContainer>
              </View>
            )}
      </Fragment>
    );
  }
}

export default Index;
