/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect } from "@tarojs/taro";
import { View, Image } from "@tarojs/components";
import "./index.scss";

function Index() {
  const [show, setShow] = useState(true);

  useEffect(() => {
    clearTimeout(animationTimer);
    var animationTimer = setTimeout(() => {
      setShow(false);
    }, 3000);
    return () => {
      animationTimer && clearTimeout(animationTimer);
    };
  }, []);

  return (
    <View>
      {show && (
        <View className="kb-animation-hands-slide">
          <View className="kb-animation-hands-slide--pillar"></View>
          <View className="kb-animation-hands-slide--hand">
            <Image
              src="https://cdn-img.kuaidihelp.com/wkd/miniApp/new/hand.png"
              width="88"
              height="64"
              lazyLoad
            />
          </View>
        </View>
      )}
    </View>
  );
}

export default Index;
