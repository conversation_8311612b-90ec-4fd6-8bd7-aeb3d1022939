/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useEffect, useRef } from '@tarojs/taro';
import { View, ScrollView } from '@tarojs/components';
import KbHandsSlideAnimation from '@/components/_pages/order/move-area/hands-slide-animation';
import isNumber from 'lodash/isNumber';
import { noop } from '@base/utils/utils';
import './index.scss';

function Index(props) {
  const { data, onChange = noop, actionRef: propsActionRef } = props;
  const ownerInstance = {
    isToped: true,
  };

  const actionRef = useRef({});
  const [state] = useState({
    data: {
      curHeight: 0,
      smallHeight: 0,
      maxHeight: 0,
    },
  });
  const [style, setStyle] = useState({});
  const [showHands, setShowHands] = useState(true);
  const [status, setStatus] = useState('');
  const [scrollTop, updateScrollTop] = useState();
  const [scrollY, updateScrollY] = useState(false);

  if (propsActionRef && !propsActionRef.current) {
    propsActionRef.current = {
      switchStatus: (status) => {
        changeTransform(status, 'touchend');
        onUpdateStatus(status);
      },
    };
  }

  useEffect(() => {
    const { maxHeight = 0 } = data || {};
    if (maxHeight === 0) return;
    state.data = data;
    var curHeight = state.data.curHeight;
    var smallHeight = state.data.smallHeight;
    if (curHeight == smallHeight) {
      //兼容最小模式
      state.data.curHeight = smallHeight;
    }
    if (curHeight) {
      changeTransform(curHeight, 'touchend');
      onUpdateStatus(checkStatus());
    }
  }, [data]);

  const onUpdateStatus = (status) => {
    onChange({ status });
    setStatus(status);
    updateScrollTop(status === 'min' ? 0 : -1);
    clearTimeout(actionRef.current.timer);
    actionRef.current.timer = setTimeout(() => {
      // 延迟切换允许滚动状态，保证最小化时置顶处理
      updateScrollY(status === 'max');
    }, 100);
  };

  // 判断是否到达最大模式
  const checkStatus = () => {
    var data = state.data;
    var curHeight = data.curHeight;
    var maxHeight = data.maxHeight;
    var smallHeight = data.smallHeight;
    return curHeight + 10 >= maxHeight ? 'max' : curHeight === smallHeight ? 'small' : 'min';
  };

  const changeTransform = (status, type) => {
    var curHeight =
      state.data[status + 'Height'] ||
      1 * (isNumber(status) ? status.toFixed(2) : state.data.minHeight);
    setStyle({
      transform: 'translateY(' + -1 * curHeight + 'px)',
      transition: type === 'touchend' ? 'transform 0.3s ease' : '',
      'will-change': type === 'touchmove' ? 'transform' : 'auto',
    });
    state.data.curHeight = curHeight;
  };

  const onHandler = (ev) => {
    var { type, touches } = ev;
    var touch = touches[0] || { pageX: 0, pageY: 0 };
    var offsetY, curHeight;
    // 模块全部展开，且未滚动到顶部；
    if (checkStatus() === 'max' && ownerInstance.isToped === false) {
      return;
    }
    type = type.toLowerCase();
    switch (type) {
      case 'touchstart':
        state.startHeight = state.data.curHeight;
        state.isToped = true;
        state.touch = touch;
        break;
      case 'touchmove':
        // var pageX = state.touch.pageX;
        var pageY = state.touch.pageY;
        // var offsetX = touch.pageX - pageX;
        offsetY = touch.pageY - pageY;
        curHeight = state.startHeight - offsetY;
        if (curHeight >= state.data.maxHeight) {
          curHeight = state.data.maxHeight;
        } else if (curHeight <= state.data.minHeight) {
          curHeight = state.data.minHeight;
        }
        changeTransform(curHeight, type);
        if (!state.isMoved) {
          setShowHands(false);
          state.isMoved = true;
        }
        break;
      case 'touchend':
        offsetY = ev.changedTouches[0].pageY - state.touch.pageY;
        var offsetYAbs = Math.abs(offsetY);
        if (offsetYAbs <= 10) return;
        changeTransform(offsetY < 0 ? 'max' : 'min', type);
        onUpdateStatus(checkStatus());
        break;
    }
  };

  const onScroll = (ev) => {
    ownerInstance.isToped = ev.detail.scrollTop <= 50;
  };

  const styleObj = data
    ? {
        height: `${data.maxHeight}px`,
        ...style,
      }
    : null;

  return (
    <View
      className='kb-move-area'
      style={styleObj}
      onTouchStart={onHandler}
      onTouchMove={onHandler}
      onTouchEnd={onHandler}
    >
      {status === 'min' && showHands && (
        <View class='kb-move-area__hands'>
          <KbHandsSlideAnimation />
        </View>
      )}
      <View className='kb-move-area__inner'>
        {status === 'small' && <View className='kb-move-area__sign' />}
        <View className='kb-move-area__inner--pt'>
          <ScrollView
            className='kb-move-area__scrollview'
            onScroll={onScroll}
            scrollY={scrollY}
            scrollTop={scrollTop}
          >
            {props.children}
          </ScrollView>
        </View>
      </View>
    </View>
  );
}

Index.defaultProps = {
  data: null,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
