/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useState, useRef, Fragment, useEffect } from '@tarojs/taro';
import KbHandsSlideAnimation from '@/components/_pages/order/move-area/hands-slide-animation';
import { View } from '@tarojs/components';
import { noop } from '@base/utils/utils';
import './index.scss';

function Index(props) {
  const { data: propsData, onChange = noop, actionRef: propsActionRef } = props;
  const [showHands, setShowHands] = useState(true);
  const [scrollTop, setScrollTop] = useState();
  const [status, setStatus] = useState('');
  const [data, updateData] = useState(propsData);
  const actionRef = useRef({});

  const switchStatus = () => {
    updateData({
      ...actionRef.current.data,
      ts: new Date().getTime(),
    });
  };

  useEffect(() => {
    if (scrollTop === -1) {
      setScrollTop(0);
    }
  }, [scrollTop]);

  const setScrollTopFn = () => {
    setScrollTop(-1);
  };

  if (propsActionRef && !propsActionRef.current) {
    propsActionRef.current = {
      switchStatus,
      setScrollTopFn,
    };
  }

  useEffect(() => {
    actionRef.current.data = propsData;
    switchStatus(propsData);
  }, [propsData]);

  const onUpdateStatus = (status) => {
    onChange({ status });
    setStatus(status);
  };

  const triggerChange = (e) => {
    const { type, status } = e.detail;
    onUpdateStatus(status);
    type === 'touchend' && setShowHands(false);
  };

  return (
    <area-weapp scrollTop={scrollTop} data={data} onChange={triggerChange}>
      <Fragment>
        <View slot='before'>
          {status === 'min' && showHands && (
            <View class='kb-move-area__hands'>
              <KbHandsSlideAnimation />
            </View>
          )}
        </View>
        {props.children}
      </Fragment>
    </area-weapp>
  );
}

Index.defaultProps = {
  data: null,
};

Index.options = {
  addGlobalClass: true,
};

Index.config = {
  usingComponents: {
    'area-weapp': './area-weapp/index',
  },
};

export default Index;
