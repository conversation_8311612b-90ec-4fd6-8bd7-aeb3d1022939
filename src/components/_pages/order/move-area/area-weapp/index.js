/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

Component({
  options: {
    multipleSlots: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    data: {
      type: "Object",
      value: {}
    },
    scrollTop:{
      type: "Number",
      value: -1
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    status: "",
    showHands: true
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onUpdateStatus({ status, type }) {
      // 更新当前模块状态
      this.triggerEvent("change", {
        status,
        type
      });
      this.setData({
        status
      });
    },
    onSetData(data) {
      this.setData(data);
    }
  }
});
