function changeTransform(instance, state, status, type) {
  var curHeight = state.data[status + "Height"] || (1 * status.toFixed(2));
  instance.setStyle({
    transform: 'translateY(' + (-1 * curHeight) + 'px)',
    transition: type === 'touchend' ? "transform 0.3s ease" : "",
    "will-change": type === 'touchmove' ? "transform" : "auto"
  });
  state.data.curHeight = curHeight;
}
// 判断是否到达最大模式
function checkStatus(state) {
  var data = state.data;
  var curHeight = data.curHeight;
  var maxHeight = data.maxHeight;
  var smallHeight = data.smallHeight;
  return (curHeight + 10 >= maxHeight && maxHeight > 0) ? "max" : curHeight === smallHeight ? "small" : "min";
}
module.exports = {
  scroll: function (ev, ownerInstance) {
    var state = ownerInstance.getState();
    state.isToped = ev.detail.scrollTop <= 25;
  },
  handler: function (ev, ownerInstance) {
    var instance = ev.instance;
    var type = ev.type;
    var touch = ev.touches[0] || { pageX: 0, pageY: 0 }
    var state = instance.getState();
    var offsetX, offsetY, curHeight;
    // 模块全部展开，且未滚动到顶部；
    if (checkStatus(state) === "max" && ownerInstance.getState().isToped === false) {
      return;
    }
    switch (type) {
      case "touchstart":
        state.startHeight = state.data.curHeight;
        state.isToped = true;
        state.touch = touch;
        break;
      case "touchmove":
        var pageX = state.touch.pageX;
        var pageY = state.touch.pageY;
        offsetX = touch.pageX - pageX;
        offsetY = touch.pageY - pageY;
        curHeight = state.startHeight - offsetY;
        if (curHeight >= state.data.maxHeight) {
          curHeight = state.data.maxHeight;
        } else if (curHeight <= state.data.minHeight) {
          curHeight = state.data.minHeight;
        }
        changeTransform(instance, state, curHeight, type);
        if (!state.isMoved) {
          ownerInstance.callMethod('onSetData', {
            showHands: false
          })
          state.isMoved = true;
        }
        break;
      case "touchend":
        offsetY = ev.changedTouches[0].pageY - state.touch.pageY;
        var offsetYAbs = Math.abs(offsetY);
        if (offsetYAbs <= 10) return;
        changeTransform(instance, state, offsetY < 0 ? "max" : "min", type);
        ownerInstance.callMethod("onUpdateStatus", { status: checkStatus(state), type: type });
        break;
    }

  },
  propObserver: function (newValue, oldValue, ownerInstance, instance) {
    if (newValue && newValue !== oldValue) {
      var state = instance.getState();
      state.data = newValue;
      var maxHeight = state.data.maxHeight;
      var curHeight = state.data.curHeight;
      var smallHeight = state.data.smallHeight;
      if (!maxHeight) return
      if (curHeight === smallHeight) {
        // 兼容最小模式
        state.data.minHeight = smallHeight;
      }
      if (curHeight) {
        changeTransform(instance, state, curHeight, "touchend");
        ownerInstance.callMethod("onUpdateStatus", { status: checkStatus(state), type: "observer" });
      }
    }
  }
}

