<!--
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
-->
<wxs src="./sdk/touch.wxs" module="touch"></wxs>
<view
  class="kb-move-area"
  style="height:{{data.maxHeight}}px"
  change:prop="{{touch.propObserver}}"
  prop="{{data}}"
  bindtouchstart="{{touch.handler}}"
  catchtouchmove="{{touch.handler}}"
  bindtouchend="{{touch.handler}}"
>
    <slot name="before"></slot>
    <view class="kb-move-area__inner">
        <view class="kb-move-area__sign" wx:if="{{status==='small'}}">
            <image class="kb-move-area__sign--image" layout="flex" src="/images/icon/arrow-up.png" />
        </view>
        <view class="kb-move-area__inner--pt">
            <scroll-view class="kb-move-area__scrollview" bindscroll="{{touch.scroll}}" scroll-y="{{status==='max'}}" scroll-top="{{status==='min'?0:status==='max'?scrollTop:-1}}">
                <slot></slot>
            </scroll-view>
        </view>
    </view>
</view>
