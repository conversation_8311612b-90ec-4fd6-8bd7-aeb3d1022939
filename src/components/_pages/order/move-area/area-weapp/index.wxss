/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-move-area {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    z-index: 1;
    transform: translateY(0);
    padding: 0 20rpx;
}

.kb-move-area__inner {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    height: 100%;
}

.kb-move-area__inner--pt {
    height: 100%;
    box-sizing: border-box;
    padding-top: 20rpx;
}

.kb-move-area__sign {
    height: 30rpx;
    background: #f4f4f4;
    display: flex;
    align-items: center;
    justify-content: center;
}

.kb-move-area__sign--image {
    width: 80rpx;
    height: 18rpx;
}

.kb-move-area__scrollview {
    height: 100%;
}