/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { MovableArea, MovableView, ScrollView, View } from '@tarojs/components';
import PageLayout from '~base/components/page/layout';
import OrderOperateDepart from './operate/depart';
import LongList from '~base/components/long-list';
import { useMoveArea } from './_utils/move';
import OrderDetailContent from './content';
import { useEffect, useRef, useState } from 'react';
import OperateDepartModal from './operate/depart/modal';
import OperateCancelModal from './operate/cancel/modal';
import OrderDetailMap from './map';
import { checkOrderCanCancel, checkOrderCanDepart } from './operate/_utils';
import OrderDetailOperate from './operate';
import './index.scss';

const OrderDetail = (props) => {
  const { top, onMoveChange, order_id } = props;
  const [orderData, setOrderData] = useState(null);
  const [showMap, setShowMap] = useState(false);
  const [showDepart, setShowDepart] = useState(false);
  const [showCancel, setShowCancel] = useState(false);
  const detailRef = useRef();
  const {enableRefresh, disabled, posY, id, isMoveMax, isMoveMin, onChange, onVTouchMove, onTouchEnd, onActiveChange, onTouchStart } = useMoveArea({
    onMoveChange
  });
  const handleOrderDetailLoad = (data) => {
    setOrderData(data);
    setShowDepart(checkOrderCanDepart(data));
    setShowCancel(checkOrderCanCancel(data));
  }

  const handleRefresh = () => detailRef.current?.loader?.();
  const handleMapReady = () => setShowMap(true);

  // 地图可用切换
  useEffect(() => {
    onActiveChange(showMap);
  }, [showMap]);

  return (
    <>
      <PageLayout
        renderFooter={
          <OrderDetailOperate data={orderData} onSuccess={handleRefresh} />
        }
      >
        <View className='order-detail' id={id} style={{ paddingTop: top }}>
          <OrderDetailMap className='order-detail__map' data={orderData} onReady={handleMapReady} />
          <MovableArea className='order-detail__move-area'>
            <MovableView
              onTouchEnd={onTouchEnd}
              onChange={onChange}
              onTouchStart={onTouchStart}
              onVTouchMove={onVTouchMove}
              direction='vertical'
              x={0}
              y={showMap ? posY : 0}
              disabled={disabled}
              inertia
              className='order-detail__move-view'
              animation
              friction={2}
            >
              <OrderDetailContent enableRefresh={enableRefresh} showMap={showMap} actionRef={detailRef} isMoveMax={isMoveMax} onLoad={handleOrderDetailLoad} order_id={order_id} />
            </MovableView>
          </MovableArea>
        </View>
      </PageLayout>
      {process.env.MODE_ENV === 'server' && showDepart && <OperateDepartModal />}
      {showCancel && <OperateCancelModal />}
    </>
  );
};

export default OrderDetail;
