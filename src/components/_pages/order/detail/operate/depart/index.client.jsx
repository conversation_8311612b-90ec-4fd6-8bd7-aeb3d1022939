import { AtButton } from "taro-ui"
import Popconfirm from "~base/components/popconfirm";
import { orderVehicleGo } from "~/services/order/detail";
import './index.scss';

const OrderOperateDepart = (props) => {
    const { data, onSuccess, className } = props;

    const handleConfirm = async () => {
        const res = await orderVehicleGo({
            order_id: data?.order_id,
            auth_code: data?.auth_code
        })
        const isSuccess = `${res?.code}` === '0';
        if (isSuccess) {
            onSuccess?.();
        }
        return isSuccess;
    }

    return (
        <Popconfirm confirmText='开始发车' cancelText='暂不发车' buttonProps={{ type: 'primary', circle: true, className }} title="发车提示" content='发车前，确保已完成货物装卸并做好安全检查' onConfirm={handleConfirm}>完成装卸货，开始发车</Popconfirm>
    )
}

export default OrderOperateDepart;