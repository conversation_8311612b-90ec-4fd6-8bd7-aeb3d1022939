import { AtButton } from "taro-ui"
import { View } from "@tarojs/components";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { useEffect } from "react";
import { OperateDepartModalName } from "./_utils";
import { useCheckOrderTruckDepart } from "./_utils/check";
import './index.scss';

const OrderOperateDepart = (props) => {
    const { data, onSuccess, className } = props;
    useCheckOrderTruckDepart(data);
    const { onOpenChange, form } = useActionContext(OperateDepartModalName, {
        onSuccess
    });

    const handleClick = () => {
        form?.setFieldsValue?.({ order_id: data?.order_id });
        onOpenChange(true)
    }

    return (
        <AtButton type='primary' circle onClick={handleClick} className={className}>开始发车</AtButton>
    )
}

export default OrderOperateDepart;