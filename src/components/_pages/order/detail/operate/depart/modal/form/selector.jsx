import { Text, View } from "@tarojs/components";
import FormSelectorField from "~base/components/form/selector";
import { getLineEditStopPoint, getOrderDepartPoint } from "~/services/truck/order";
import React, { useCallback, useMemo } from "react";
import { AtButton } from "taro-ui";
import { makePhoneCall } from "~base/utils/utils";
import pick from "lodash/pick";

/**
 * 
 * @param {{
 *   value?:{value:string;label:string;};
 *   onChange?:(value?:{value:string;label:string;})=>void;
 *   label?:string;
 *   className?:string;
 *   title?:string;
 *   hoverClass?:'kb-hover'|'none'|'kb-hover-opacity';
 *   readOnly?:boolean;
 *   placeholder?:string;
 *   params?:any;
 *   action?:'normal'|'line-edit';
 *   actionSheetProps?:{multiple?:boolean;optionRender?:(text:string)=>React.ReactNode;}
 * }} props 
 * @returns 
 */
const OperateDepartSelector = (props) => {
    const { action, children, readOnly, hoverClass, value, onChange, placeholder, label, title = `请选择${label}`, className, actionSheetProps, params } = props;
    const { vehicle_owner_phone, vehicle_owner_id, company_name } = params || {};
    const { enableMore = !vehicle_owner_id } = props;

    // 联系车主
    const handleMakePhoneCall = () => makePhoneCall(vehicle_owner_phone);

    const actionSheetPropsMerged = useMemo(() => ({
        enableMore,
        align: 'start',
        title,
        closable: true,
        showSearch: true,
        className: 'depart-point-action-sheet',
        renderEmptyFooter: (
            <>
                {
                    vehicle_owner_phone && (
                        <View className="footer-inner">
                            <AtButton circle size='small' type='primary' onClick={handleMakePhoneCall}>联系车主</AtButton>
                        </View>
                    )
                }
            </>
        ),
        optionRender: (text) => (
            <>
                <Text className='kb-icon kb-icon-location kb-color__brand kb-icon-size__base kb-margin-md-r' />
                <Text className='kb-sheet__button-text'>{text}</Text>
            </>
        ),
        ...actionSheetProps
    }), [actionSheetProps, vehicle_owner_phone]);

    // 额外补充的请求数据
    const extraReq = { page_size: 20, vehicle_owner_id, company_name };
    const request = (req) => action === 'line-edit' ? getLineEditStopPoint({ ...req, ...extraReq }) : getOrderDepartPoint({ ...req, ...extraReq });

    return (
        <FormSelectorField
            readOnly={readOnly}
            hoverClass={hoverClass}
            value={value}
            onChange={onChange}
            label={label}
            placeholder={placeholder}
            className={className}
            request={request}
            actionSheetProps={actionSheetPropsMerged}
        >
            {children}
        </FormSelectorField>
    )
}

export default OperateDepartSelector;