import { View } from "@tarojs/components";
import { useEffect, useImperativeHandle } from "react";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import KbModal from "~base/components/modal";
import OperateDepartModalForm from "./form";
import { OperateDepartModalName } from "../_utils";
import { useCheckOrderTruckDepart } from "../_utils/check";

const OperateDepartModal = () => {
    const { start } = useCheckOrderTruckDepart();
    const { open, onOpenChange, form } = useActionContext(OperateDepartModalName, {
        form: {
            order_id: {},
            from_stop: {
                tag: '请选择发车点'
            }
        },
        api: {
            url: '/g_autovd/v2/Vehicle/Order/vehicleGo',
            toastLoading: true,
            toastSuccess: true,
            toastError: true,
            formatRequest: ({ order_id, from_stop }) => ({
                order_id,
                from_stop_id: from_stop?.value
            }),
        }
    });

    const handleChange = v => form?.setFieldsValue?.({ from_stop: v });

    const handleClose = () => onOpenChange(false);
    const handleConfirm = () => form?.onFinish?.().then(res => {
        const isSuccess = `${res?.code}` === '0';
        if (isSuccess) {
            start({
                order_id: form?.data?.order_id
            });
        }
        return isSuccess;
    });

    return (
        <KbModal isOpened={open} title='发车提示' cancelText='暂不发车' confirmText='开始发车' confirmButtonProps={{ loading: form?.loading }} onClose={handleClose} onConfirm={handleConfirm}>
            <View>
                <View className="kb-text__center kb-color__black">
                    发车前，确保电量充足并做好安全检查。
                </View>
                <OperateDepartModalForm value={form?.data?.['from_stop']} onChange={handleChange} />
            </View>
        </KbModal>
    )
}

export default OperateDepartModal;