import { orderCheckRoutingStatus } from '~/services/order/detail';
import { useUpdate } from '~base/hooks/page';
import { useRequest } from '~base/utils/request/hooks';

/**
 *
 * @description 车辆发车检查
 */
export function useCheckOrderTruckDepart(data) {
  const { run, cancel, loading } = useRequest(orderCheckRoutingStatus, {
    manual: true,
    toastLoading: '车辆任务规划中',
    pollingInterval: 3000,
    // 不用等待，表示不需要轮询了
    checkIsSuccess: (isWaiting) => !isWaiting,
  });

  // 开始检查
  function start(req) {
    return run(req);
  }

  // 停止
  function stop() {
    cancel();
  }

  useUpdate(
    ({ logined }) => {
      const { order_id } = data || {};
      if (logined && order_id) {
        orderCheckRoutingStatus({ order_id }).then((isWaiting) => {
          if (isWaiting) {
            start({
              order_id,
            });
          }
        });
      }
    },
    [data],
  );

  return {
    start,
    stop,
  };
}
