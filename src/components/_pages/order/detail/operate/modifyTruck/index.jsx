import { AtButton } from "taro-ui"
import { View } from "@tarojs/components";
import { useEffect } from "react";
import Taro from "@tarojs/taro";
import longListRefresherManager from "~base/components/long-list/refresher";
import { serverIndexOrderRefresherKey } from "~/components/_pages/truck/choose/_utils";
import pick from "lodash/pick";
import './index.scss';

const OrderOperateModifyTruck = (props) => {
    const { data, onSuccess, className } = props;

    const handleClick = () => {
        longListRefresherManager.record(serverIndexOrderRefresherKey, onSuccess);
        Taro.navigator({
            url: 'truck/choose',
            options: {
                ...pick(data, ['order_id', 'goods_volume', 'vehicle_type']),
                type: '1'
            },
        });
    }

    return (
        <AtButton type='primary' circle onClick={handleClick} className={className}>更换车辆</AtButton>
    )
}

export default OrderOperateModifyTruck;