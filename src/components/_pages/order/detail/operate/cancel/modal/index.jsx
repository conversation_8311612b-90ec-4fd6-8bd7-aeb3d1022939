import { AtFloatLayout } from "taro-ui"
import OperateCancelModalForm from "./form";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { OperateCancelModalName } from "../_utils";
import { useEffect } from "react";
import { serviceURL } from "~/services/_utils";
import './index.scss';

const OperateCancelModal = () => {
    const { open, onOpenChange, form } = useActionContext(OperateCancelModalName, {
        form: {
            order_id: {},
            reason: { value: '', tag: '请输入原因' }
        },
        checkBySubmit: true,
        api: {
            url: serviceURL('/Order/cancel'),
            toastLoading: true,
            toastSuccess: true,
            toastError: true
        }
    });

    const handleClose = () => onOpenChange(false);

    return (
        <AtFloatLayout isOpened={open} onClose={handleClose}>
            <OperateCancelModalForm onSuccess={handleClose} />
        </AtFloatLayout>
    )
}

export default OperateCancelModal