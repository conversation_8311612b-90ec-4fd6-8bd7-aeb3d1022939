import { AtButton, AtFloatLayout, AtTextarea } from "taro-ui"
import { View } from "@tarojs/components";
import { useCreateCancelReasonBars } from "./_utils";
import classNames from "classnames";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { OperateCancelModalName } from "../../_utils";
import './index.scss';

const OperateCancelModalForm = (props) => {
    const { onSuccess } = props;
    const { form, onOpenChange } = useActionContext(OperateCancelModalName);
    const { bars, onSwitch, current } = useCreateCancelReasonBars();

    // 提交
    const handelClick = async () => {
        const res = await form?.onFinish?.();
        const isSuccess = `${res?.code}` === '0';
        if (isSuccess) {
            onSuccess?.();
        }
    };

    // 切换原因类型
    const handleSwitch = ({ key, label }) => {
        onSwitch(key);
        form.setFieldsValue({
            reason: key === '0' ? '' : label
        });
    }

    // 输入原因
    const handleChange = (v) => {
        form?.setFieldsValue({ reason: v });
    }

    const handleCancel = () => onOpenChange(false);

    return (
        <View className="cancel-form">
            <View className="cancel-form-title">取消订单原因</View>
            <View className="cancel-form-bars">
                {
                    bars.map(item => (
                        <View
                            key={item.key}
                            className={classNames("cancel-form-bars__item", {
                                "cancel-form-bars__item-active": item.key === current
                            })}
                            onClick={() => handleSwitch(item)}
                        >
                            {item.label}
                        </View>
                    ))
                }
            </View>
            {
                current === '0' && (
                    <View className="cancel-form-textarea">
                        <AtTextarea cursorSpacing={200} placeholder="请输入取消原因，最多100字" maxLength={100} count onChange={handleChange} value={form?.data?.reason} />
                    </View>
                )
            }
            <View className="cancel-form-btns">
                <AtButton circle onClick={handleCancel}>再想想</AtButton>
                <AtButton type='primary' circle onClick={handelClick} loading={form?.loading}>确定</AtButton>
            </View>
        </View>
    )
}

export default OperateCancelModalForm