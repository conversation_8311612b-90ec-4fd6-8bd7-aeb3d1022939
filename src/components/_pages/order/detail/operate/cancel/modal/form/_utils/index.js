import { useState } from 'react';

export function useCreateCancelReasonBars() {
  const [current, setCurrent] = useState('0');

  const bars =
    process.env.MODE_ENV === 'client'
      ? [
          {
            key: '1',
            label: '和车主协商取消',
          },
          {
            key: '2',
            label: '计划有变',
          },
          {
            key: '3',
            label: '车子超时未到达',
          },
          {
            key: '0',
            label: '其他原因',
          },
        ]
      : [
          {
            key: '1',
            label: '和货主协商取消',
          },
          {
            key: '2',
            label: '车子发生交通事故',
          },
          {
            key: '3',
            label: '车子出现故障',
          },
          {
            key: '0',
            label: '其他原因',
          },
        ];

  const onSwitch = (c) => setCurrent(c);

  return {
    bars,
    current,
    onSwitch,
  };
}
