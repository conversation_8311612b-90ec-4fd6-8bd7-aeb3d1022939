.cancel-form {
    padding: $spacing-v-md $spacing-h-md;

    &-title {
        font-weight: bold;
        padding: $spacing-v-md 0;
    }

    &-bars {
        display: flex;
        align-items: center;
        gap: $spacing-v-md;
        padding-bottom: $spacing-v-md;
        flex-wrap: wrap;

        &__item {
            background-color: $color-grey-9;
            border-radius: $border-radius-arc;
            padding: $spacing-v-xs $spacing-h-md;
            color: #646566;
            border: $width-base solid $color-grey-9;

            &-active {
                color: $color-brand;
                border-color: $color-brand;
            }
        }
    }

    &-textarea {
        border-radius: $border-radius-lg;
        overflow: hidden;

        &,
        .at-textarea {
            background-color: $color-grey-9;
        }

        .at-textarea {
            border-color: $color-grey-9;
        }
    }

    &-btns {
        display: flex;
        align-items: center;
        gap: $spacing-v-md;
        padding-top: 2 * $spacing-v-xl;

        .at-button {
            flex-grow: 1;
        }
    }
}