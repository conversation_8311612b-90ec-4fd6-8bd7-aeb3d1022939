import { View } from "@tarojs/components"
import OrderOperateDepart from "./depart";
import OrderOperateOpen from "./open";
import OrderOperateModifyTruck from "./modifyTruck";
import { checkOrderCanDepart, checkOrderCanModifyTruck, checkOrderCanOpenDoor } from "./_utils";
import './index.scss';

const OrderDetailOperate = (props) => {
    const { data, onSuccess } = props;

    const isCanDepart = checkOrderCanDepart(data);
    const isCanOpenDoor = checkOrderCanOpenDoor(data);
    const isCanModifyTruck = checkOrderCanModifyTruck(data);

    const show = isCanDepart || isCanOpenDoor || isCanModifyTruck;

    return (
        show
            ? (
                <View className="order-detail-operate">
                    {isCanOpenDoor && <OrderOperateOpen className='order-detail-open' data={data} onSuccess={onSuccess} />}
                    {isCanDepart && <OrderOperateDepart className='order-detail-depart' data={data} onSuccess={onSuccess} />}
                    {isCanModifyTruck && <OrderOperateModifyTruck className='order-detail-modify-truck' data={data} onSuccess={onSuccess} />}
                </View>
            )
            : null
    )
}

export default OrderDetailOperate;