import { AtButton } from "taro-ui"
import { Text, View } from "@tarojs/components";
import Popconfirm from "~base/components/popconfirm";
import { orderOpenDoor } from "~/services/order/detail";
import './index.scss';

const OrderOperateOpen = (props) => {
    const { data, onSuccess, className } = props;

    const handleConfirm = async () => {
        const res = await orderOpenDoor({
            order_id: data?.order_id,
            auth_code: data?.auth_code
        })
        const isSuccess = `${res?.code}` === '0';
        if (isSuccess) {
            onSuccess?.();
        }
        return isSuccess;
    }

    return (
        <Popconfirm buttonProps={{ type: 'secondary', circle: true, className }} title="温馨提示" content='是否确定解锁车门？' onConfirm={handleConfirm}>
            <View className="kb-icon kb-icon-lock" />
            <Text className="kb-display__inline-block">解锁车门</Text>
        </Popconfirm>
    )
}

export default OrderOperateOpen;