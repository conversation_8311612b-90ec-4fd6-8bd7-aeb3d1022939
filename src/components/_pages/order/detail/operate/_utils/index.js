// op_switch

// enable_cancel:取消,1:可以，2:不可以
// enable_delete:删除，1:可以，2:不可以
// enable_start:发车，1:可以，2:不可以
// 检查是否可取消
export function checkOrderCanCancel(data) {
  return `${data?.op_switch?.enable_cancel}` === '1';
}

// 检查是否可发车
export function checkOrderCanDepart(data) {
  return `${data?.op_switch?.enable_start}` === '1';
}

// 检查是否可开门
export function checkOrderCanOpenDoor(data) {
  return `${data?.op_switch?.enable_open}` === '1';
}

// 检查是否可删除
export function checkOrderCanRemove(data) {
  return `${data?.op_switch?.enable_delete}` === '1';
}

// 订单是否可分享
export function checkOrderCanShare(data) {
  return process.env.MODE_ENV === 'client' && `${data?.op_switch?.enable_share}` === '1';
}

// 检查是否可更换车辆
export function checkOrderCanModifyTruck(data) {
  return `${data?.op_switch?.enable_chg_vehicle}` === '1';
}


// 检查是否可设置停靠点
export function checkOrderCanModifyStop(data) {
  return `${data?.op_switch?.enable_set_stop}` === '1';
}
