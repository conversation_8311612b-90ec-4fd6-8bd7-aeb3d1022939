/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-rental-order-date {
  padding-bottom: $spacing-h-md;
  color: $color-grey-0;
  .kb-line {
    position: relative;
    width: 85%;
    margin-bottom: $spacing-h-lg;
    margin-left: $spacing-h-md;
    padding: $spacing-h-lg $spacing-h-md;
    padding-bottom: 0;
    border-bottom: 2px dashed $color-brand;
    .kb-dot {
      position: absolute;
      bottom: -12px;
      left: -12px;
      width: 24px;
      height: 24px;
      background: rgba(15, 199, 205, 0.1);
      border-radius: 50%;
      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 12px;
        height: 12px;
        background-color: $color-brand;
        border-radius: 50%;
        transform: translate(-50%, -50%);
        content: '';
      }
      &__end {
        right: -12px;
        left: unset;
      }
    }
  }
  .time_interval {
    margin-bottom: $spacing-h-md;
    padding: 0 $spacing-h-sm;
    color: $color-brand;
    font-size: 22px;
    background: rgba(15, 199, 205, 0.1);
    border-radius: $border-radius-md;
  }
}
