/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useMemo } from 'react';
import TimeLineExtend from '~base/components/time-line/extend';
import './index.scss';

const RentalOrderDetailLine = ({ data }) => {
  const { order_operate = [] } = data;

  const items = useMemo(() => {
    return order_operate.map((item, index) => {
      return {
        key: index,
        content: (
          <View>
            <View className='kb-size__base'>{item.info}</View>
            <View className='kb-color__grey kb-size__sm kb-spacing-sm-t'>{item.time}</View>
          </View>
        ),
        isActive: index == 0,
        isCurrent: index == 0,
      };
    });
  }, [order_operate]);

  if (!order_operate.length) return null;

  return (
    <View className='kb-box kb-spacing-md-lr kb-rental-order-line'>
      <View className='kb-size__lg kb-border-b kb-spacing-md-tb'>订单记录</View>
      <View>
        <TimeLineExtend items={items} />
      </View>
    </View>
  );
};

RentalOrderDetailLine.options = {
  addGlobalClass: true,
};

export default RentalOrderDetailLine;
