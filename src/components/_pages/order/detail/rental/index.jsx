/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import RentalTruckCard from '~/components/_pages/truck/choose/rental/list/card';
import { rentOrderDetailApi } from '~/services/rental';
import LongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import PageLayout from '~base/components/page/layout';
import RentalOrderCreateExtra from '../../create/rental/extra';
import RentalOrderDetailCertificate from './certificate';
import RentalOrderDetailDate from './date';
import './index.scss';
import RentalOrderDetailLine from './line';

const RentalOrderDetail = ({ order_id, top }) => {
  const { config, data } = useLongList(rentOrderDetailApi, {
    api: {
      data: {
        order_id,
      },
    },
    isNonList: true,
  });

  return (
    <PageLayout>
      <View className='order-detail-rental' style={{ paddingTop: top }}>
        <LongList data={config}>
          {data && (
            <View className='kb-spacing-md'>
              <View className='kb-size__xl kb-color__black kb-spacing-md-b'>{data?.status_cn}</View>

              <RentalTruckCard data={data} source='orderDetail' />
              <RentalOrderDetailDate data={data} />
              <RentalOrderCreateExtra data={data} source='orderDetail' />
              <RentalOrderDetailCertificate data={data} />
              <RentalOrderDetailLine data={data} />
            </View>
          )}
        </LongList>
      </View>
    </PageLayout>
  );
};

RentalOrderDetail.options = {
  addGlobalClass: true,
};

export default RentalOrderDetail;
