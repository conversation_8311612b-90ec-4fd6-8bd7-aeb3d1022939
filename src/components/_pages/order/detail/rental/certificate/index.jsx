/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, View } from '@tarojs/components';
import { useMemo } from 'react';
import './index.scss';

const RECEIPT_LIST = [
  {
    label: '发车凭证',
    key: '1',
  },
  {
    label: '收车凭证',
    key: '2',
  },
  {
    label: '还车凭证',
    key: '3',
  },
  {
    label: '定损凭证',
    key: '4',
  },
];

const RentalOrderDetailCertificate = ({ data }) => {
  const { order_receipt = [] } = data;

  const receiptMap = useMemo(() => {
    const map = {};
    (order_receipt || []).forEach((receipt) => {
      const typeKey = String(receipt?.receipt_type ?? '');
      if (typeKey) {
        map[typeKey] = receipt;
      }
    });
    return map;
  }, [order_receipt]);

  const hasCertificate = useMemo(() => {
    return RECEIPT_LIST.some((item) => receiptMap[item.key]?.images?.length);
  }, [receiptMap]);

  if (!hasCertificate) return null;

  return (
    <View className='kb-box kb-spacing-md-lr kb-rental-order-certificate'>
      <View className='kb-size__lg kb-border-b kb-spacing-md-tb'>车辆凭证</View>
      <View className='kb-spacing-md-tb'>
        {RECEIPT_LIST.map((item) => {
          const receipt = receiptMap[item.key];
          const images = receipt?.images || [];
          if (!images.length) return null;
          return (
            <View key={item.key}>
              <View className='kb-size__base kb-color__greyer kb-spacing-sm-b'>{item.label}</View>
              <View className='img-warp at-row at-row__align--center'>
                {images.map((url) => (
                  <Image key={url} src={url} className='img-item' />
                ))}
              </View>
            </View>
          );
        })}
      </View>
    </View>
  );
};

RentalOrderDetailCertificate.options = {
  addGlobalClass: true,
};

export default RentalOrderDetailCertificate;
