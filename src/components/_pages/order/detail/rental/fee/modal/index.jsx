import Taro from '@tarojs/taro';
import { useImperativeHandle, useRef, useState } from 'react';
import OrderContentFeeModalContent from './content';

const OrderContentFeeModal = (props) => {
  const { actionRef, onConfirm } = props;
  const [value, setValue] = useState('');
  const ref = useRef({ value: '' });

  const handleChange = (v) => {
    ref.current.value = v;
  };

  useImperativeHandle(
    actionRef,
    () => ({
      open: (data, item) => {
        const key = item.key;
        if (data[key]) {
          ref.current.value = Number(data[key]);
        }
        Taro.kbModal({
          title: item.label,
          cancelText: '取消',
          onClose: () => {
            ref.current.value = '';
          },
          content: (
            <OrderContentFeeModalContent
              data={data}
              defaultValue={ref.current.value}
              onChange={handleChange}
            />
          ),
          onConfirm: () => {
            onConfirm?.({ [key]: ref.current.value });
          },
        });
      },
    }),
    [value],
  );

  return <></>;
};

export default OrderContentFeeModal;
