import { View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtInput } from 'taro-ui';

const OrderContentAuthCodeModalContent = (props) => {
  const { defaultValue, onChange } = props;
  const [value, setValue] = useState('');

  const handleChange = (v) => {
    setValue(v);
    onChange?.(v);
  };

  useEffect(() => {
    handleChange(defaultValue);
  }, [defaultValue]);

  return (
    <View>
      <AtInput
        className='kb-input__rect kb-input__rect-grey'
        type='number'
        value={value}
        onChange={handleChange}
        placeholder='请输入金额'
        cursor={-1}
      />
    </View>
  );
};

export default OrderContentAuthCodeModalContent;
