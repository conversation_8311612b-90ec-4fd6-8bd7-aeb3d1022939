/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import TruckImg from '@/components/_pages/order/list/card/rental/bars/components/truck-img';
import { Text, View } from '@tarojs/components';
import { AtIcon, AtTextarea } from 'taro-ui';
import './index.scss';
import OrderContentFeeModal from './modal';
import { useCreateFee } from './utils';

const RentalOrderDetailLine = (props) => {
  const { feeList, value, total, feeModalRef, onChange, handleCustom, handleShowTips } =
    useCreateFee(props);

  return (
    <View className='kb-box kb-spacing-md-lr kb-rental-order-fee'>
      <View className='kb-size__lg kb-border-b kb-spacing-md-tb'>其他费用</View>
      <View>
        {feeList.map((item) => {
          return (
            <View
              key={item.key}
              className='at-row at-row__align--center at-row__justify--between kb-spacing-md-t'
            >
              <View
                className='kb-color__greyer'
                hoverClass={item.tips ? 'kb-hover' : ''}
                onClick={() => handleShowTips(item)}
              >
                {item.label}
                {item.tips && (
                  <AtIcon
                    value='question'
                    prefixClass='kb-icon'
                    className='kb-color__greyer kb-size__sm kb-spacing-sm-l kb-settle-tips'
                  />
                )}
              </View>
              <View className='kb-flex-1 kb-widthUnset at-row at-row__align--center at-row__justify--end'>
                {item.value && <Text>{item.value}</Text>}
                {item.extra && (
                  <View
                    className='kb-color__brand kb-spacing-sm-l'
                    hoverClass='kb-hover'
                    onClick={() => handleCustom(item)}
                  >
                    {item.extra}
                  </View>
                )}
              </View>
            </View>
          );
        })}
        <View className='kb-spacing-md-tb'>
          <View className='kb-color__greyer kb-spacing-md-b'>费用备注</View>
          <AtTextarea
            value={value.loss_remark}
            onChange={(value) => onChange({ loss_remark: value })}
            placeholder='请输入备注内容'
            className='kb-settle-textarea'
            maxLength={100}
          />
        </View>
        <View>
          <View className='kb-color__greyer'>车辆定损图片</View>
          <TruckImg
            className='kb-settle-truck-img'
            value={value?.loss_certificate_list || []}
            onChange={(value) => onChange({ loss_certificate_list: value })}
            source='settle'
          />
        </View>
        <View className='kb-spacing-md-tb kb-border-t at-row at-row__align--center at-row__justify--between kb-size__lg'>
          <View>合计应退</View>
          <View>{total}</View>
        </View>
      </View>
      <OrderContentFeeModal actionRef={feeModalRef} onConfirm={onChange} />
    </View>
  );
};

RentalOrderDetailLine.options = {
  addGlobalClass: true,
};

export default RentalOrderDetailLine;
