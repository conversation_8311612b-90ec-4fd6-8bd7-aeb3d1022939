/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import numeral from 'numeral';
import { useEffect, useMemo, useRef, useState } from 'react';

export const useCreateFee = ({ data, onChange: propsOnChange }) => {
  const [value, setValue] = useState({});
  const feeModalRef = useRef(null);

  const onChange = (value) => {
    setValue((prev) => ({
      ...prev,
      ...value,
    }));
  };

  const handleCustom = (item) => {
    feeModalRef.current?.open?.(value, item);
  };

  const handleShowTips = (item) => {
    if (item.tips) {
      Taro.kbModal({
        title: item.label,
        content: <View className='kb-text__center'>{item.tips}</View>,
        confirmText: '我已知晓',
      });
    }
  };

  useEffect(() => {
    if (data?.pre_settle) {
      setValue(data.pre_settle || {});
    }
  }, [data]);

  useEffect(() => {
    propsOnChange?.(value);
  }, [value]);

  const feeList = useMemo(() => {
    const isOverdue = value.remain_rent?.indexOf('-') > 0;
    return [
      isOverdue
        ? {
            label: '超期费用',
            key: 'timeout_rent',
            value: value.timeout_rent ? `￥${value.timeout_rent}` : '',
            // extra: '自定义',
            tips: '超期费用由平台按照规则自动计算，您也可以自定义超期费用，但是不能高于平台的金额',
          }
        : {
            label: '剩余租赁费用',
            key: 'remain_rent',
            value: value.remain_rent ? `￥${value.remain_rent}` : '',
          },
      {
        label: '违约金',
        key: 'break_rent',
        value: value.break_rent ? `￥${value.break_rent}` : '',
        tips: '违约金由平台按照规则自动计算，您也可以自定义违约金金额，但是不嫩高于平台的金额',
        extra: '自定义',
      },
      {
        label: '车辆定损费用',
        key: 'loss_amount',
        value: value.loss_amount ? `￥${value.loss_amount}` : '',
        extra: '修改',
      },
    ].filter((item) => !item.hide);
  }, [value]);

  const total = useMemo(() => {
    return numeral(value.remain_rent)
      .subtract(value.break_rent || 0)
      .subtract(value.loss_amount || 0)
      .subtract(value.timeout_rent || 0)
      .format('0.00');
  }, [value]);

  return {
    feeList,
    value,
    total,
    feeModalRef,
    onChange,
    handleCustom,
    handleShowTips,
  };
};
