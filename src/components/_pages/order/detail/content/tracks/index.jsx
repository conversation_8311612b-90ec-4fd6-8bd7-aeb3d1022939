import { View } from "@tarojs/components"
import classNames from "classnames";
import { dateCalendar, makePhoneCall } from "~base/utils/utils";
import TimeLineExtend from "~base/components/time-line/extend";
import { useMemo } from "react";
import { useFormatTracks } from "./_utils";
import { checkOrderCanShowTrackTimeLine, checkOrderCanShowTracks, checkOrderCanShowTracksStatus } from "../../../list/status/_utils";
import './index.scss';

const OrderContentTracks = (props) => {
    const { data, className } = props;
    const { formatted, hasCall, onClickCall } = useFormatTracks(data);
    const rootCLs = classNames('order-content-tracks kb-box', className);

    // 显示行程轨迹
    const isShowTrack = useMemo(() => checkOrderCanShowTracks(data), [data]);

    // 显示轨迹状态
    const isShowTrackStatus = useMemo(() => checkOrderCanShowTracksStatus(data), [data]);

    // 是否展示形成轨迹路线
    const isShowTrackTimeLine = useMemo(() => checkOrderCanShowTrackTimeLine(data), [data]);

    return (
        isShowTrack
            ? (
                <View className={rootCLs}>
                    <View className="tracks-title">行程轨迹</View>
                    {
                        isShowTrackStatus
                            ? (
                                <View className="tracks-status">
                                    <View className="kb-icon kb-icon-car"></View>
                                    <View className="tracks-status__info">
                                        <View>等待{formatted.statusLabel}发车</View>
                                        {
                                            formatted.hasCall && (
                                                <View className="kb-icon kb-icon-tel" hoverClass="kb-hover-opacity" onClick={onClickCall}></View>
                                            )
                                        }
                                    </View>
                                </View>
                            )
                            : null
                    }
                    {
                        isShowTrackTimeLine && (
                            <TimeLineExtend items={formatted.tracks} />
                        )
                    }
                </View>
            )
            : null
    )
}

export default OrderContentTracks;