import { useMemo } from 'react';
import { checkIsStartPoint } from '~/components/_pages/index/server/order/components/_utils/line';
import { dateCalendar, makePhoneCall } from '~base/utils/utils';

function getStatusLabel(data) {
  const { contact_name, contact_phone, stop_type } = data || {};
  if (!(contact_name || contact_phone) || checkIsStartPoint(stop_type)) {
    return '车主';
  }
  return [contact_name, contact_phone].filter((item) => !!item).join('/');
}

export function useFormatTracks(data) {
  const { tracks, current_stop } = data || {};
  const { vehicle_stop_id: current_vehicle_stop_id, contact_phone } = current_stop || {};

  // 拨打
  const onClickCall = () => makePhoneCall(contact_phone);

  const formatted = useMemo(() => {
    const currentIndex = tracks?.findIndex((item) => !!item.time); // 第一个有时间
    return {
      statusLabel: getStatusLabel(current_stop),
      tracks: tracks?.map((item, index) => ({
        key: item.vehicle_stop_id,
        label: dateCalendar(item.time, { timer: true }),
        content: `${item.event_desc}` + (item.stop_type_name ? `（${item.stop_type_name}）` : ''),
        isCurrent: currentIndex === index,
        isActive: !!item.time,
      })),
      hasCall: !!contact_phone,
    };
  }, [data]);

  return {
    formatted,
    onClickCall,
  };
}
