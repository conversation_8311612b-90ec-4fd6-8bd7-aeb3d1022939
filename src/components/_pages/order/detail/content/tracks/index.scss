.order-content-tracks {
    padding: $spacing-v-md $spacing-h-md;

    .time-line__item-content {
        padding-left: $spacing-h-xs;
    }

    .tracks {
        &-title {
            border-bottom: $border-lightest;
            padding: $spacing-v-md 0;
        }

        &-status {
            text-align: center;
            padding: $spacing-v-xxl 0;

            .kb-icon {

                &-car {
                    margin-bottom: $spacing-v-md;
                    font-size: $icon-font-size-lg;
                    width: 90px;
                    height: 90px;
                    line-height: 90px;
                    text-align: center;
                    background-color: #F7F8FA;
                    border-radius: $border-radius-circle;
                }
            }

            &__info {
                position: relative;

                .kb-icon-tel {
                    position: absolute;
                    right: 0;
                    top: 50%;
                    transform: translateY(-50%);
                    font-size: $icon-font-size-lg;
                    color: $color-brand;
                }
            }
        }
    }
}