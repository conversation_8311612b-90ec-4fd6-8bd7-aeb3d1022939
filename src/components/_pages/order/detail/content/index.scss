.order-detail-content {
    height: 100%;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    background: #F7F8FA;
    transition: border-radius 0.3s ease;

    &__bar {
        border-radius: $border-radius-xxl $border-radius-xxl 0 0;
        padding-top: $spacing-v-lg;

        &::before {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 90px;
            height: 10px;
            background-color: #C8C9CC;
            border-radius: $border-radius-sm;
            top: $spacing-v-sm;
        }
    }

    &__max {
        border-radius: 0;
    }

    &__desc {
        padding: 0 $spacing-h-md;
        padding-bottom: $spacing-v-md;

        .truck-card {
            padding: $spacing-v-md $spacing-h-md;
        }

        .desc-row {
            margin-bottom: $spacing-v-md;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

}