import { Text, View, Button } from '@tarojs/components';
import OrderStatus from '../../../list/status';
import OrderBars from '../../../list/bars';
import { getCancelTypeLabel } from '../../_utils';
import { checkOrderCanShare } from '../../operate/_utils';
import { checkOrderIsWaiting, checkOrderRoteIsNotActivated } from '../../../list/status/_utils';
import { useEffect, useMemo } from 'react';
import { switchShareMenu } from '~base/utils/share';
import './index.scss';

const OrderContentStatus = (props) => {
  const { data, onOperate, onSuccess } = props;
  const cancelTypeLabel = getCancelTypeLabel(data?.cancel_type);
  const canShare = checkOrderCanShare(data);

  useEffect(() => {
    switchShareMenu(canShare);
  }, [canShare]);

  const isWaiting = useMemo(() => checkOrderIsWaiting(data), [data]);

  return (
    <View className='order-content-status'>
      <View className='at-row at-row__align--center at-row__justify--between'>
        <View className='at-col'>
          {
            !isWaiting && <OrderStatus data={data} strong long />
          }
        </View>
        <OrderBars type='detail' data={data} onOperate={onOperate} onSuccess={onSuccess} />
        {
          canShare && (
            <Button className='order-content-status__share' openType='share' data-info={data} data-page='order.detail'>
              <View className='kb-icon kb-icon-share-circle' />
            </Button>
          )
        }
      </View>
      {
        checkOrderRoteIsNotActivated(data)
          ? (
            <View className='kb-spacing-md-t'>
              <Text className='kb-color__orange'>
                备注：订单路线尚未开通，已联系厂商开通，若无法开通，平台自动取消订单。
              </Text>
            </View>
          )
          : data?.cancel_reason
            ? (
              <View className='kb-spacing-md-t'>
                <Text className='kb-color__grey'>{cancelTypeLabel}取消订单：</Text><Text className='kb-color__orange'>{data?.cancel_reason}</Text>
              </View>
            )
            : null
      }
    </View>
  );
};

export default OrderContentStatus;
