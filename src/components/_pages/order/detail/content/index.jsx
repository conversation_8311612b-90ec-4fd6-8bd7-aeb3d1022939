import { View } from "@tarojs/components";
import LongList from "~base/components/long-list";
import { useLongList } from "~base/components/long-list/hooks";
import { formatResponseDefaultNonList } from "~base/components/long-list/create";
import classNames from "classnames";
import { useImperativeHandle, useMemo, useRef, useState } from "react";
import OrderContentStatus from "./status";
import TruckCard from "~/components/_pages/truck/list/item/card";
import OrderContentInfo from "./info";
import OrderContentTracks from "./tracks";
import { serviceURL } from "~/services/_utils";
import OrderContentSearch from "./search";
import OrderContentAuthCode from "./authCode";
import OrderContentAuthCodeModal from "./authCode/modal";
import { AtButton } from "taro-ui";
import { checkOrderIsCancel } from "../../list/status/_utils";
import './index.scss';

const OrderDetailContent = (props) => {
    const { isMoveMax, order_id, actionRef, showMap, enableRefresh: enableRefreshProps } = props;
    const [showAuthCodeModalBtn, setShowAuthCodeModalBtn] = useState(false);
    const authCodeModalRef = useRef();
    const ref = useRef({ simpleData: null });

    const { config, data } = useLongList(serviceURL('/Order/detail'), {
        api: {
            data: { order_id },
            formatResponse: (res, req) => {
                const { code, data: resData, msg } = res || {};
                const showBtn = `${code}` === '70000';
                setShowAuthCodeModalBtn(showBtn);
                if (showBtn) {
                    ref.current.simpleData = { ...resData, errMsg: req.auth_code ? (resData?.err_msg || msg) : '' };
                    handleClickShowModal();
                    ref.current.simpleData.errMsg = ''
                }
                return formatResponseDefaultNonList(res)
            }
        },
        isNonList: true
    });

    // 数据加载
    const handleLoad = ([data]) => props?.onLoad(data);

    // 点击输入验证码
    const handleClickShowModal = () => authCodeModalRef.current?.open?.(ref.current.simpleData);

    const rootCls = classNames('order-detail-content', {
        'order-detail-content__bar': showMap,
        'order-detail-content__max': isMoveMax
    });

    useImperativeHandle(actionRef, () => ({
        loader: () => config.loader()
    }));

    // 非已取消的订单，都允许刷新
    const enableRefresh = useMemo(() => !checkOrderIsCancel(data) && enableRefreshProps, [data, enableRefreshProps]);

    return (
        <View className={rootCls}>
            <LongList
                data={config}
                onLoad={handleLoad}
                enableRefresh={enableRefresh}
                noScrollview={showMap && !isMoveMax}
                renderEmptyFooter={
                    showAuthCodeModalBtn
                        ? (
                            <View className='footer-inner'>
                                <AtButton size="small" type='primary' circle onClick={handleClickShowModal}>输入授权码</AtButton>
                            </View>
                        )
                        : null
                }
            >
                <View className="order-detail-content__desc">
                    {
                        data && (
                            <OrderContentSearch data={data} onUpdate={config.loader}>
                                <OrderContentStatus data={data} onSuccess={config.triggerRefresher} onOperate={config.recordRefresher} />
                            </OrderContentSearch>
                        )
                    }
                    <OrderContentAuthCode data={data} className='desc-row' />
                    <OrderContentTracks data={data} className='desc-row' />
                    <TruckCard showPhone isOrderDetail data={data?.vehicle} className='desc-row kb-box' />
                    <OrderContentInfo data={data} className='desc-row' onSuccess={config.triggerRefresher} />
                </View>
            </LongList>
            <OrderContentAuthCodeModal actionRef={authCodeModalRef} onRefresh={config.loader} />
        </View>
    )
}

export default OrderDetailContent;