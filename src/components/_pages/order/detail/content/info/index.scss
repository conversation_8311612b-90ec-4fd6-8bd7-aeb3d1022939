.order-content-info {
    padding: 0 $spacing-h-md;

    .info {
        &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: $spacing-v-md 0;

            &__content {
                display: flex;
                align-items: center;

                .order-pay-status {
                    padding-left: $spacing-h-md;
                    margin-left: $spacing-h-md;
                    position: relative;

                    &::before {
                        content: '';
                        height: 70%;
                        top: 50%;
                        transform: translateY(-50%);
                        border-left: $border-grey;
                        position: absolute;
                        left: 0;
                    }
                }
            }
        }

        &-line {
            .kb-order-routes-timeline {
                background-color: transparent;
                padding:$spacing-v-md 0;
            }
        }

        &-detail {
            &__row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: $spacing-v-md 0;

                &:first-child {
                    border-bottom: $border-lightest;
                }
            }

            &__label {
                color: $color-grey-2;
            }

            &__value {
                text-align: right;
            }

            &__more {
                padding: $spacing-v-md 0;
                text-align: center;
                font-size: $font-size-base;
                color: #646566;

                .kb-icon {
                    transform: rotate(90deg);
                    font-size: $icon-font-size-sm;
                }
            }
        }
    }
}