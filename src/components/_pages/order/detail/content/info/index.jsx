import { Text, View } from "@tarojs/components"
import classNames from "classnames";
import Line from "~/components/_pages/index/server/order/components/line";
import OrderPayStatus from "../../../list/status/pay";
import { useInfoDetailItems } from "./_utils";
import { AtIcon } from "taro-ui";
import './index.scss';

const OrderContentInfo = (props) => {
    const { data, className, onSuccess } = props;
    const rootCLs = classNames('order-content-info kb-box', className);
    const { items, collapsed, onClickMore } = useInfoDetailItems(data);
    const hasItems = items.length > 0;

    return (
        hasItems
            ? (
                <View className={rootCLs}>
                    <View className="info-header">
                        <View className="info-header__content">
                            <View>订单信息</View>
                            <OrderPayStatus data={data} />
                        </View>
                        {
                            data?.amount > 0 && (
                                <View className="info-header__extra">
                                    ￥{data?.amount}
                                </View>
                            )
                        }
                    </View>
                    <View className="info-line">
                        <Line data={data} hideTitle size='normal' onSuccess={onSuccess} />
                    </View>
                    <View className="info-detail">
                        {
                            items.map(item => (
                                <View key={item.key} className="info-detail__row">
                                    <View className="info-detail__label">
                                        {item.label}
                                    </View>
                                    <View className="info-detail__value">{item.value || '--'}</View>
                                </View>
                            ))
                        }
                        {
                            collapsed && (
                                <View onClick={onClickMore} hoverClass="kb-hover-opacity" className="info-detail__more"><Text className="kb-icon__text--mr">展开更多</Text><Text className="kb-icon kb-icon-arrow" /></View>
                            )
                        }
                    </View>
                </View>
            )
            : null
    )
}

export default OrderContentInfo;
