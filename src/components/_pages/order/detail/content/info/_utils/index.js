import { useEffect, useMemo, useState } from 'react';
import TruckCardType from '~/components/_pages/truck/list/item/card/type';
import { dateCalendar } from '~base/utils/utils';

function getPayTypeLabel(type) {
  if (!type) return;
  const payTypeLabels = {
    wechat: '微信支付',
    alipay: '支付宝支付',
  };
  return payTypeLabels[type] || type;
}

const items = [
  {
    key: 'vehicle_time',
    label: '用车时间',
    render: (value) => dateCalendar(value, { timer: true }),
  },
  {
    key: 'order_id',
    label: '订单号',
  },
  {
    key: 'create_at',
    label: '订单时间',
    render: (value) => dateCalendar(value, { timer: true }),
  },
  {
    key: 'vehicle_type',
    label: '订单车型',
    render: (value, data) => (
      <TruckCardType data={{ type: data?.vehicle_type, truckload: data?.goods_volume }} />
    ),
  },
  {
    key: 'note',
    label: '订单备注',
  },
  {
    key: 'sender_phone',
    label: '联系电话',
  },
  {
    key: 'amount',
    label: '订单金额',
    render: (value) => value && value >=0 ? `${value}元` : '',
  },
  {
    key: 'pay_type',
    label: '支付方式',
    render: getPayTypeLabel,
  },
];

export function useInfoDetailItems(data) {
  const max = 3;
  const [collapsed, setCollapsed] = useState(false);

  const itemsWithValue = useMemo(
    () =>
      items.map(({ key, label, render }) => {
        const value = data?.[key];
        return {
          key,
          label,
          value: render ? render(value, data) : value,
        };
      }).filter(item=>!!item.value),
    [data],
  );

  const itemsPatched = useMemo(
    () => (collapsed ? itemsWithValue.slice(0, max) : itemsWithValue),
    [itemsWithValue, collapsed],
  );

  useEffect(() => {
    setCollapsed(itemsWithValue.length > max);
  }, [itemsWithValue]);

  // 展开或收起
  const onClickMore = () => setCollapsed((pre) => !pre);

  return { items: itemsPatched, collapsed, onClickMore };
}
