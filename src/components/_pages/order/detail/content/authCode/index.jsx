import { View } from '@tarojs/components';
import classNames from 'classnames';
import './index.scss';

const OrderContentAuthCode = (props) => {
    const { data, className } = props;
    const { auth_code } = data || {};

    const rootCls = classNames('order-content-auth-code', className);

    return (
        auth_code
            ? (
                <View className={rootCls}>
                    <View className='kb-box at-row at-row__align--center at-row__justify--between kb-spacing-md'>
                        <View>授权码</View>
                        <View className='kb-color__brand'>{auth_code}</View>
                    </View>
                    <View className='kb-spacing-md-lr kb-size__base kb-color__grey'>
                        分享订单后，用户凭此取货码，可解锁车门和查看订单，订单完成后失效。
                    </View>
                </View>
            )
            : null
    );
};

export default OrderContentAuthCode;
