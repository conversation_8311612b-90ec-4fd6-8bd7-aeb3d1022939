import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useEffect, useImperativeHandle, useRef, useState } from "react";
import { AtInput } from "taro-ui";
import OrderContentAuthCodeModalContent from "./content";
import { stubFalse } from "lodash";


const OrderContentAuthCodeModal = (props) => {
    const { actionRef, onRefresh } = props;
    const [value, setValue] = useState('');
    const ref = useRef({ value: '' });

    const handleChange = v => {
        ref.current.value = v;
    }

    useImperativeHandle(actionRef, () => ({
        open: (data) => {
            const { errMsg } = data || {};
            if (errMsg) {
                Taro.kbToast({ text: errMsg });
            }
            Taro.kbModal({
                title: '查看订单',
                cancelText: '取消',
                onClose: () => {
                    ref.current.value = '';
                },
                content: (
                    <OrderContentAuthCodeModalContent data={data} defaultValue={ref.current.value} onChange={handleChange} />
                ),
                onConfirm: () => {
                    const { value: auth_code } = ref.current;
                    if (!auth_code || auth_code.length !== 6) {
                        Taro.kbToast({
                            text: '请输入6位授权码'
                        });
                        return false;
                    }
                    onRefresh?.({ auth_code })
                }
            });
        }
    }), [value]);

    return (
        <></>
    )
}

export default OrderContentAuthCodeModal;