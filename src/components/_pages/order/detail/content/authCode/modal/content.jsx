import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { useEffect, useState } from "react";
import { AtInput } from "taro-ui";

const OrderContentAuthCodeModalContent = (props) => {
    const { defaultValue, onChange, data: orderDetail } = props;
    const [value, setValue] = useState('');

    const handleChange = v => { setValue(v); onChange?.(v) }

    useEffect(() => {
        handleChange(defaultValue);
    }, [defaultValue]);

    return (
        <View>
            <View className="kb-color__black">
                授权码已短信下发给【{orderDetail?.stop_name}】联系人{orderDetail?.contact_name}（{orderDetail?.contact_phone}），输入授权码可查看订单信息。
            </View>
            <View className="kb-spacing-md-t">
                <AtInput className="kb-input__circle" type='number' maxLength={6} value={value} onChange={handleChange} placeholder="输入授权码" cursorSpacing={100} />
            </View>
        </View>
    )
}

export default OrderContentAuthCodeModalContent;