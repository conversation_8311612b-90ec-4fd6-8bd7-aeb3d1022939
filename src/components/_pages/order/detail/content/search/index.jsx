import { Image, Text, View } from '@tarojs/components';
import { useEffect, useMemo, useState } from 'react';
import { getOrderWaitTakeStatus } from '~/services/order/detail';
import DateTimer from '~base/components/dateTimer';
import LoaderPure from '~base/components/loader/pure';
import { useRequest } from '~base/utils/request/hooks';
import { checkOrderIsWaiting } from '../../../list/status/_utils';
import './index.scss';

const OrderContentSearch = (props) => {
  const { data: orderDetail, onUpdate } = props;
  const [startTimer, setStartTimer] = useState(0);

  const { data: resData, run } = useRequest(getOrderWaitTakeStatus, {
    pollingInterval: 3000,
    manual: true,
    checkIsSuccess: (isWaiting) => {
      if (!isWaiting) {
        // 已接单，刷新订单详情；
        onUpdate?.();
      }
      return !isWaiting;
    },
  });

  // 从订单详情检查是否为待接单
  const orderIsWaiting = useMemo(() => checkOrderIsWaiting(orderDetail), [orderDetail]);

  useEffect(() => {
    const { time } = resData?.data || {};
    if (orderIsWaiting && time >= 0) {
      setStartTimer((pre) => {
        return pre === 0 ? 1 * time : pre;
      });
    }
  }, [resData, orderIsWaiting]);

  useEffect(() => {
    // 已接单
    if (orderIsWaiting) {
      run({ order_id: orderDetail?.order_id });
    }
  }, [orderIsWaiting]);

  return (
    <>
      {props.children}
      {orderIsWaiting && (
        <View className='kb-spacing-md-b'>
          <View className='order-content-search kb-box'>
            <View className='order-content-search__avatar'>
              <Image
                className='order-content-search__avatar--img'
                mode='widthFix'
                src='https://cdn-img.kuaidihelp.com/truck/truck_1.png'
              />
            </View>
            <View className='order-content-search__tips'>
              <Text className='kb-display__inline-block kb-spacing-xs-r'>等待车主接单...</Text>
              <LoaderPure size='small' />
            </View>
            {startTimer > 0 && (
              <View className='order-content-search__time'>
                <DateTimer start={startTimer} />
              </View>
            )}
          </View>
        </View>
      )}
    </>
  );
};

export default OrderContentSearch;
