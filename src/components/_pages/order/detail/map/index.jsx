import { View } from "@tarojs/components";
import { useEffect, useMemo } from "react";
import LineMap from "~/components/_pages/line/map";
import { getOrderVehiclePath } from "~/services/order/detail";
import { useRequest } from "~base/utils/request/hooks";
import { formatOrderRoutes, formatOrderVehicleCurrentPath } from "./_utils";
import { checkOrderCanShowTracks } from "../../list/status/_utils";

const OrderDetailMap = (props) => {
    const { className, data: orderData, onReady } = props;
    const { routes, vehicle, order_id } = orderData || {};
    const { data, run } = useRequest(getOrderVehiclePath, { manual: true });

    const showMap = useMemo(() => data?.length > 0 && routes?.length > 0, [data, routes]);

    useEffect(() => {
        if (showMap) {
            onReady?.()
        }
    }, [showMap]);

    const points = useMemo(() => {
        if (!showMap) return [];
        return [formatOrderRoutes(routes, data), formatOrderVehicleCurrentPath(vehicle)];
    }, [showMap]);

    useEffect(() => {
        if (order_id && checkOrderCanShowTracks(orderData)) {
            run({ order_id });
        }
    }, [order_id]);

    return (
        showMap
            ? (
                <View className={className}>
                    <LineMap points={points} />
                </View>
            )
            : null
    )
}

export default OrderDetailMap;