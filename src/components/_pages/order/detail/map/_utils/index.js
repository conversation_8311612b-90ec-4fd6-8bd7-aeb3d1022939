import isArray from 'lodash/isArray';

function formatOrderVehiclePath(paths) {
  if (!isArray(paths)) {
    return [];
  }
  return paths.map((item) => {
    const {
      lon,
      lng = lon,
      lat,
      points,
    } = {
      ...item.points?.[0],
      ...item,
    };
    return {
      latitude: 1 * lat,
      longitude: 1 * lng,
      points: formatOrderVehiclePath(points),
    };
  });
}

// 车辆当前位置：后续可扩展支持当前导航
export function formatOrderVehicleCurrentPath(vehicle) {
  const { lon, lng = lon, lat } = vehicle || {};
  const item = {
    latitude: 1 * lat,
    longitude: 1 * lng,
  };
  return [
    {
      ...item,
      points: [item],
    },
  ];
}

// 格式化站点路径
export function formatOrderRoutes(routes, paths) {
  if (!isArray(routes)) {
    return [];
  }
  const pathsFormatted = formatOrderVehiclePath(paths);
  return routes.map((item, index) => {
    const { lon, lng = lon, lat, stop_sort, landmark } = item;

    return {
      points: [],
      ...pathsFormatted[index], // 补充路线数据
      data: item,
      latitude: 1 * lat,
      longitude: 1 * lng,
      content: `${stop_sort}. ${landmark}`,
    };
  });
}
