/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$move-view-min-height: 400px;

.order-detail {
  height: 100%;
  position: relative;
  overflow-y: hidden;
  box-sizing: border-box;

  &__map {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }

  &__move {
    &-area {
      height: calc(200% - $move-view-min-height);
      width: 100%;
      pointer-events: none;
    }

    &-view {
      width: 100%;
      height: calc(50% + $move-view-min-height/2);
      pointer-events: auto;
      overflow: hidden;
    }
  }
}