/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import KbTimeline from '~base/components/time-line';
import { dateCalendar } from '~base/utils/utils';
import './index.scss';

const Record = (props) => {
  const { data = [], className } = props;

  const items = useMemo(() => {
    if (!data.length || !Array.isArray(data)) return [];
    return data.map((item) => ({
      left: [dayjs(item.operate_time).format('HH:mm:ss'), dateCalendar(item.operate_time)],
      titles: [item.operate_name],
      content: [item.operate_user_name ? '操作员：' + item.operate_user_name : ''],
      color: 'black',
    }));
  }, [data]);

  if (!items.length) return null;

  return (
    <>
      <View className={classNames('kb-task__detail-timeline', className)}>
        <View className='kb-size__lg kb-border-b kb-spacing-lg-b'>任务记录</View>
        <View className='kb-task__detail-timeline-list'>
          <KbTimeline items={items} dotClassName='kb-task__detail-timeline-dot' />
        </View>
      </View>
    </>
  );
};

Record.options = {
  addGlobalClass: true,
};

export default Record;
