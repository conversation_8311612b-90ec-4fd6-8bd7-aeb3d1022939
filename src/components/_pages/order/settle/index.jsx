/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import { AtButton } from 'taro-ui';
import { rentOrderDetailApi, rentOrderSettle } from '~/services/rental';
import LongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import PageLayout from '~base/components/page/layout';
import { sleep } from '~base/utils/utils';
import RentalTruckCard from '../../truck/choose/rental/list/card';
import RentalOrderCreateExtra from '../create/rental/extra';
import RentalOrderDetailCertificate from '../detail/rental/certificate';
import RentalOrderDetailDate from '../detail/rental/date';
import RentalOrderDetailFee from '../detail/rental/fee';
import './index.scss';

const OrderSettle = ({ order_id }) => {
  const [value, setValue] = useState();
  const { config, data } = useLongList(rentOrderDetailApi, {
    api: {
      data: {
        order_id,
        for_settle: 1,
      },
    },
    isNonList: true,
  });

  const handleSubmit = async () => {
    if (!value.loss_certificate_list?.length) {
      Taro.kbToast({
        text: '请上传车辆定损凭证',
      });
      return;
    }
    const isSuccess = await rentOrderSettle({
      order_id,
      ...value,
    });
    sleep(400);
    if (isSuccess) {
      Taro.navigator();
    }
  };

  return (
    <PageLayout
      renderFooter={
        <View className='kb-background__white kb-spacing-md'>
          <AtButton type='primary' circle onClick={handleSubmit}>
            提交
          </AtButton>
        </View>
      }
    >
      <View className='order-detail-settle'>
        <LongList data={config}>
          {data && (
            <View className='kb-spacing-md'>
              <RentalTruckCard data={data} source='orderDetail' />
              <RentalOrderDetailDate data={data} />
              <RentalOrderCreateExtra data={data} source='orderDetail' />
              <RentalOrderDetailFee data={data} onChange={setValue} />
              <RentalOrderDetailCertificate data={data} />
            </View>
          )}
        </LongList>
      </View>
    </PageLayout>
  );
};

OrderSettle.options = {
  addGlobalClass: true,
};

export default OrderSettle;
