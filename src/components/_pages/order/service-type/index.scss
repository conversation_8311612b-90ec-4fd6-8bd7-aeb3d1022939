/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-service-type {
  .min-width-140 {
    min-width: 140px;
  }

  .min-width-150 {
    min-width: 150px;
  }

  .kb-service-type__mini {
    display: flex;
  }

  .tag {
    position: relative;
    display: inline-flex;
    width: fit-content;
    height: 52px;
    margin-left: 20px;
    padding-right: 20px;
    padding-left: 5px;
    color: #fff;
    font-size: 28px;
    line-height: 52px;
    background: #ccc;
    border-radius: 6px;

    &-duihao {
      position: absolute;
      right: 4px;
      bottom: 4px;
      width: 0;
      height: 0;
      border-bottom: 27px solid #fff;
      border-left: 27px solid transparent;

      &:before {
        position: absolute;
        right: 5px;
        bottom: -20px;
        width: 10px;
        height: 6px;
        background: transparent;
        border-bottom: $width-base solid #ccc;
        border-left: $width-base solid #ccc;
        transform: rotate(-45deg);
        content: '';
      }
    }

    &-on {
      background: #009fff;
    }

    &-on &-duihao:before {
      border-bottom: $width-base solid #009fff;
      border-left: $width-base solid #009fff;
    }

    &-mini {
      margin-right: 20px;
      margin-left: 0;
      padding: 0 40px 0 20px;
    }
  }
}
