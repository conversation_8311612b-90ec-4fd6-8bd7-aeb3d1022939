/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro, { Fragment } from '@tarojs/taro';
import { AtIcon, AtModal, AtModalContent } from 'taro-ui';

class Index extends Taro.Component {
  constructor() {
    super(...arguments);
    this.state = {
      isOpend: true,
    };
  }

  open = () => {};

  render() {
    const { isOpend } = this.state;
    const { type } = this.props;
    return (
      <Fragment>
        <AtIcon
          className='kb-size__lg kb-color__orange kb-margin-sm-l'
          prefixClass='kb-icon'
          value='alert'
          onClick={this.open}
        />
        <AtModal isOpened={isOpend}>
          <AtModalContent>
            {
              // type=='jd'?<View class="size-14">
              type == 'sf' ? (
                <View class='size-14'>
                  {/* <View class="display-flex-row flex-align-start">
                <View class="min-width-140 color-blue">特快送：</View>
                <View>优质航空资源 跨省货品寄送</View>
              </View> */}
                  <View class='display-flex-row flex-align-start'>
                    <View class='min-width-140 color-blue'>特惠送：</View>
                    <View>为用户提供门到门的快递服务</View>
                  </View>
                </View>
              ) : null
            }
          </AtModalContent>
        </AtModal>
      </Fragment>
    );
  }
}

export default Index;
