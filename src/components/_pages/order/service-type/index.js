/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';

class Index extends Taro.Component {
  static defaultProps = {
    mode: 'normal', //mini
    payOnline: false, // 是否线上付
  };

  static options = {
    addGlobalClass: true,
  };

  constructor() {
    super(...arguments);
    this.product_code = {
      jd: [
        { label: '特惠送', value: 'p1' },
        // { label: '特快送', value: 'p2' },
      ],
      sf: [
        { label: '标快', value: 'offer' },
        { label: '特快', value: 'express' },
      ],
      sfky: [{ label: '标准达', value: 'SE0141' }],
      sxjd: [
        { label: '网点自提', value: '1' },
        { label: '送货上门', value: '2' },
      ],
    };
  }

  componentDidMount() {
    this.setInitData();
  }

  componentDidUpdate(preProps) {
    const { type: preType, current, payOnline: prePayOnline } = preProps;
    const { type, payOnline } = this.props;
    if (preType != type || !current || payOnline != prePayOnline) {
      this.setInitData();
    }
  }

  fixProductCode = (then) => {
    const { type, payOnline } = this.props;
    if (type == 'jd' && payOnline) {
      this.product_code['jd'] = [{ label: '特惠送', value: 'p1' }];
    }
    then && then();
  };

  setInitData() {
    this.fixProductCode(() => {
      const { type, current } = this.props;
      const item = this.product_code[type];
      const index = item.findIndex((item) => item.value == current);
      if (item && index < 0) {
        const [{ value }] = item;
        this.onSelect(value);
      }
    });
  }

  onSelect = (value) => {
    this.props.onChange(value);
  };

  onShowTips = () => {
    const { type } = this.props;
    Taro.kbModal({
      className: 'kb-size__base',
      content:
        type == 'jd'
          ? [
              // { text: '特快送：', className: 'kb-color__brand' },
              // { text: '优质航空资源 跨省货品寄送' },
              { text: '特惠送：', className: 'kb-color__brand' },
              { text: '为用户提供门到门的快递服务' },
            ]
          : type == 'sf'
          ? [
              { text: '标快：', className: 'kb-color__brand' },
              {
                text: '为您提供“价格更优、时效稳定、托寄无忧、服务范围广”门到门的标准快递服务',
              },
              { text: '特快：', className: 'kb-color__brand' },
              {
                text: '为您提供“快速、准时、稳定”的高品质、门到门的标准快递服务',
              },
            ]
          : type == 'sfky'
          ? [
              { text: '重货包裹：', className: 'kb-color__brand' },
              {
                text: '主打20-100KG，托运大件包裹类、仓店调拨类货物，为您提供高品质、门到门的物流服务。',
              },
              {
                text: '选择重货包裹服务时，需办理保价服务并支付保费，保费最低1元/票。声明价值＞500元，保费按官网上所公示的收费标准收取。',
              },
              { text: '小票零担：', className: 'kb-color__brand' },
              {
                text: '主打100-500KG，为满足您批量发货，而推出兼顾价格与时效的经济型物流服务。',
              },
              {
                text: '选择小票零担服务时，需办理保价服务并支付保费，保费最低10元/票。声明价值＞2000元，保费=声明价值* 标准服务费率，四舍五入取整。',
              },
            ]
          : [],
    });
  };

  render() {
    const { current, type, mode } = this.props;
    const list = this.product_code[type] || [];
    const rootCls = classNames('kb-service-type', {
      'kb-form__item': mode != 'mini',
      'kb-service-type__mini': mode == 'mini',
    });
    return (
      <View className={rootCls}>
        {list && list.length > 1 && (
          <View className='at-row at-row__justify--between at-row__align--center'>
            {mode !== 'mini' && (
              <View className='item-title at-row at-row__align--center'>
                {type == 'sxjd' ? '送货方式' : '服务类型'}
                {type != 'sxjd' && type != 'sfky' && (
                  <AtIcon
                    className='kb-color__orange kb-margin-sm-l'
                    prefixClass='kb-icon'
                    value='help'
                    size='18'
                    onClick={this.onShowTips}
                  />
                )}
              </View>
            )}
            <View
              className={
                mode == 'mini'
                  ? 'at-row at-row__align--center kb-spacing-sm-r'
                  : 'at-row at-row__justify--end at-row__align--center kb-spacing-sm-r'
              }
            >
              {list.map((item) => {
                const tagOnCls = classNames('tag', {
                  'tag-on': current == item.value,
                });
                return (
                  <View
                    className={tagOnCls}
                    key={item.label}
                    onClick={() => this.onSelect(item.value)}
                  >
                    {item.label}
                    <View class='tag-duihao'></View>
                  </View>
                );
              })}
              {mode == 'mini' && type != 'sxjd' && (
                <AtIcon
                  className='kb-color__orange kb-margin-sm-l'
                  prefixClass='kb-icon'
                  value='help'
                  size='18'
                  onClick={this.onShowTips}
                />
              )}
            </View>
          </View>
        )}
      </View>
    );
  }
}

export default Index;
