/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { AtButton, AtIcon } from 'taro-ui';
import { createOrder } from '~/services/order';
import { requestPayment } from '~/utils/qy';
import { useActionContext } from '~base/components/actionContext/_utils/hooks';
import { sleep } from '~base/utils/utils';
import './index.scss';
import TimePicker from './timepicker';
import { orderDone } from '~/components/_pages/index/client/_utils/orderProcess';

const Footer = (props) => {
  const { onOpenChange } = useActionContext('timepicker');
  const { onChange, pickupTime, data, pay_amount } = props;

  const { date, hour, minute } = pickupTime || {};
  const time = date + ' ' + hour + ':' + minute;

  const showDate = useMemo(() => {
    const now = dayjs();
    if (dayjs(time).diff(now, 'minute') < 30) {
      return '现在用车';
    }
    return '预约 ' + dayjs(time).format('MM月DD日 HH:mm') + '用车';
  }, [time]);

  const handleSubmit = () => {
    createOrder({
      ...data,
      amount: pay_amount,
      is_reservation: dayjs(time).diff(dayjs(), 'minute') < 30 ? '0' : '1',
      vehicle_time: dayjs(time).format('YYYY-MM-DD HH:mm'),
    }).then((res) => {
      requestPayment(res).then(async () => {
        if (!res.order_id) {
          Taro.kbToast({
            text: '订单生成失败，请前往订单查看',
          });
          return;
        }
        orderDone();
        Taro.kbToast({
          text: '下单成功',
        });
        Taro.navigator({
          url: 'index'
        });
        await sleep(300);
        Taro.navigator({
          url: 'order/detail',
          options: {
            order_id: res.order_id,
          },
        });
      });
    });
  };

  return (
    <View className='kb-spacing-md kb-background__white'>
      <View className='kb-create__footer at-row at-row__align--center at-row__justify--between'>
        <View className='kb-flex1'>
          <View className='at-row at-row__align--center'>
            <AtIcon prefixClass='kb-icon' value='clock' className='kb-size__xl kb-color__brand' />
            <Text className='kb-spacing-sm-lr kb-size__sm'>{showDate}</Text>
            <View
              className='kb-color__brand kb-size__sm'
              hoverClass='kb-hover-opacity'
              onClick={() => onOpenChange(true)}
            >
              修改
            </View>
          </View>
        </View>
        <AtButton className='kb-create__btn' circle type='primary' onClick={handleSubmit}>
          立即下单
        </AtButton>
      </View>
      <TimePicker onChange={onChange} />
    </View>
  );
};

Footer.options = {
  addGlobalClass: true,
};

export default Footer;
