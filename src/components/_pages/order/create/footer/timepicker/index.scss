/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.timepicker {
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: $spacing-v-md;
    font-size: $font-size-lg;
    border-bottom: 1px solid #eee;
  }

  &-view {
    height: 400px;
  }

  &-item {
    color: #333;
    font-size: 28px;
    line-height: 70px;
    text-align: center;

    // &.disabled {
    //   color: #ccc;
    // }
  }

  &-close {
    color: $color-grey-2;
    font-size: $font-size-sm!important;
    transform: rotate(45deg);
  }
}
