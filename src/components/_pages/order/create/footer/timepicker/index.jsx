/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { PickerView, PickerViewColumn, RootPortal, View } from '@tarojs/components';
import { useEffect, useState } from '@tarojs/taro';
import dayjs from 'dayjs';
import { useCallback } from 'react';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import { useActionContext } from '~base/components/actionContext/_utils/hooks';
import './index.scss';

const TimePicker = (props) => {
  const {
    duration = 50,
    minuteStep = 10, // 分钟间隔，默认10分钟
    defaultSelected = [0, 0, 0], // 默认选中值索引
    defaultTime = null, // 默认选中的时间
    onChange = () => {}, // 选择变化时的回调函数
  } = props;
  const { open, onOpenChange } = useActionContext('timepicker');

  // 选项数据
  const [options, setOptions] = useState([]);

  // 选中的索引值
  const [selectedValue, setSelectedValue] = useState(defaultSelected);

  const now = dayjs();

  // 新增：根据日期索引动态生成小时和分钟选项
  const generateDynamicOptions = (dateIndex, dateOptions, selectedValueParam) => {
    let hourStart = 0;
    let minuteStart = 0;
    let isToday = false;
    if (dateOptions[dateIndex] && dateOptions[dateIndex].isToday) {
      isToday = true;
      hourStart = now.hour();
      minuteStart = now.minute();
    }
    // 生成小时选项
    const hours = [];
    let hourAdjust = 0;
    // 如果是今天且分钟大于等于50，则小时从下一小时开始
    if (isToday && minuteStart >= 50) {
      hourAdjust = 1;
    }
    if (isToday) {
      for (let i = hourStart + hourAdjust; i <= 23; i++) {
        hours.push({ label: `${i}点`, value: i });
      }
      // 如果当前时间已是23:50及以后，今天已无可选小时，直接跳到明天
      if (hours.length === 0) {
        // 直接返回明天的选项
        const tomorrowOptions = generateDynamicOptions(dateIndex + 1, dateOptions, selectedValueParam);
        return tomorrowOptions;
      }
    } else {
      for (let i = 0; i <= 23; i++) {
        hours.push({ label: `${i}点`, value: i });
      }
    }
    // 生成分钟选项
    const minutes = [];
    // 当前小时（小时栏的value）
    const currentHour = hours[selectedValueParam[1]]?.value;
    if (isToday && currentHour === hourStart) {
      // 今天且选中当前小时，只展示当前分钟之后的时间
      let firstMinute = minuteStart + 1;
      let startMinute = Math.ceil(firstMinute / minuteStep) * minuteStep;
      for (let i = startMinute; i < 60; i += minuteStep) {
        const formattedMin = i === 0 ? '00' : i < 10 ? `0${i}` : `${i}`;
        minutes.push({ label: `${formattedMin}分`, value: formattedMin });
      }
      if (minutes.length === 0) {
        minutes.push({ label: '00分', value: '00' });
      }
    } else {
      // 今天且选中未来小时，或未来天数，分钟全展示
      for (let i = 0; i < 60; i += minuteStep) {
        const formattedMin = i === 0 ? '00' : i < 10 ? `0${i}` : `${i}`;
        minutes.push({ label: `${formattedMin}分`, value: formattedMin });
      }
    }
    return [dateOptions, hours, minutes];
  };

  // 生成日期选项
  const generateDates = () => {
    let startDay = 0;
    // 如果当前时间已是23:50及以后，今天已无可选时间，直接从明天开始
    if (now.hour() === 23 && now.minute() >= 50) {
      startDay = 1;
    }

    const dates = [];
    for (let i = startDay; i < duration + startDay; i++) {
      const date = now.add(i, 'day');
      const weekMap = ['日', '一', '二', '三', '四', '五', '六'];
      const weekDay = weekMap[date.day()];
      const isToday = i === 0;

      dates.push({
        label: isToday
          ? `${date.format('M月D号')} 今天`
          : i === 1
            ? `${date.format('M月D号')} 明天`
            : `${date.format('M月D号')} 周${weekDay}`,
        value: date.format('YYYY-MM-DD'),
        isToday,
      });
    }
    return dates;
  };

  // useCallback包裹，避免useEffect依赖警告
  const memoGenerateDates = useCallback(generateDates, [duration]);
  const memoGenerateDynamicOptions = useCallback(generateDynamicOptions, [minuteStep]);

  // 初始化数据和监听selectedValue[0]变化
  useEffect(() => {
    const dateOptions = memoGenerateDates();
    let dateIndex = selectedValue[0] || 0;
    if (dateIndex >= dateOptions.length) dateIndex = 0;
    const [dates, hours, minutes] = memoGenerateDynamicOptions(
      dateIndex,
      dateOptions,
      selectedValue,
    );
    setOptions([dates, hours, minutes]);
  }, [memoGenerateDates, memoGenerateDynamicOptions, selectedValue]);

  // defaultTime 逻辑，首次挂载时设置 selectedValue
  useEffect(() => {
    if (defaultTime) {
      const dateOptions = memoGenerateDates();
      const defaultDate = dayjs(defaultTime);
      if (defaultDate.isValid()) {
        const dateStr = defaultDate.format('YYYY-MM-DD');
        const hour = defaultDate.hour();
        const minute = defaultDate.minute();
        let dateIndex = dateOptions.findIndex((item) => item.value === dateStr);
        dateIndex = dateIndex >= 0 ? dateIndex : 0;
        const [, hourOptions, minuteOptions] = memoGenerateDynamicOptions(
          dateIndex,
          dateOptions,
          [0, 0, 0],
        );
        let hourIndex = hourOptions.findIndex((item) => item.value === hour);
        hourIndex = hourIndex >= 0 ? hourIndex : 0;
        const closestMinute = Math.round(minute / minuteStep) * minuteStep;
        const formattedMinute =
          closestMinute === 0
            ? '00'
            : closestMinute < 10
            ? `0${closestMinute}`
            : `${closestMinute >= 60 ? 59 : closestMinute}`;
        let minuteIndex = minuteOptions.findIndex((item) => item.value === formattedMinute);
        minuteIndex = minuteIndex >= 0 ? minuteIndex : 0;
        setSelectedValue([dateIndex, hourIndex, minuteIndex]);
      }
    }
  }, [defaultTime, minuteStep, duration, memoGenerateDates, memoGenerateDynamicOptions]);

  // 监听selectedValue变化，确保无论是手动选择还是重置，都能正确触发onChange
  useEffect(() => {
    // 确保options已经初始化
    if (options.length === 3 && selectedValue.length === 3) {
      const selectedDate = options[0][selectedValue[0]];
      const selectedHour = options[1][selectedValue[1]];
      const selectedMinute = options[2][selectedValue[2]];

      if (selectedDate && selectedHour && selectedMinute) {
        // 确保小时和分钟值正确格式化
        const hourValue = parseInt(selectedHour.value, 10);
        const minuteValue = selectedMinute.value; // 已经是字符串格式

        const result = {
          date: selectedDate.value,
          hour: hourValue,
          minute: minuteValue,
        };

        // 调用onChange回调
        onChange(result);
      }
    }
  }, [options, selectedValue, onChange]);

  // 处理选择器值变化
  const handleChange = (e) => {
    const newValue = [...e.detail.value]; // 创建副本，避免直接修改e.detail.value

    // 检测日期变化，重置小时和分钟
    if (newValue[0] !== selectedValue[0]) {
      newValue[1] = 0; // 重置小时为第一个选项
      newValue[2] = 0; // 重置分钟为第一个选项
    }
    // 检测小时变化，重置分钟
    else if (newValue[1] !== selectedValue[1]) {
      newValue[2] = 0; // 重置分钟为第一个选项
    }

    setSelectedValue(newValue);

    // 如果选项数据已经初始化，则触发onChange回调
    if (options.length === 3) {
      const selectedDate = options[0][newValue[0]];
      const selectedHour = options[1][newValue[1]];
      const selectedMinute = options[2][newValue[2]];

      if (selectedDate && selectedHour && selectedMinute) {
        // 确保小时和分钟值正确格式化
        const hourValue = parseInt(selectedHour.value, 10);
        const minuteValue = selectedMinute.value; // 已经是字符串格式

        const result = {
          date: selectedDate.value,
          hour: hourValue,
          minute: minuteValue,
        };

        // 调用onChange回调
        onChange(result);
      }
    }
  };

  return (
    <RootPortal>
      <AtFloatLayout isOpened={open} onClose={() => onOpenChange(false)}>
        <View className='timepicker-header'>
          <View>选择用车时间</View>
          <View hoverClass='kb-hover-opacity' onClick={() => onOpenChange(false)}>
            <AtIcon prefixClass='kb-icon' value='add' className='timepicker-close' />
          </View>
        </View>
        <PickerView className='timepicker-view' value={selectedValue} onChange={handleChange}>
          {options.map((columnData, columnIndex) => (
            <PickerViewColumn key={columnIndex}>
              {columnData.map((item, index) => (
                <View key={index} className='timepicker-item'>
                  {item.label}
                </View>
              ))}
            </PickerViewColumn>
          ))}
        </PickerView>
      </AtFloatLayout>
    </RootPortal>
  );
};

TimePicker.options = {
  addGlobalClass: true,
};

export default TimePicker;
