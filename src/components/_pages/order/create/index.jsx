/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { AtIcon } from 'taro-ui';
import { loadQuotation } from '~/services/order';
import KbScrollView from '~base/components/scroll-view';
import { navigateToDocument } from '~base/utils/navigator';
import { useRequest } from '~base/utils/request/hooks';
import Line from '../../index/server/order/components/line';
import TruckCardType from '../../truck/list/item/card/type';
import Footer from './footer';
import './index.scss';

const OrderCreateIndex = (props) => {
  const { data } = props;
  const [pickupTime, setPickupTime] = useState(null);

  const showDate = useMemo(() => {
    const { date, hour, minute } = pickupTime || {};
    const time = date + ' ' + hour + ':' + minute;
    return dayjs(time).format('YYYY年MM月DD日 HH:mm');
  }, [pickupTime]);

  const { data: { pay_amount } = {}, run } = useRequest(loadQuotation, {
    manual: true,
  });

  useEffect(() => {
    data?.routes?.length > 0 && run({ ...data });
  }, [data]);

  return (
    <KbScrollView
      scrollY
      className='kb-scrollview kb-footer__white'
      renderFooter={
        <Footer
          data={data}
          pay_amount={pay_amount}
          pickupTime={pickupTime}
          onChange={setPickupTime}
        />
      }
    >
      <View className='kb-spacing-md'>
        <View className='kb-box kb-spacing-md-lr'>
          <View className='kb-border-b kb-spacing-lg-tb kb-size__lg kb-text__bold'>
            <TruckCardType data={data} />
          </View>
          <Line className='kb-create-timeline' data={data} hideTitle />
          <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-b'>
            <View className='kb-color__greyer'>用车时间</View>
            <View>{showDate}</View>
          </View>
        </View>
        <View className='kb-box kb-spacing-md-lr'>
          <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-tb kb-border-b'>
            <View>费用</View>
            <View>{pay_amount || '--'}元</View>
          </View>
          <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-tb'>
            <View hoverClass='kb-hover' onClick={() => navigateToDocument(3)}>
              <Text className='kb-color__orange'>开票须知</Text>
              <AtIcon
                prefixClass='kb-icon'
                value='amaze'
                className='kb-size__base kb-color__orange kb-spacing-sm-l'
              />
            </View>
          </View>
        </View>
        <View className='kb-box kb-spacing-md-lr'>
          <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-tb kb-border-b'>
            <View>订单备注</View>
            <View className='kb-color__grey'>{data.note}</View>
          </View>
          <View className='at-row at-row__align--center at-row__justify--between kb-spacing-lg-tb'>
            <View>联系电话</View>
            <View className='kb-color__grey'>{data.contact_phone}</View>
          </View>
        </View>
      </View>
    </KbScrollView>
  );
};

export default OrderCreateIndex;
