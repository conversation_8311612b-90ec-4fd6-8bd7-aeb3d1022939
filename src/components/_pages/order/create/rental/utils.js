/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useState } from 'react';
import { getRentQuotation } from '~/services/rental';
import { usePostMessage } from '~base/hooks/page';
import { useRequest } from '~base/utils/request/hooks';

export const useRentalOrderCreate = () => {
  const [data, setData] = useState({});
  const { run: getQuotation, data: quotation } = useRequest(getRentQuotation, {
    manual: true,
  });
  const { truckOwner: { vehicle_owner_id } = {} } = data;

  usePostMessage((key, data) => {
    const { params } = data;
    if (key === 'routerParamsChange') {
      const _data = params?.data || {};
      setData(_data);
      getQuotation(transformRentParams(_data));
    }
  }, []);

  const updateData = (val) => {
    setData((pre) => ({
      ...pre,
      ...val,
    }));
  };

  return {
    data,
    updateData,
    quotation,
    vehicle_owner_id,
  };
};

export const transformRentParams = (data) => {
  const {
    vehicle_type,
    rental_time,
    rentalTruckInfo: { brand, model, type, truckload } = {},
    truckOwner: { vehicle_owner_id } = {},
  } = data;
  return {
    vehicle_owner_id,
    brand,
    model,
    type,
    vehicle_type,
    truckload,
    lease_start_day: rental_time[0],
    lease_end_day: rental_time[1],
  };
};
