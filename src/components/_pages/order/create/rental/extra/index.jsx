/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';

const RentalOrderCreateExtra = ({ data, source }) => {
  const list = [
    {
      label: '车辆租赁费用',
      key: 'total_rent',
    },
    {
      label: '车辆租赁押金',
      key: 'mortgage',
    },
  ];
  return (
    <View className='kb-box kb-spacing-md-lr'>
      <View className='kb-size__lg kb-border-b kb-spacing-md-tb'>租金明细</View>
      {list.map((item, index) => (
        <View
          key={item.key}
          className={classNames(
            'at-row at-row__align--center at-row__justify--between kb-spacing-md-b',
            {
              'kb-spacing-md-t': index === 0,
            },
          )}
        >
          <View className='kb-size__base kb-color__greyer'>{item.label}</View>
          <View className='kb-size__base2'>{data?.[item.key] || 0}</View>
        </View>
      ))}
      <View
        className={classNames('at-row at-row__align--center kb-spacing-md-tb kb-border-t', {
          'kb-size__lg at-row__justify--between': source == 'orderDetail',
        })}
      >
        {source == 'orderDetail' ? (
          <>
            <View>合计</View>
            <View>{data?.amount}</View>
          </>
        ) : (
          <>
            <AtIcon
              prefixClass='kb-icon'
              value='info-circle'
              className='kb-size__sm kb-color__greyer'
            />
            <View className='kb-spacing-sm-l kb-size__base kb-color__greyer'>
              还车后7天内，若车主未发起结算，押金原额退还
            </View>
          </>
        )}
      </View>
    </View>
  );
};

RentalOrderCreateExtra.options = {
  addGlobalClass: true,
};

export default RentalOrderCreateExtra;
