/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbScrollView from '@base/components/scroll-view';
import { View } from '@tarojs/components';
import Others from '~/components/_pages/index/client/others';
import RentalTime from '~/components/_pages/index/client/rental/timeRange';
import RentalTruckCard from '~/components/_pages/truck/choose/rental/list/card';
import OperateDepartSelector from '../../detail/operate/depart/modal/form/selector';
import RentalOrderCreateExtra from './extra';
import Footer from './footer';
import './index.scss';
import { useRentalOrderCreate } from './utils';

const RentalOrderCreate = () => {
  const { data, updateData, quotation, vehicle_owner_id } = useRentalOrderCreate();

  return (
    <KbScrollView
      scrollY
      className='kb-scrollview kb-rental-order-create'
      renderFooter={<Footer data={data} quotation={quotation} />}
    >
      <View className='kb-spacing-md-lr kb-spacing-md-b'>
        <RentalTruckCard data={data.rentalTruckInfo} />
        <View className='kb-box kb-spacing-md-lr'>
          <RentalTime readOnly data={data} />
          <OperateDepartSelector
            className='kb-spacing-none-l'
            label='收车点'
            value={data.stop?.label}
            params={{ vehicle_owner_id }}
            onChange={(val) => updateData({ stop: val })}
          />
        </View>
        <Others className='kb-rental-order-create_other' data={data} readOnly />
        <RentalOrderCreateExtra data={quotation} />
      </View>
    </KbScrollView>
  );
};

RentalOrderCreate.options = {
  addGlobalClass: true,
};

export default RentalOrderCreate;
