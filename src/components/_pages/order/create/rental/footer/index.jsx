/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbAgreement from '@/components/_pages/agreement';
import KbCheckbox from '@base/components/checkbox';
import { Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import { AtButton } from 'taro-ui';
import { orderDone } from '~/components/_pages/index/client/_utils/orderProcess';
import { createRentOrder } from '~/services/rental';
import { requestPayment } from '~/utils/qy';
import { transformRentParams } from '../utils';
import './index.scss';

const Footer = (props) => {
  const { data, quotation } = props;
  const [agree, setAgree] = useState(false);

  const handleSubmit = () => {
    if (!data.stop) {
      Taro.kbToast({
        text: '请选择收车点',
      });
      return;
    }
    if (!agree) {
      Taro.kbModal({
        title: '请仔细阅读并同意',
        content: (
          <View className='at-row at-row__justify--center'>
            <KbAgreement agreeType='rentalAgreement' />
          </View>
        ),
        confirmText: '同意并继续',
        cancelText: '不同意',
        onConfirm: () => {
          setAgree(true);
        },
      });
      return;
    }
    const { stop: { value: stop_id, label: stop_name } = {}, contact_phone, note } = data || {};
    createRentOrder({
      ...transformRentParams(data),
      stop_id,
      stop_name,
      contact_phone,
      note,
    }).then((res) => {
      requestPayment(res).then(async () => {
        if (!res.order_id) {
          Taro.kbToast({
            text: '订单生成失败，请前往订单查看',
          });
          return;
        }
        orderDone();
        Taro.kbToast({
          text: '下单成功',
        });
        Taro.navigator({
          url: 'order',
          target: 'tab',
        });
      });
    });
  };

  return (
    <View className='kb-spacing-md kb-background__white'>
      <View className='at-row at-row__align--center kb-margin-md-b'>
        <KbCheckbox
          label='我已阅读并同意'
          checked={agree}
          onChange={setAgree}
          className='kb-color__black'
        />
        <KbAgreement agreeType='rentalAgreement' />
      </View>
      <View className='kb-create__footer at-row at-row__align--center at-row__justify--between'>
        <View className='kb-flex1'>
          <Text>合计：</Text>
          <Text className='kb-color__brand kb-size__lg'>￥{quotation?.total_amount || 0}</Text>
        </View>
        <AtButton className='kb-create__btn' circle type='primary' onClick={handleSubmit}>
          提交订单
        </AtButton>
      </View>
    </View>
  );
};

Footer.options = {
  addGlobalClass: true,
};

export default Footer;
