/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { dateCalendar } from '@base/utils/utils';
import classNames from 'classnames';
import './index.scss';

const Index = (props) => {
  const { data, border, color } = props;
  const typesMap = {
    courier: '快递员',
    dak: '驿站',
  };
  const rootCls = classNames('kb-coupon', {
    'kb-coupon__border': border,
  });
  return (
    <View className={rootCls}>
      <View className='coupon-info' style={{ color }}>
        {data.cost && <View className='coupon-info__cost'>{data.cost}</View>}
        <View className='coupon-info__content'>
          {data.name && (
            <View className='coupon-info__content--item'>
              仅限对{typesMap[data.type] || ''}
              {data.name}下单可用
            </View>
          )}
          {data.date && (
            <View className='coupon-info__content--item'>
              有效期至{dateCalendar(data.date, { timer: true })}
            </View>
          )}
          {data.desc && <View>{data.desc}</View>}
          {props.children}
        </View>
      </View>
    </View>
  );
};

Index.defaultProps = {
  data: {},
  border: true,
  color: 'white',
};
Index.options = { addGlobalClass: true };

export default Index;
