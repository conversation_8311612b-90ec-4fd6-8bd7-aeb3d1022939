/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-scan-coupon {
  position: relative;
  width: 580px;
  height: 650px;
  margin: 0 auto;
  color: rgb(241, 16, 46);
  background: url('https://cdn-img.kuaidihelp.com/miniapp/miniapp_yz/scan-coupon-2.png?v=4')
    no-repeat center top / 100% auto;
  &--fee {
    &::before {
      font-size: $font-size-base;
      content: '¥';
    }
    position: absolute;
    top: 300px;
    left: 50%;
    font-weight: 800;
    font-size: $font-size-xxl * 1.5;
    text-align: center;
    transform: translateX(-50%);
  }
  &--btn {
    position: absolute;
    bottom: 53px;
    left: 50%;
    z-index: 999;
    display: inline-block;
    width: 484px;
    height: 90px;
    color: #d14222;
    font-weight: bold;
    font-size: $font-size-xl;
    line-height: 90px;
    letter-spacing: 6px;
    white-space: nowrap;
    text-align: center;
    background-image: linear-gradient(to bottom, #fdc89c, #fcf6d2);
    border: none;
    border-radius: $border-radius-arc;
    box-shadow: 0px 5px 0px 0px rgba(196, 49, 32, 0.4);
    transform: translateX(-50%);
    &-pay {
      font-size: $font-size-lg;
    }
  }
  &--num {
    position: absolute;
    top: 260px;
    right: 70px;
    padding: 10px 8px;
    color: $color-white;
    font-size: $font-size-xs + 2px;
    background: #e9b372;
    border-top-right-radius: 10px;
    border-bottom-left-radius: 10px;
  }
  &--desc {
    position: absolute;
    top: 390px;
    right: 0;
    left: 0;
    color: #de8953;
    font-size: 24px;
    text-align: center;
  }
}
