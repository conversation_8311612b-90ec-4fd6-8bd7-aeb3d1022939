/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { formatNumeral } from '@/components/_pages/order/_utils/order.pay';
import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import { debounce } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro, { useEffect, useMemo, useState } from '@tarojs/taro';
import { AtCurtain } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const [isOpened, updateIsOpened] = useState(false);
  const [coupon, updateCoupon] = useState({});

  // 领取券
  const receiveCoupon = (data) => {
    return new Promise((resolve) => {
      request({
        url:
          process.env.MODE_ENV === 'wkd'
            ? '/g_wkd/v1/WeApp/createCustomCoupon'
            : '/api/weixin/mini/minpost/Coupon/createCustomCoupon',
        data,
        toastLoading: false,
        toastError: false,
        onThen(res) {
          if (res.code != 0) {
            setTimeout(() => {
              Taro.kbToast({
                text: res.msg,
              });
            }, 500);
          }
          resolve(res);
        },
      });
    });
  };

  // 获取券详情
  const getCustomCouponInfo = (data) => {
    return new Promise((resolve) => {
      const { coupon_id } = data;
      request({
        url:
          process.env.MODE_ENV === 'wkd'
            ? '/g_wkd/v1/WeApp/getCustomCouponInfo'
            : '/api/weixin/mini/minpost/Coupon/getCustomCouponInfo',
        data: { coupon_id },
        toastLoading: false,
        toastError: true,
        onThen(res) {
          resolve(res);
        },
      });
    });
  };

  useEffect(() => {
    console.log('扫码优惠券', data);
    if (data && data.coupon_id) {
      getCustomCouponInfo(data).then((res) => {
        console.log('扫码优惠券-详情', res && res.data);
        if (res.code == 0 && res.data) {
          const { coupon_random_price, coupon_price } = res.data || {};
          if (coupon_price && Number(coupon_price) > 0) {
            // 需要支付的优惠券
            data.cost = coupon_random_price ? formatNumeral(coupon_random_price) : '0.00';
            data.coupon_price = Number(coupon_price);
            updateCoupon({ ...res.data, ...data });
            updateIsOpened(true);
          } else {
            receiveCoupon(data).then(({ data: _data, code }) => {
              if (code == 0 && _data) {
                data.cost = _data.cost ? formatNumeral(_data.cost) : '0.00';
                data.coupon_price = Number(coupon_price);
                updateCoupon({ ..._data, ...data });
                updateIsOpened(true);
              }
            });
          }
        }
      });
    }
  }, [data]);

  const handleClose = () => {
    updateIsOpened(false);
  };

  const handleToCard = () => {
    Taro.navigator({
      url: 'order/card',
    });
  };

  // 支付券金额
  const handlePay = debounce(
    () => {
      const { phone, index_shop_id, dak_id, coupon_id, coupon_price, cost } = coupon;
      request({
        url:
          process.env.MODE_ENV === 'wkd'
            ? '/g_wkd/v1/WeApp/getCouponPay'
            : '/api/weixin/mini/minpost/Coupon/getCouponPay',
        data: {
          phone,
          index_shop_id,
          dak_id,
          coupon_id,
          coupon_price,
          coupon_random_price: cost,
          pay_method: process.env.PLATFORM_ENV === 'alipay' ? 'alipay' : 'wechat',
        },
        toastError: true,
        onThen({ code, data }) {
          if (code == 0 && data) {
            requestPayment(data)
              .then(() => {
                setTimeout(() => {
                  Taro.kbToast({
                    text: '优惠券已领取至您的卡包',
                    onClose: () => {
                      handleToCard();
                    },
                  });
                  updateIsOpened(false);
                }, 500);
              })
              .catch((e) => {
                Taro.kbToast({
                  text: e,
                });
              });
          }
        },
      });
    },
    1500,
    {
      leading: true,
      trailing: false,
    },
  );

  const isNeedPay = useMemo(() => {
    return !!(coupon && Number(coupon.coupon_price) * 1 > 0);
  }, [coupon]);
  return (
    <AtCurtain isOpened={isOpened} onClose={handleClose} closeBtnPosition='bottom-center'>
      <View className='kb-scan-coupon'>
        <View className='kb-scan-coupon--fee'>{coupon.cost || 0}</View>
        {coupon.received_num && coupon.received_num > 1 && (
          <View className='kb-scan-coupon--num'>x{coupon.received_num}张</View>
        )}
        <View className='kb-scan-coupon--desc'>
          {isNeedPay ? '支付后系统' : ''}发放至您的优惠券卡包里
        </View>
        {isNeedPay ? (
          <View
            hoverClass='kb-hover-opacity'
            className='kb-button kb-scan-coupon--btn kb-scan-coupon--btn-pay'
            onClick={handlePay}
          >
            支付并领取({formatNumeral(coupon.coupon_price)}元)
          </View>
        ) : (
          <View
            hoverClass='kb-hover-opacity'
            className='kb-button kb-scan-coupon--btn'
            onClick={handleToCard}
          >
            立即查看
          </View>
        )}
      </View>
    </AtCurtain>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  data: {}, //二维码数据信息
};

export default Index;
