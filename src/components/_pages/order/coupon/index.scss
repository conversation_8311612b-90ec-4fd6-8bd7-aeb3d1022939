/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-coupon {
  border-radius: $border-radius-lg;
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }

  &::before {
    content: "";
    display: block;
    height: 20px;
    width: 100%;
    background: linear-gradient(to right, #FF5567, #FFE284);
  }

  .coupon-info {
    border-radius: 0 0 $border-radius-lg $border-radius-lg;
    position: relative;
    background-color: $color-white;
    padding: $spacing-v-md $spacing-h-xxl;
    display: flex;
    align-items: center;
    overflow: hidden;

    &::before,
    &::after {
      content: "";
      display: block;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background-color: currentColor;
      width: 30px;
      height: 30px;
      border-radius: $border-radius-circle;
      z-index: 1;
    }

    &::before {
      left: -15px;
    }

    &::after {
      right: -15px;
    }

    &__cost {
      color: $color-red;
      font-size: $font-size-xxl;
      padding-right: $spacing-h-md;

      &::before {
        content: "￥";
        font-size: $font-size-sm;
        vertical-align: middle;
      }
    }

    &__content {
      flex-grow: 1;
      font-size: $font-size-base;
      color: $color-grey-1;

      &--item {
        margin-bottom: $spacing-v-sm;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  &__border {
    position: relative;

    &::after {
      content: "";
      border: $border-lightest;
      border-top: 0;
      position: absolute;
      left: 0;
      right: 0;
      top: 20px;
      bottom: 0;
      z-index: 0;
      border-radius: 0 0 $border-radius-lg $border-radius-lg
    }

    .coupon-info {

      &::before,
      &::after {
        border: $border-lightest;
      }
    }
  }

}
