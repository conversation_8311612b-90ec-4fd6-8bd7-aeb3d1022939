/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbCreditDoor from '@/components/_pages/order/credit/credit-door';
import KbMoveArea from '@/components/_pages/order/move-area';
import KbServiceType from '@/components/_pages/order/service-type';
import { transferWkdAddress } from '@/components/_pages/order/_utils/index';
import { onShowFeeWay } from '@/components/_pages/order/_utils/order.edit';
import { getBrandConfig } from '@/components/_pages/order/_utils/order.relation';
import KbCurtain from '@/components/_pages/welfare/activity/curtain';
import apis from '@/utils/apis';
import KbCheckbox from '@base/components/checkbox';
import KbLoader from '@base/components/loader';
import KbMask from '@base/components/mask';
import request from '@base/utils/request';
import { debounce, isFullData, noop } from '@base/utils/utils';
import { Image, Text, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { Fragment, useCallback, useEffect, useRef, useState } from '@tarojs/taro';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import isObject from 'lodash/isObject';
import { AtIcon } from 'taro-ui';
import './index.scss';

function Index(props) {
  const {
    relationInfo,
    address,
    weight,
    volume,
    product_code = '',
    isOpenCredit = false,
    moveAreaStatus,
    moveAreaData,
    onChange = noop,
    onChangeArea = noop,
  } = props || {};
  const { brand } = relationInfo || {};
  const { brands = {} } = useSelector((state) => state.global);
  const [loading, setLoading] = useState(false);
  const [quotationList, setQuotationList] = useState([]);
  const [welfare, setWelfare] = useState({});
  const [welfareNewUserData, setWelfareNewUserData] = useState({});

  const actionRefCurrent = useRef({});
  actionRefCurrent.current.getYjQuotation = ({ addrData, weight, volume, product_code }) => {
    setLoading(true);
    actionRefCurrent.current.loading = true;
    let vol = {};
    if (volume && volume.checked) {
      vol = volume;
    }
    request({
      url: apis['order.quotation.yj'],
      data: {
        ...addrData,
        weight,
        delivery_type: product_code,
        ...vol,
      },
      toastLoading: false,
      onThen: async ({ data }) => {
        setLoading(false);
        actionRefCurrent.current.loading = false;
        let { quotation } = data || {};
        // 接口返回报价单null时，置空价格
        if (actionRefCurrent.current.hasList && !quotation) {
          quotation = actionRefCurrent.current.list.map(({ available, unavailable_msg, brand }) => {
            return {
              available,
              unavailable_msg,
              brand,
            };
          });
        }
        if (isArray(quotation)) {
          let list = [];
          let oBrandConfig = await getBrandConfig();
          list = quotation.map((item, index) => {
            let channelItem = oBrandConfig[item.brand] || {};
            item = { ...item, ...channelItem };
            index == 0 && (item.label = 'min');
            item.weightLimitMax = item.weightLimitMax * 1;
            item.weightLimitMin = item.weightLimitMin * 1;
            if (item.weightLimitMax && weight * 1 > item.weightLimitMax) {
              item.available = 0;
              item.unavailable_msg = `此品牌暂不支持${item.weightLimitMax}KG以上的货物寄递；`;
            }
            if (item.weightLimitMin && weight * 1 < item.weightLimitMin) {
              item.available = 0;
              item.unavailable_msg = `此品牌暂不支持低于${item.weightLimitMin}KG的货物寄递；`;
            }
            if (item.coupon_welfare == 1 && isArray(item.discount_list)) {
              item.discount_list.map((iitem) => {
                if (iitem.type == 'welfareCoupon') {
                  item.welfare = iitem.welfare_detail;
                  triggerUpdateWelfare(iitem.welfare_detail);
                }
              });
            }
            return item;
          });
          triggerUpdate(list);
        }
      },
    });
  };

  const getYjQuotationDebounce = useCallback(
    debounce(actionRefCurrent.current.getYjQuotation, 500, { trailing: true }),
    [],
  );

  useEffect(() => {
    if (isEmpty(address)) return;
    let addrData = transferWkdAddress(address, 'address');
    if (!isFullData(addrData)) {
      triggerUpdate([]);
      return;
    }
    getYjQuotationDebounce({ addrData, weight, volume, product_code });
  }, [address, weight, product_code, volume, isOpenCredit]);

  const triggerChange = () => {
    onChange({ changeSource: 'list', quotationList: actionRefCurrent.current.list });
  };

  // 列表更新
  // 百度渲染延迟才能正常展示选项卡；
  useEffect(() => {
    // 此处主要是更新布局
    if (!actionRefCurrent.current.hasList) return;
    triggerChange();
  }, [quotationList]);

  useEffect(() => {
    const { hasList, count } = actionRefCurrent.current;
    if (!hasList || count > 3) return;
    const { maxHeight = 0 } = moveAreaData || {};
    if (maxHeight) return;
    // maxHeight === 0且尝试次数小于3，应再次重置高度
    triggerChange();
    actionRefCurrent.current.count++;
  }, [moveAreaData]);

  useEffect(() => {
    if (welfare && welfare.id && welfare.match == 1) {
      setWelfareNewUserData({
        type: 'new-user',
        coupon: welfare,
      });
    }
  }, [welfare]);

  const onChoosebrand = (item, e) => {
    if (isObject(e)) {
      e.stopPropagation();
    }
    const { brand, available, unavailable_msg } = item || {};
    if (available <= 0 && unavailable_msg) {
      Taro.kbToast({
        text: unavailable_msg,
      });
      return;
    }
    Taro.kbUpdateRelationInfo && Taro.kbUpdateRelationInfo({ brand });
    onChange({ changeSource: 'chooseBrand' });
    actionRef.current.setScrollTopFn && actionRef.current.setScrollTopFn();
  };

  const onNavigatorDetail = ({ brand }) => {
    let data = getItemQuatation(brand);
    Taro.kbSetGlobalData('yjBrandQuotationInfo', data);
    Taro.navigator({
      url: 'order/amount',
    });
  };

  const getItemQuatation = (brand) => {
    return quotationList.find((item) => {
      return item.brand == brand;
    });
  };

  const onNoopEvent = (e) => {
    e.stopPropagation();
  };

  const triggerUpdateWelfare = (data) => {
    setWelfare(data);
    onChange({ changeSource: 'welfare', welfare: data });
  };

  const triggerUpdate = (list = []) => {
    actionRefCurrent.current.count = 0;
    actionRefCurrent.current.hasList = list && list.length > 0;
    actionRefCurrent.current.list = list;
    Taro.kbSetGlobalData('quotation', list);
    setQuotationList(list);
  };

  const handleChange = (key, data) => {
    if (key == 'product_code') {
      onChange({ changeSource: 'product_code', product_code: data });
    }
  };

  const onJumpToWelfare = () => {
    /**
     * TODO
     * 订阅消息
     * 路径
     */
    if (welfare.match != 1 && welfare.match != 3) {
      Taro.navigator({
        url: 'welfare',
        suffix: 'default',
      });
    }
  };

  const actionRef = useRef();
  const handleClickMask = () => {
    actionRef.current.switchStatus('min');
  };

  const handlePayOnlineTips = () => {
    Taro.kbModal({
      topImg: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/youhua/pay_title.png?v=1',
      content: [
        {
          text: '支持先寄后付的快递品牌，用户下单后',
        },
        {
          src: 'https://cdn-img.kuaidihelp.com/wkd/miniApp/youhua/pay_primary.png?v=1',
        },
        {
          text: '快件妥投后，平台会以包裹的结算重量对您的微信账户进行扣费',
        },
      ],
      confirmText: '我知道啦',
    });
  };

  return (
    <Fragment>
      {actionRefCurrent.current.hasList ? (
        <Fragment>
          <KbMask show={moveAreaStatus === 'max'} zIndex='1' onClick={handleClickMask} />
          <KbMoveArea data={moveAreaData} onChange={onChangeArea} actionRef={actionRef}>
            <View className='kb-estimatedFeeList-list--content'>
              {process.env.PLATFORM_ENV !== 'swan' && (
                <View className='kb-spacing-sm-b'>
                  <KbCreditDoor open={isOpenCredit} />
                </View>
              )}
              {quotationList.map((item, index) => {
                if (!item.discount_list) {
                  item.discount_list = [];
                }
                const itemCls = classNames('kb-estimatedFeeList-list-item', {
                  'make-disabled': item.available <= 0,
                  'kb-estimatedFeeList-list-item--1': brand === item.brand,
                  'kb-estimatedFeeList-list-item--2': brand !== item.brand,
                });
                const payOnlineActive = !!(item.pay == 2 || (item.pay == 3 && isOpenCredit));
                const brandInfo = brands[item.brand] || {};
                const needWxCredit = !!(item.pay == 2 || item.pay == 3);
                return (
                  <View
                    class={itemCls}
                    key={`${item.brand}-${index}`}
                    onClick={onChoosebrand.bind(null, item)}
                    hoverClass='kb-hover-opacity'
                  >
                    <View className='kb-estimatedFeeList-list-item__body at-row at-row__justify--between at-row__align--center'>
                      <View className='kb-margin-md-r'>
                        <Image
                          lazyLoad
                          mode='widthFix'
                          style={{ width: '30px', height: '30px' }}
                          src={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${item.brand}.png?v=20230314`}
                        />
                      </View>
                      <View className='at-col'>
                        <View className='at-row at-row__justify--between at-row__align--center'>
                          <View className='at-row at-row__align--center'>
                            <View className='kb-color__black yj-brand-name'>{brandInfo.name}</View>
                            {payOnlineActive && (
                              <View
                                class='at-row flex-content-center yj-pay-label'
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handlePayOnlineTips();
                                }}
                              >
                                <Text className='kb-icon kb-icon-money1 yj-pay-label--icon kb-icon-size__base' />
                                先寄后付
                              </View>
                            )}
                          </View>
                          <View
                            className='at-row at-row__justify--end at-row__align--center kb-freight__row'
                            onClick={onNavigatorDetail.bind(null, item, (e) => e.stopPropagation())}
                            hoverClass='kb-hover-opacity'
                            hoverStopPropagation
                          >
                            <View className='kb-size__sm'>预估：</View>
                            <View className='kb-freight'>
                              {loading ? (
                                <AtIcon
                                  prefixClass='kb-icon'
                                  value='loading'
                                  className='kb-icon-size__base kb-color__grey'
                                />
                              ) : (
                                <Fragment>
                                  <Text>
                                    {item.discount_price > 0 ? item.discount_price : '--'}
                                  </Text>
                                  <Text className='kb-freight__uint'>元</Text>
                                </Fragment>
                              )}
                            </View>
                            <View className='kb-icon kb-icon-arrow kb-icon-size__xs' />
                          </View>
                        </View>
                        {needWxCredit && (
                          <View className='kb-size__sm kb-margin-sm-t'>
                            {process.env.PLATFORM_ENV === 'alipay' && '可享“先寄后付，积累信用”'}
                          </View>
                        )}
                        {item.tips ? (
                          <View className='kb-estimatedFeeList-list__tips'>{item.tips}</View>
                        ) : null}
                        {item.discount_list.map((ditem) => {
                          const isActivityType =
                            ditem.type == 'double12' ||
                            ditem.type == 'newYear' ||
                            ditem.type == 'spring';
                          return (
                            <Fragment key={ditem.type}>
                              {isActivityType && (
                                <View className='cash-tag kb-margin-sm-t'>
                                  <View className='cash-img'>
                                    <Image
                                      lazyLoad
                                      className='cash-img-coin'
                                      style={{
                                        width: '12px',
                                        height: '12px',
                                      }}
                                      mode='widthFix'
                                      src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/cash/coin.png'
                                    />
                                    <Image
                                      lazyLoad
                                      className='cash-img-red'
                                      mode='widthFix'
                                      style={{
                                        width: '14px',
                                        height: '13px',
                                      }}
                                      src='https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/cash/red.png'
                                    />
                                  </View>
                                  <View
                                    className='kb-size__sm kb-color__red'
                                    style='margin-left:20px;'
                                  >
                                    {ditem.desc}
                                  </View>
                                </View>
                              )}
                            </Fragment>
                          );
                        })}
                      </View>
                      <View className='kb-spacing-md-l pt-b-20'>
                        <KbCheckbox
                          checked={brand === item.brand}
                          onChange={onChoosebrand.bind(null, item)}
                        />
                      </View>
                    </View>
                    <View className='kb-estimatedFeeList-list-item__footer'>
                      {item.product_code && brand === item.brand && (
                        <View onClick={onNoopEvent}>
                          <KbServiceType
                            mode='mini'
                            current={product_code}
                            type={relationInfo.brand}
                            payOnline={payOnlineActive}
                            onChange={(e) => handleChange('product_code', e)}
                          />
                        </View>
                      )}
                      {item.coupon_welfare == 1 && welfare.coupon_type == 3 && (
                        <View className='kb-estimatedFeeList-list-item__welfare kb-size__sm radious-a'>
                          <View onClick={onJumpToWelfare} hoverClass='kb-hover-opacity'>
                            {welfare.match == 0 && (
                              <View className='at-row'>
                                <View>本单无法使用福利券,去福利中心分享领取</View>
                              </View>
                            )}
                            {welfare.match == 1 ? (
                              welfare.fee && welfare.fee > 0 ? (
                                <View onClick={onShowFeeWay} hoverClass='kb-hover-opacity'>
                                  已优惠{welfare.fee}元，分享助力可获得最高
                                  {welfare.max_fee || '-'}元优惠&gt;
                                </View>
                              ) : (
                                <View>业务员揽收前均可在订单详情进行分享助力</View>
                              )
                            ) : null}
                            {welfare.match == 2 && (
                              <View>
                                {welfare.help_status == '3' ? (
                                  <View>本单已使用福利券翻倍优惠￥{welfare.fee}</View>
                                ) : (
                                  <View className='at-row'>
                                    <View>
                                      本单福利券优惠￥{welfare.fee}
                                      ,分享助力成功可翻倍
                                    </View>
                                  </View>
                                )}
                              </View>
                            )}
                          </View>
                        </View>
                      )}
                    </View>
                    {item.label == 'min' && <View className='kb-tag-badge__sh'>最实惠</View>}
                  </View>
                );
              })}
            </View>
          </KbMoveArea>
          <KbCurtain data={welfareNewUserData} />
        </Fragment>
      ) : loading ? (
        <View className='kb-estimatedFeeList-list__loader'>
          <KbLoader size='small' />
        </View>
      ) : null}
    </Fragment>
  );
}

Index.options = {
  addGlobalClass: true,
};

export default Index;
