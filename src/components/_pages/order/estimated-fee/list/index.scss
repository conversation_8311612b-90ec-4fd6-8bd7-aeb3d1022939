/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-estimatedFeeList-list {
  &--content {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
  }

  &-item {
    position: relative;
    margin: 0 20px;
    padding: 15px;
    overflow: hidden;
    color: #6190aa;
    background: #eff6f9;

    &__body {
      min-height: 100px;
    }

    &__footer {
      margin: 10px 20px 0 70px;
    }

    &--1 {
      order: 1;
      animation: transform 0.5s;
    }

    &--2 {
      order: 2;
    }

    &__welfare {
      width: fit-content;
      padding: 0 10px;
      color: $color-orange;
      background: $color-white;
    }

    &::after {
      position: absolute;
      right: 30px;
      bottom: 0;
      left: 30px;
      border-bottom: $border-lightest;
      content: '';
    }
  }

  &__loader {
    margin-top: -100px;
  }

  &__tips {
    width: fit-content;
    padding: 5px $spacing-h-md;
    color: $color-red;
    font-size: $font-size-xs;
    background: $color-white;
    border-radius: 50px;
  }
}

.kb-tag-badge__sh {
  position: absolute;
  top: 6px;
  right: -28px;
  width: 100px;
  padding: $spacing-v-xs 0;
  color: $color-white;
  font-size: $font-size-lg/2;
  text-align: center;
  background: linear-gradient(to right, $color-red, $color-yellow);
  transform: rotate(45deg);
  transform-origin: center;
}

.yj-brand-name {
  white-space: nowrap;
}

.yj-pay-label {
  position: relative;
  width: fit-content;
  height: 30px;
  margin-right: 10px;
  margin-left: 10px;
  padding-right: 10px;
  padding-left: 30px;
  color: $color-brand;
  font-size: 20px;
  line-height: 30px;
  background: $color-white;
  border: $width-base solid $color-brand;
  border-left: none;
  border-radius: 50px;

  &--orange {
    color: $color-orange;
    border-color: $color-orange;
  }

  &--icon {
    position: absolute;
    top: 50%;
    left: -5px;
    transform: translateY(-50%);
  }
}

.color-blue2 {
  color: #6190aa;
}

/* 置灰 */
.make-disabled {
  -webkit-filter: grayscale(100%);
  -moz-filter: grayscale(100%);
  -ms-filter: grayscale(100%);
  -o-filter: grayscale(100%);
  filter: grayscale(100%);
  filter: gray;
  filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
}

.kb-freight {
  width: fit-content;
  min-width: 100px;
  color: $color-red;
  font-weight: bold;
  white-space: nowrap;
  text-align: center;

  &__row {
    width: auto;
    white-space: nowrap;
  }

  &__uint {
    font-weight: normal;
    font-size: $font-size-sm;
  }
}

/* 滚动 */
@keyframes transform {
  0% {
    transform: translateY(400%) scale(1.5);
  }

  100% {
    transform: translateY(0) scale(1);
  }
}

/* 活动红包动画 */
.cash-tag {
  position: relative;
  width: fit-content;
}

.cash-img {
  position: absolute;
  top: 0px;
  left: 10px;
  margin-left: -10px;
}

.cash-img-coin {
  position: absolute;
  top: -6px;
  left: $width-base;
  z-index: 2;
  animation: jumpAnimotion 0.6s ease infinite;
}

@keyframes jumpAnimotion {
  0%,
  100% {
    top: -6px;
  }

  50% {
    top: -12px;
  }
}
