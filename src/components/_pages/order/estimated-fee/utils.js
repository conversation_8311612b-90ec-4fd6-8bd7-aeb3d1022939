/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { apis } from '@base/config';
import request from '@base/utils/request';
import { transferWkdAddress } from '@/components/_pages/order/_utils';
import debounce from 'lodash/debounce';

// 格式化整理报价单数据
export const formatResponseQuotation = (res) => {
  console.info('报价单返回', res);
  const { code, data } = res;
  if (code == 0 && data) {
    const { quotation: originalQuotation, user_discount_status } = data || {};
    let quotation = [];
    // 整理数据
    if (originalQuotation) {
      Object.keys(originalQuotation).map((key) => {
        const oBrandItem = originalQuotation[key] || {};
        let item = oBrandItem[user_discount_status] || {};
        item.brand = item.brand || key;
        item.user_discount_status = user_discount_status;
        quotation.push(item);
      });
    }
    res.data.originalQuotation = originalQuotation;
    res.data.quotation = quotation;
  }
  return res;
};

export const getQuotationFeeApiData = (params) => {
  let reqData = {};
  const {
    send_province,
    receive_province,
    goods_weight: weight,
    brand,
    relationData: { dakId, id, phone, relation_id },
    customer_id,
    arrive_pay,
  } = params || {};
  const { customer } = Taro.kbRelationInfo.data || {};
  const customerId = customer_id || (customer && customer.id);
  const formatFirstWeight = (v) => (v && v * 1 > 0 ? v : 1);
  let addressData = transferWkdAddress(params);
  if (process.env.MODE_ENV == 'wkd') {
    reqData = {
      ...addressData,
      weight: weight || 1,
      courier_id: id,
      phone: phone,
      area: receive_province,
    };
    if (reqData.courier_id) {
      reqData.courier_brand = brand;
    }
  } else {
    reqData = {
      area: receive_province,
      send_area: send_province,
      city: (addressData && addressData.shipping_city) || '',
      weight: formatFirstWeight(weight),
      brand,
      regiment_info: {
        ...addressData,
        brand,
        weight: formatFirstWeight(weight),
      },
      dak_id: dakId,
      courier_id: id,
      relation_id: relation_id,
      is_to_pay: arrive_pay > 0 ? 1 : 0,
    };
  }
  if (customerId) {
    reqData.customer_id = customerId;
  }
  return reqData;
};

/**
 * 获取报价单费用， 适用于快递员、驿站等
 * @param {*} params 请求参数
 * @param {*} opts 配置项
 * @returns
 */
export const getQuotationFee = (params = {}, opts = {}) => {
  const {
    formatRequest = true, // 是否格式化参数
    ignore = false, // 是否忽略
  } = opts || {};

  return new Promise((resolve) => {
    if (ignore) {
      resolve({ code: 1013, data: {} });
      return;
    }

    const loader = debounce(
      () => {
        request({
          url: apis['order.quotation'],
          toastLoading: false,
          loadingStatusKey: 'getting',
          formatRequest: (req) => {
            const reqData = formatRequest ? getQuotationFeeApiData(params) : params;
            return {
              ...req,
              ...reqData,
            };
          },
          onThen: (res) => {
            if (res && res.code == 0) {
              const { f_weight, range_one } = res.data || {};
              // 补偿: 快递员阶梯报价单第一阶梯报价单缺失情况range_one
              if (!range_one) {
                res.data.range_one = {
                  f_weight: f_weight,
                };
              }
            }
            resolve(res);
          },
        });
      },
      300,
      { trailing: true },
    );

    loader();
  });
};
