/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
import { debounce } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import isEmpty from 'lodash/isEmpty';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const {
    relationInfo,
    formData,
    extraInfo,
    carInfo,
    extraData,
    data,
    onChange = () => {},
  } = props;
  const { brand } = relationInfo || {};
  const { goods_name, goods_weight, service } = extraInfo || {};
  let reserve_start_time = extraInfo.reserve_start_time == '1h' ? '' : extraInfo.reserve_start_time;
  let reserve_end_time = extraInfo.reserve_end_time == '1h' ? '' : extraInfo.reserve_end_time;
  const { oService = {} } = service || {};
  const { car_id, car_name, city_revision } = carInfo || {};
  const { extra } = extraData || {};
  const {
    city_code,
    shipping_city_id,
    send_name,
    send_mobile,
    send_province,
    send_city,
    send_district,
    send_address,
    send_door,
    send_latitude,
    send_longitude,
    receive_name,
    receive_mobile,
    receive_province,
    receive_city,
    receive_district,
    receive_address,
    receive_door,
    receive_latitude,
    receive_longitude,
  } = formData || {};

  const [estimate, setEstimate] = useState({});
  const [loading, setLoading] = useState(false);
  const [isOpend, setIsOpend] = useState(false);

  const isFullObject = (obj) => {
    for (let key in obj) {
      if (!obj[key]) {
        return false;
      }
    }
    return true;
  };

  useEffect(() => {
    getEstimatedFee();
  }, [brand, extraInfo, formData, car_id, extra]);

  useEffect(() => {
    if (!isEmpty(data) && data.clean) {
      triggerUpdate({
        estimate: {},
      });
    }
  }, [data]);

  const getEstimatedFee = debounce(
    () => {
      let reqData = {
        brand,
        city_code,
        shipper_name: send_name,
        shipper_mobile: send_mobile,
        shipper_province: send_province,
        shipper_city: send_city,
        shipper_district: send_district,
        shipper_address: send_address + send_door,
        shipper_house_number: send_door,
        send_lat: send_latitude,
        send_lng: send_longitude,
        receiver_name: receive_name,
        receiver_phone: receive_mobile,
        receiver_province: receive_province,
        receiver_city: receive_city,
        receiver_district: receive_district,
        receiver_address: receive_address + receive_door,
        receiver_house_number: receive_door,
        receiver_lat: receive_latitude,
        receiver_lon: receive_longitude,
      };
      if (!isFullObject(reqData)) return;
      if (brand == 'dada') {
        reqData = {
          ...reqData,
          cargo_weight: goods_weight,
          cargo_type: goods_name,
          is_use_insurance: oService.proPrice && oService.proPrice > 0 ? 1 : 0,
          cargo_price: oService.decVal || '',
          is_finish_code_needed: oService.finish_code > 0 ? 1 : 0,
        };
        if (!reqData.cargo_price) return;
      } else if (brand == 'ss') {
        reqData = {
          ...reqData,
          cargo_weight: goods_weight,
          cargo_type: goods_name,
          publish_start_time: reserve_start_time,
          publish_end_time: reserve_end_time,
          privacyNumber: oService.privacyNumber > 0 ? 1 : 0,
        };
      } else if (brand == 'huolala') {
        reqData = {
          ...reqData,
          order_vehicle_id: car_id,
          city_info_revision: city_revision,
          shipper_city_id: city_code,
          receiver_city_id: shipping_city_id,
          spec_req_arr: extra,
          use_time: reserve_start_time,
        };
      }
      setLoading(true);
      request({
        url: '/g_wkd/v1/rush/Rush/getPrice',
        data: reqData,
        toastLoading: false,
        onThen: ({ data, code }) => {
          setLoading(false);
          if (code == 0) {
            setEstimate(data);
            onChange({ estimate: data });
          }
        },
      });
    },
    800,
    {
      leading: false,
      trailing: true,
    },
  );

  const onJumpToRules = () => {
    switch (brand) {
      case 'ss':
        Taro.navigator({
          url: 'https://m.kuaidihelp.com/help/ss_rules.html',
          target: 'webview',
        });
        break;
      case 'dada':
        Taro.navigator({
          url: 'order/delivery/rules',
          options: {
            brand,
          },
        });
        break;
      case 'huolala':
        Taro.navigator({
          url: 'order/delivery/rules',
          options: {
            brand,
            city_code,
            car_id,
            car_name,
          },
        });
        break;
    }
  };

  const triggerUpdate = ({ estimate }) => {
    setEstimate(estimate);
    onChange({ estimate });
  };

  return (
    <Fragment>
      <View
        className='at-row at-row__justify--between at-row__align--center kb-size__sm kb-color__grey'
        onClick={() => setIsOpend(!isOpend)}
        hoverClass='kb-hover-opacity'
      >
        <View className='at-col'>
          <View className='at-row kb-margin-sm-b'>
            <View className='kb-spacing-md-r'>
              <Text className='kb-color__black'>预估运费：</Text>
              {loading ? (
                <AtIcon
                  prefixClass='kb-icon'
                  value='loading'
                  className='kb-icon-size__base kb-color__grey'
                />
              ) : (
                <Text className='kb-color__red'>
                  {estimate.real_price > 0 ? `￥${estimate.real_price}` : '--'}
                </Text>
              )}
            </View>
          </View>
          {brand !== 'huolala' && (
            <View className='kb-color__grey kb-size__sm kb-spacing-xs-t'>
              按1kg预估，填写物品及重量显示具体费用
            </View>
          )}
        </View>
        {estimate.real_price > 0 && <View className='kb-spacing-xs'>明细</View>}
      </View>

      <AtFloatLayout isOpened={isOpend} onClose={() => setIsOpend(false)}>
        <View className='kb-fee-detail'>
          <View className='kb-fee-detail__content'>
            <View className='at-row at-row__justify--between at-row__align--center kb-spacing-md-b'>
              <View className='kb-color__brand' onClick={onJumpToRules}>
                计价规则&gt;
              </View>
              <View onClick={() => setIsOpend(false)}>收起</View>
            </View>
            <View className='kb-fee-detail__list'>
              {estimate.detail &&
                estimate.detail.map((item) => (
                  <View className='kb-fee-detail__list--item' key={item.name}>
                    <View className='kb-color__grey'>{item.name}</View>
                    <View>￥{item.price}</View>
                  </View>
                ))}
            </View>
            <View className='kb-fee-detail__total'>
              <View>预估费用：</View>
              <View className='kb-color__red'>
                {estimate.real_price > 0 ? '￥' + estimate.real_price : '--'}
              </View>
            </View>
          </View>
        </View>
      </AtFloatLayout>
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  extraInfo: {},
};

export default Index;
