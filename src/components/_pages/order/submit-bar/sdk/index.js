/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const preferenceMap = {
  submit: "提交订单",
  pprint: "提交且预打印",
  print: "提交直接打印",
  mprint: "提交一单多打"
};

// 检查是否可以触发订阅
export function checkCanSubmit({ total, action }) {
  // 非一单多打，或者订单数量等于1都可以发起订阅
  // 目前错误原因只有一种情况：批量下单点击了一单多打
  if (action !== "mprint" || total === 1) {
    return null;
  }
  return {
    code: 103,
    msg: "批量下单不可一单多打，请重新选择提交方式"
  };
}

// 订单提交偏好设置
export function getOrderPreference() {
  return [
    {
      key: "submit",
      text: preferenceMap.submit
    },
    {
      key: "pprint",
      text: [
        "提交且预打印",
        { text: "(下单即出运单号)", className: "kb-color__grey kb-size__base" }
      ]
    },
    {
      key: "print",
      text: [
        preferenceMap.print,
        {
          text: "(下单直接打印面单)",
          className: "kb-color__grey kb-size__base"
        }
      ]
    },
    {
      key: "mprint",
      text: [
        preferenceMap.mprint,
        {
          text: "(同一地址信息打印多单)",
          className: "kb-color__grey kb-size__base"
        }
      ]
    }
  ];
}

// 偏好缓存key
export const submitBarStorageKey = "submit-bar";
