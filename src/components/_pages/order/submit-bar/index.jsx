/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { checkIsBrand } from '@/components/_pages/store-card/_utils';
import KbSubscribe from '@base/components/subscribe';
import { getStorage, noop, setStorage } from '@base/utils/utils';
import { View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { useCallback, useEffect, useState } from '@tarojs/taro';
import './index.scss';
import { checkCanSubmit, getOrderPreference, preferenceMap, submitBarStorageKey } from './sdk';

let submitBarStorageData = null;
const Index = (props) => {
  const { set, onClick, total, onCheck, onChange, relation, btnText, ...rest } = props;
  const [action, updateAction] = useState('submit');
  const [label, updateLabel] = useState('提交订单');
  const items = getOrderPreference();
  const mode = checkIsBrand(relation, 'sto') ? 'sto' : 'default';
  // 触发变更
  function triggerChange(key) {
    const text = preferenceMap[key];
    if (text) {
      updateLabel(text);
      updateAction(key);
      onChange(key);
    }
    submitBarStorageData = key;
  }

  useEffect(() => {
    // 获取本地存储的
    if (!set) return;
    if (submitBarStorageData) {
      triggerChange(submitBarStorageData);
      return;
    }
    getStorage({
      key: submitBarStorageKey,
    })
      .then((res) => {
        const { data: { data } = {} } = res || {};
        triggerChange(data);
      })
      .catch((err) => console.log(err));
  }, [set]);

  // 弹出偏好
  const handleClick = useCallback(() => {
    Taro.kbActionSheet({
      items,
      direction: 'row',
      onClick: (_, item) => {
        const { key } = item;
        const checkResult = checkCanSubmit({ total, action: key });
        if (checkResult) {
          Taro.kbToast({
            text: checkResult.msg,
          });
          return;
        }
        triggerChange(key);
        setStorage({
          key: submitBarStorageKey,
          data: key,
        });
      },
    });
  }, [total]);

  // 检查
  const handleCheck = useCallback(
    () => checkCanSubmit({ action, total }) || props.onCheck(),
    [total, action],
  );

  // 订阅
  const handleSubscribe = useCallback(() => {
    onClick({
      action,
    });
  }, [total, action, onClick]);

  let subScribeAction = 'waitPaid';
  if (process.env.MODE_ENV === 'yz') {
    mode === 'sto' && (subScribeAction = 'stoWaitPaid');
  }
  if (process.env.MODE_ENV === 'wkd') {
    // eslint-disable-next-line
    const { relationInfo = {} } = useSelector((state) => state.global);
    if (relationInfo.type === 'brand') {
      subScribeAction = 'sendToBrand';
    }
  }

  return (
    <View className='at-row at-row__align--center'>
      {set && <View onClick={handleClick} className='kb-submit-preference' hoverClass='kb-hover' />}
      <View>
        <KbSubscribe
          action={subScribeAction}
          type='primary'
          circle
          size='small'
          onCheck={handleCheck}
          onSubscribe={handleSubscribe}
          {...rest}
        >
          {btnText || label}
        </KbSubscribe>
      </View>
    </View>
  );
};

Index.defaultProps = {
  onClick: noop,
  onCheck: noop,
  set: false, // 是否可设置提交方式
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
