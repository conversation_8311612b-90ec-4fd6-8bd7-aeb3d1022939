/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbListBox from '@/components/_pages/order/listbox';
import { freshList } from '@/components/_pages/order/_utils';
import KbEmpty from '@base/components/empty';
import KbLoader from '@base/components/loader';
import request from '@base/utils/request';
import { getStorage, noop, setStorage } from '@base/utils/utils';
import { View } from '@tarojs/components';
import Taro, { Fragment, useEffect, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isFunction from 'lodash/isFunction';
import { AtButton, AtFloatLayout, AtInput } from 'taro-ui';
import './index.scss';

const package_info_max = 4;
const packageInfoKey = 'packageInfo';
const manual = '手动输入';

function Index(props) {
  const { actionRef, current, isFresh, locked, isRemark, curRemark, mode, brand, onChange } = props;

  let custom = 0;

  const [isOpened, setIsOpen] = useState();
  const [selectted, setSelectted] = useState();
  const [package_info_value, setPackage_info_value] = useState('');
  const [packages, setPackages] = useState([]);
  const [remark, setRemark] = useState();
  const [loading, setLoading] = useState(false);

  if (actionRef) {
    actionRef.current = {
      open: () => {
        onPackageInfoAction('open');
      },
    };
  }

  useEffect(() => {
    getPackageInfo();
  }, [mode, brand]);

  // 拉取远程设定的物品类型列表
  const requestGoodsList = () => {
    return new Promise((resolve, reject) => {
      let url = '/v1/GrabOrder/packageInfo',
        reqData = {},
        GoodsGlobalDataKey = packageInfoKey,
        list = [];
      if (mode == 'tcjs') {
        url = '/g_wkd/v1/rush/Rush/getCargoTypes';
        reqData = {
          brand,
        };
        GoodsGlobalDataKey = `${packageInfoKey}_delivery_${brand}`;
      }
      list = Taro.kbGetGlobalData(GoodsGlobalDataKey);
      if (list && list.length > 0) {
        resolve(list);
        return;
      }
      request({
        url,
        toastLoading: false,
        data: reqData,
        onThen: ({ code, data }) => {
          if (code == 0 && data) {
            if (mode == 'tcjs') {
              custom = data.custom;
              list = data.list;
            } else {
              list = data;
            }
            resolve(list);
            Taro.kbSetGlobalData(GoodsGlobalDataKey, list);
          } else {
            reject();
          }
        },
      });
    });
  };

  const getPackageInfo = () => {
    setLoading(true);
    const data = ['日用品', '数码产品', '衣物', '食物', '文件', '鞋靴'];
    getPackageInfoStorage((local) => {
      requestGoodsList()
        .then((list) => {
          let delArr = ['其它', '其他'];
          let Goods = list.length > 0 ? list.filter((item) => !delArr.includes(item)) : data;
          Goods = Goods.concat(local).map((item, index) => {
            return {
              label: item,
              key: `i_${index}`,
            };
          });
          if (mode == 'kd' || (mode == 'tcjs' && custom > 0)) {
            Goods.push({
              key: 'other',
              label: manual,
            });
          }
          setLoading(false);
          setPackages(Goods);
        })
        .catch(() => {});
    });
  };

  // 获取本地物品缓存
  const getPackageInfoStorage = (then) => {
    getStorage({
      key: packageInfoKey,
      complete: (res) => {
        const { data } = res.data || {};
        then(isArray(data) ? data : []);
      },
    }).catch(() => {});
  };

  // 设置本地物品缓存
  const setPackageInfoStorage = (value) => {
    if (!value || packages.findIndex((item) => item.label === value) >= 0) {
      return;
    }
    getPackageInfoStorage((list) => {
      list.unshift(value);
      setStorage({
        key: packageInfoKey,
        data: list.slice(0, 6),
      });
    });
  };

  // 选择物品类型、或者快递品牌
  const onPackageInfoAction = (key, value) => {
    switch (key) {
      case 'disabled':
        return;
      case 'open':
        // 开启获取物品类型
        if (locked) return;
        setSelectted(current);
        isRemark && setRemark(curRemark);
        switchFloatLayoutStatus(true, getPackageInfo);
        break;
      case 'input':
        // 自定义物品类型
        setPackage_info_value(value);
        break;
      case 'select':
        // 勾选物品类型
        const { label } = value;
        if (selectted === label) return;
        setSelectted(label);
        if (label !== manual && !isRemark) {
          onPackageInfoAction('confirm', label);
        }
        break;
      case 'cancel':
        // 取消物品选择
        onClose();
        break;
      case 'confirm':
        // 确认物品选择
        if (selectted === manual) {
          if (package_info_value.length > package_info_max) {
            value = `${package_info_value}`.substring(0, package_info_max);
            Taro.kbToast({
              text: `物品信息最多${package_info_max}个字`,
            });
          } else {
            value = package_info_value;
          }
          // 缓存
          setPackageInfoStorage(value);
        }
        if (!value) {
          return;
        }
        setSelectted(value);
        switchFloatLayoutStatus(false, () => {
          let cdata = {
            goods_name: value,
          };
          if (isRemark) {
            cdata.goods_remark = remark;
          }
          onChange(cdata);
        });
        break;
      case 'remark':
        setRemark(value);
      default:
        break;
    }
  };

  // 切换物品类型弹窗状态
  const switchFloatLayoutStatus = (isOpened, then) => {
    setIsOpen(isOpened);
    isFunction(then) && then();
  };

  // 关闭物品类型弹窗
  const onClose = () => switchFloatLayoutStatus(false);

  //处理物品选择
  const handleSelectChange = (item) => {
    onPackageInfoAction('select', item);
  };

  const hasList = !!packages.length;
  return (
    <View className='kb-order-goods'>
      <AtFloatLayout isOpened={isOpened} onClose={onClose}>
        {!!isOpened && (
          <Fragment>
            <View className='kb-float-layout__bars'>
              <View
                className='layout-bars__cancel'
                hoverClass='kb-hover'
                onClick={() => {
                  onPackageInfoAction('cancel');
                }}
              >
                取消
              </View>
              <View className='layout-bars__title'>{isRemark ? '物品信息' : '物品类型'}</View>
              {!isRemark && (
                <View
                  className='layout-bars__confirm'
                  hoverClass='kb-hover'
                  onClick={() => onPackageInfoAction('confirm', selectted)}
                >
                  确定
                </View>
              )}
            </View>
            {hasList ? (
              <View className='kb-package'>
                <View className='kb-package-warp'>
                  {isRemark && <View className='kb-spacing-md'>物品类型</View>}
                  {isFresh && <View className='kb-package-title'>普通包裹</View>}
                  <KbListBox list={packages} onChange={handleSelectChange} selectted={selectted} />
                  {isFresh && (
                    <Fragment>
                      <View className='kb-package-title'>生鲜包裹</View>
                      <KbListBox
                        list={freshList}
                        onChange={handleSelectChange}
                        selectted={selectted}
                      />
                    </Fragment>
                  )}
                  {manual === selectted && (
                    <View className='kb-package-input'>
                      <AtInput
                        placeholderClass='placeholder'
                        focus
                        value={package_info_value}
                        cursor={-1}
                        cursorSpacing={20}
                        border={false}
                        maxLength={10}
                        placeholder={`请输入物品信息（最多${package_info_max}个字）`}
                        className='kb-input--without-background'
                        onChange={(e) => onPackageInfoAction('input', e)}
                        // onBlur={e => onPackageInfoAction("confirm", e)}
                      />
                    </View>
                  )}
                </View>
                {isRemark && (
                  <Fragment>
                    <View className='kb-package-remark'>
                      <View>备注内容</View>
                      <View className='kb-package-input'>
                        <AtInput
                          placeholderClass='placeholder'
                          cursor={-1}
                          cursorSpacing={20}
                          placeholder='选填，不超过20字'
                          maxLength={20}
                          value={remark}
                          onChange={(e) => onPackageInfoAction('remark', e)}
                        />
                      </View>
                    </View>
                    <View className='kb-spacing-md-lr'>
                      <AtButton
                        className='kb-package-confirm__btn'
                        type='primary'
                        circle
                        onClick={() => onPackageInfoAction('confirm', selectted)}
                      >
                        确定
                      </AtButton>
                    </View>
                  </Fragment>
                )}
              </View>
            ) : loading ? (
              <KbLoader />
            ) : (
              <View className='kb-extra-info__empty'>
                <KbEmpty size='small' description='暂无数据' />
              </View>
            )}
          </Fragment>
        )}
      </AtFloatLayout>
    </View>
  );
}

Index.defaultProps = {
  actionRef: null,
  current: '', //默认物品类型
  isFresh: false, // 是否开启生鲜包裹
  locked: false, // 锁定物品类型
  curRemark: '', // 默认备注
  isRemark: false, //是否可以填写备注
  mode: 'kd', //默认快递类型，还可以有同城急送模式tcjs
  brand: '',
  onChange: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
