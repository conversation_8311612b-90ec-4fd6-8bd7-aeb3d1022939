/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from "@tarojs/components";
import Taro, { Fragment } from "@tarojs/taro";

const Index = props => {
  const { type, orderInfo } = props;
  const { relationInfo, deposit, pay_method } = orderInfo;
  const { relation_id } = relationInfo || {};
  const handleToWallet = () => {
    Taro.navigator({
      url: "user/wallet"
    });
  };
  const wrapperCls = "kb-spacing-sm kb-text__left ";
  const itemCls =
    "kb-color__block kb-size__bold kb-spacing-sm-tb kb-size__base";
  const titleCls = "kb-spacing-xs-tb kb-text__left kb-color__red";
  return relation_id == "brand:sto" ? (
    deposit ? (
      <Fragment>
        {type === "result" ? (
          <View className={wrapperCls}>
            <View className={titleCls}>温馨提示:</View>
            <View className="kb-spacing-sm-tb">
              1、
              {pay_method != "alipay"
                ? "系统自动从您的钱包中扣除0.01元保证金、"
                : ""}
              订单成功签收后将有
              <Text className="kb-color__red">0.5元现金补贴</Text>
              发放至您的钱包
            </View>
            <View className="kb-spacing-sm-tb">
              2、若订单取消，将无法获得0.5元现金补贴，您支付的0.01元退还到您的钱包
            </View>
            <View className="kb-spacing-sm-tb">
              3、您的订单已推送给申通快递，请耐心等待快递员与您联系，
              <Text className="kb-color__red">您需线下支付运费给快递员.</Text>
            </View>
          </View>
        ) : orderInfo.status == "已签收" ? (
          <View className={itemCls}>
            <Text className="kb-color__red">0.5元现金补贴</Text>
            已发放至您的钱包，
            <Text className="kb-decollator__bottom" onClick={handleToWallet}>
              去查看
            </Text>
          </View>
        ) : orderInfo.status !== "已取消" ? (
          <View className={itemCls}>
            订单成功签收后将有
            <Text className="kb-color__red">0.5元现金补贴</Text>
            发放至您的钱包
          </View>
        ) : (
          <Text className={itemCls}>0.01元保证金将退回到您的钱包</Text>
        )}
      </Fragment>
    ) : type === "result" ? (
      <Fragment>
        <View className={wrapperCls}>
          <View className={titleCls}>温馨提示:</View>
          <View className="kb-list--wrapper">
            您的订单已推送给申通快递，请耐心等待快递员与您联系，
            <Text className="kb-color__red">您需线下支付运费给快递员.</Text>
          </View>
        </View>
      </Fragment>
    ) : null
  ) : null;
};
Index.options = {
  addGlobalClass: true
};
Index.defaultProps = {
  type: "result",
  orderInfo: {}
};
export default Index;
