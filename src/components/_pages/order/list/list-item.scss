/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-order {
  &__item {
    &-tcjs {
      padding: $spacing-v-md 0;

      & .kb-list__item {
        margin-bottom: $spacing-v-md;

        &:last-child {
          margin-bottom: 0;
        }
      }

      & .item-icon,
      & .item-content {
        padding-top: 0;
        padding-bottom: 0;
      }

      &__logo--text {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 60rpx;
        height: 60rpx;
        color: $color-white;
        font-size: 16rpx;
        text-align: center;
        background: $color-brand;
        border-radius: 50%;
      }
    }
  }

  &__icon {
    position: relative;

    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      color: $color-white;
      font-size: $font-size-xs/1.2;
      transform: translate(-50%, -60%);
    }

    &-send {
      &::before {
        content: '发';
      }
    }

    &-receive {
      &::before {
        content: '收';
      }
    }
  }

  &__spacing {
    margin-top: $spacing-v-sm;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  &__info {
    text-align: center;

    &--city,
    &--name {
      width: 180px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &--city {
      font-weight: bold;
    }

    &--name {
      color: $color-grey-2;
      font-size: $font-size-base;
    }
  }

  &__sign {
    position: relative;
    flex-grow: 1;
    margin: 0 $spacing-v-md;
    text-align: center;
    border-bottom: $width-2PX solid $color-grey-3;

    &--text {
      display: inline-block;
      max-width: 270px;
      overflow: hidden;
      color: $color-brand;
      font-weight: bold;
      font-size: $font-size-base;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    &::after {
      position: absolute;
      right: -10px;
      bottom: -1 * $width-2PX;
      width: 0;
      height: 0;
      border-color: $color-grey-3 transparent;
      border-style: solid;
      border-width: 0 14px 14px 0;
      content: '';
    }
  }

  &__fix {
    &--l {
      margin-left: -10px;
    }
  }

  &__brand {
    // font-weight: bold;
    border-bottom: $border-lightest;

    .item {
      &-icon,
      &-content {
        padding: $spacing-v-md $spacing-h-md $spacing-v-md 0px;
      }
    }
  }
}

.kb-tool-bar-left {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  &__kdg {
    margin-left: $spacing-h-lg;
    color: $color-brand;
  }
}

.kb-size__32 {
  font-size: 32px;
}
