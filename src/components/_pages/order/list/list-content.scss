/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.order-list {
    padding: 0 $spacing-h-md;
    padding-bottom: $spacing-v-md;

    &__wrapper {
        height: 100%;
        position: relative;
        padding-top: 100px;
        box-sizing: border-box;

        .order-list-date {
            position: absolute;
            left: $spacing-h-md * 1.5;
            top: 0;
            height: 100px;
        }
    }

    &__item {
        margin-bottom: $spacing-v-md;

        .order-bars {
            padding-right: $spacing-h-md;
            padding-bottom: $spacing-h-md;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}