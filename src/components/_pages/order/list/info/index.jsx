import { View } from '@tarojs/components';
import classNames from 'classnames';
import RentalTruckCard from '~/components/_pages/truck/choose/rental/list/card';
import TruckCardType from '~/components/_pages/truck/list/item/card/type';
import { useOrderOperator } from '../bars/_utils';
import OrderStatus from '../status';
import './index.scss';

const OrderInfo = (props) => {
  const { data, border = true, className, onSuccess, isRental } = props;

  const rootCls = classNames('order-info kb-navigator kb-navigator-ghost', className, {
    'kb-navigator-noborder': !border,
  });

  const { run } = useOrderOperator({
    onSuccess,
  });

  const handleToDetail = () => run('detail', data);

  return (
    <View>
      <View className={rootCls} hoverClass='kb-hover' onClick={handleToDetail}>
        <View className='kb-navigator__content at-row at-row__align--center'>
          <View>订单号: {data.order_id}</View>
        </View>
        <View className='kb-navigator__extra'>
          <OrderStatus data={data} strong payStatus />
        </View>
      </View>
      <View className='kb-spacing-md'>
        {isRental ? (
          <RentalTruckCard data={data} className='kb-spacing-none' source='orderList' />
        ) : (
          <TruckCardType data={data} />
        )}
      </View>
    </View>
  );
};

export default OrderInfo;
