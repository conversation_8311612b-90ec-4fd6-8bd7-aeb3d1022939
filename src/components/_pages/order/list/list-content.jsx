/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useMemo, useState } from 'react';
import { createListApiFormatters } from '~/components/_pages/index/server/order/utils';
import { orderListApi } from '~/services/order';
import KbLongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import { useDidShowCom } from '~base/hooks/page';
import OrderListCard from './card';
import './list-content.scss';
import OrderListDate from './list-date';
import { countDateRange } from './list-date/range/_utils';
import { orderListFormatRequest } from './_utils';

const OrderListContent = (props) => {
  const { active, type, actionRef } = props;
  // 组合自定义formatRequest
  const { formatResponse, formatRequest } = createListApiFormatters({
    requestFormatter: orderListFormatRequest,
    patchKeys: ['last_cargo_id', 'last_lease_id'],
  });
  const { list, config } = useLongList(orderListApi, {
    api: { data: { type }, formatRequest, formatResponse },
  });
  const [date, setDate] = useState(countDateRange(1));

  // 日期变更
  const handleDateChange = (v) => setDate(v);

  // 补充日期
  const activeMerged = useMemo(() => {
    if (!active) return active;
    return {
      ...active,
      date,
    };
  }, [date, active]);

  useDidShowCom(() => {
    if (active) {
      config?.loader();
    }
  });

  return (
    <View className='order-list__wrapper'>
      <OrderListDate actionRef={actionRef} value={date} onChange={handleDateChange} />
      <KbLongList active={activeMerged} data={config} enableMore>
        <View className='order-list'>
          {list.map((item) => (
            <View className='order-list__item kb-box' key={item.id}>
              <OrderListCard item={item} config={config} />
            </View>
          ))}
        </View>
      </KbLongList>
    </View>
  );
};

export default OrderListContent;
