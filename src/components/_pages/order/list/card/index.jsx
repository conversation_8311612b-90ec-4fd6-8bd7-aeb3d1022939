/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import { dateCalendar } from '~base/utils/utils';
import OrderBars from '../bars';
import OrderInfo from '../info';
import OrderTruckLine from '../line';
import RentalOrderBars from './rental/bars';
import RentalOrderSettle from './rental/settle';
import { checkOrderIsRental } from './rental/bars/utils';

const OrderListCard = ({ item, config }) => {
  const isRental = checkOrderIsRental(item);

  return (
    <>
      <OrderInfo isRental={isRental} data={item} onSuccess={config.loader} />
      {isRental ? (
        <>
          <RentalOrderBars
            data={item}
            onRefresh={config.loader}
            onSuccess={config.triggerRefresher}
            onOperate={config.recordRefresher}
          />
          {item.status == 7 && <RentalOrderSettle data={item} />}
        </>
      ) : (
        <>
          <OrderTruckLine data={item} />
          <View className='kb-size__base2 kb-spacing-md-lr'>
            用车时间
            <Text className='kb-color__brand kb-spacing-md-l'>
              {dateCalendar(item?.create_at, { timer: true })}
            </Text>
          </View>
          <OrderBars
            data={item}
            onSuccess={config.triggerRefresher}
            onOperate={config.recordRefresher}
          />
        </>
      )}
    </>
  );
};

OrderListCard.options = {
  addGlobalClass: true,
};

export default OrderListCard;
