/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useMemo } from "react";

export const useRentalOrderSettle = (props) => {
  const { data } = props;

  const list = useMemo(() => {
    return [
      {
        label: '车主结算结果',
        key: 'a',
        value: '',
        className: 'kb-color__black'
      },
      {
        label: '车辆定损费用',
        key: 'b',
        value: '￥100.00',
      },
      {
        label: '租赁费用退款',
        key: 'c',
        value: '￥100.00',
      },
      {
        label: '租赁押金退款',
        key: 'd',
        value: '￥100.00',
      },
      {
        label: '可退款',
        key: 'e',
        value: '￥100.00',
        className: 'kb-color__black'
      },
    ];
  }, [data]);

  return {
    list,
  };
};
