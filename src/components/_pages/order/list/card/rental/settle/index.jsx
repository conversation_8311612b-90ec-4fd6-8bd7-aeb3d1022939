/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useRentalOrderSettle } from './utils';
import './index.scss';

const RentalOrderSettle = (props) => {
  const { list } = useRentalOrderSettle(props);
  return (
    <View className='rental-order-settle'>
      {list.map((item, index) => (
        <View
          key={item.key}
          className={classNames(
            'at-row at-row__align--center at-row__justify--between',
            {
              'kb-spacing-md-t': index != 0,
              'kb-color__greyer': !item.className,
              [item.className]: !!item.className,
            },
          )}
        >
          <View>{item.label}</View>
          <View>{item.value}</View>
        </View>
      ))}
    </View>
  );
};

RentalOrderSettle.options = {
  addGlobalClass: true,
};

export default RentalOrderSettle;
