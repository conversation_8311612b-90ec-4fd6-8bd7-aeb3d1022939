/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useRef, useState } from 'react';
import { AtTextarea } from 'taro-ui';
import FloatLayoutExtend from '../float-layout';
import './index.scss';

const MoreOperate = (props) => {
  const { trigger, data } = props;
  const [value, setValue] = useState([]);
  const [current, setCurrent] = useState(0);
  const ref = useRef({});

  const bars = [
    {
      label: '不同意结算结果',
      key: '0',
      title: '提交后等待车主重新结算',
      placeholder: '请输入不同意结算结果原因',
    },
    {
      label: '申请平台介入',
      key: '1',
      placeholder: '请输入申请理由',
    },
  ];

  const dom = React.cloneElement(trigger, {
    onClick: () => {
      Taro.kbActionSheet({
        items: bars,
        onClick: (index) => {
          setValue('');
          setCurrent(index);
          ref.current.setIsOpened(true);
        },
      });
    },
  });

  const onConfirm = () => {
    console.log(current, value);
    return true;
  };

  return (
    <>
      {dom}
      <FloatLayoutExtend
        actionRef={ref}
        title='提前还车'
        onConfirm={onConfirm}
        cancelText='取消'
        confirmText='提交'
      >
        <View className='kb-spacing-md'>
          {bars[current].title && (
            <View className='kb-spacing-md-tb kb-color__greyer kb-size__sm'>
              {bars[current].title}
            </View>
          )}
          <AtTextarea
            className='kb-more-textarea'
            value={value}
            onChange={(v) => setValue(v)}
            placeholder={bars[current].placeholder}
            maxLength={100}
            count
            focus
          />
        </View>
      </FloatLayoutExtend>
    </>
  );
};

MoreOperate.options = {
  addGlobalClass: true,
};

export default MoreOperate;
