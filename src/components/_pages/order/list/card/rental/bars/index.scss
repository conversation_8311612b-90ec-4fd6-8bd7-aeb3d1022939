/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
.kb-rental-order {
  padding-bottom: $spacing-h-md;
  color: $color-black-1;

  &-bars {
    flex: 1;
    flex-wrap: wrap;
    justify-content: flex-end;
    width: unset;
  }

  &-btn {
    height: 56px;
    margin: 0;
    margin-left: $spacing-h-md;
    padding: 0 40px;
    font-size: $font-size-base2;
    line-height: 56px;

    &.at-button--secondary {
      color: #969799;
      border-color: #969799;
    }
  }
  .kb-rental-bars-wait-confirm {
    color: $color-brand !important;
    background: rgba(15, 199, 205, 0.05) !important;
  }
}
