/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useState } from 'react';
import { AtNoticebar } from 'taro-ui';
import RentalTruckInfo from '~/components/_pages/truck/choose/rental/list/info';
import FloatLayoutExtend from '../float-layout';
import TruckImg from '../truck-img';
import './index.scss';

const ReturnTruck = (props) => {
  const { trigger, data, isEarly } = props;
  const [value, setValue] = useState([]);
  const onConfirm = () => {
    return true;
  };
  return (
    <FloatLayoutExtend
      trigger={trigger}
      title={isEarly ? '提前还车' : '还车'}
      renderFooter={
        <AtNoticebar icon='amaze'>发车前，请确保车辆电量充足并做好安全检查！</AtNoticebar>
      }
      onConfirm={onConfirm}
    >
      <View className='kb-spacing-md'>
        <RentalTruckInfo data={data} isReturn isEarly={isEarly} />
        <TruckImg value={value} onChange={setValue} />
      </View>
    </FloatLayoutExtend>
  );
};

ReturnTruck.options = {
  addGlobalClass: true,
};

export default ReturnTruck;
