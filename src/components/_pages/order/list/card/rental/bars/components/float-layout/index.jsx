/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { RootPortal, ScrollView, View } from '@tarojs/components';
import React, { isValidElement, useEffect, useState } from 'react';
import { AtButton, AtFloatLayout } from 'taro-ui';
import './index.scss';

/**
 * @component
 * @param {Object} props - 组件属性
 * @param {React.ReactElement} props.trigger
 * @param {string} props.title
 * @param {string} props.cancelText
 * @param {string} props.confirmText
 * @param {Object} props.actionRef
 * @param {React.ReactNode} props.renderFooter
 * @param {Function} [props.onConfirm]
 * @param {Function} [props.onCancel]
 * @param {React.ReactNode} props.children

 */
const FloatLayoutExtend = (props) => {
  const {
    trigger,
    title,
    actionRef,
    cancelText = '再想想',
    confirmText = '确定',
    onConfirm = () => Promise.resolve(true),
    onCancel = () => Promise.resolve(true),
    renderFooter,
  } = props;

  const [isOpened, setIsOpened] = useState(false);

  const dom = isValidElement(trigger)
    ? React.cloneElement(trigger, {
        onClick: () => {
          setIsOpened(true);
        },
      })
    : null;

  const handleClose = async () => {
    const isSuccess = await onCancel();
    if (isSuccess) {
      setIsOpened(false);
    }
  };

  const handleConfirm = async () => {
    const isSuccess = await onConfirm();
    if (isSuccess) {
      setIsOpened(false);
    }
  };

  useEffect(() => {
    if (actionRef?.current) {
      actionRef.current = {
        setIsOpened,
      };
    }
  }, [actionRef]);

  return (
    <>
      <RootPortal>
        <AtFloatLayout isOpened={isOpened} onClose={handleClose} className='float-layout'>
          <View className='float-layout-title'>{title}</View>
          <ScrollView scrollY className='float-layout-scroll'>
            {props.children}
          </ScrollView>
          <View>
            {renderFooter}
            <View className='at-row at-row__align--center at-row__justify--between float-layout-footer'>
              <AtButton
                className='float-layout-button float-layout-button-cancel'
                circle
                onClick={handleClose}
              >
                {cancelText}
              </AtButton>
              <AtButton
                className='float-layout-button'
                circle
                type='secondary'
                onClick={handleConfirm}
              >
                {confirmText}
              </AtButton>
            </View>
          </View>
        </AtFloatLayout>
      </RootPortal>
      {dom}
    </>
  );
};

FloatLayoutExtend.options = {
  addGlobalClass: true,
};

export default FloatLayoutExtend;
