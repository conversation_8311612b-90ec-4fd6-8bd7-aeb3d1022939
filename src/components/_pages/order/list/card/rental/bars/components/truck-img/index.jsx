/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import KbImagePicker from '~base/components/image-picker';
import './index.scss';

/**
 * @param {Object} props - 组件属性
 * @param {string} props.className
 * @param {Array} props.value 图片列表
 * @param {string} props.icon 图标
 * @param {string} props.source 来源
 * @param {Function} props.onChange
 */
const TruckImg = (props) => {
  const { className, value, onChange, icon = 'add', source = 'order' } = props;

  const api = {
    url: '/g_autovd/v2/Cargo/LeaseOrder/uploadReceiptImg',
  };

  const handleImagePickerChange = (list, action) => {
    if (action == 'upload') {
      let _value = [...list];
      if (_value.length > 9) {
        Taro.kbToast({
          text: '最多上传9张',
        });
        _value = _value.slice(0, 9);
      }
      onChange(_value);
    } else if (action == 'delete') {
      onChange(list);
    }
  };

  return (
    <View className={classNames('truck-img', className)}>
      <View>
        <KbImagePicker
          api={api}
          maxSize={1 * 1024 * 1024}
          compressedWidth={1000}
          cropScale='1:1'
          custom
          files={value}
          count={9}
          onChange={handleImagePickerChange}
        >
          <View className='truck-img-picker at-row at-row__direction--column at-row__align--center at-row__justify--center'>
            <AtIcon value={icon} prefixClass={icon ? 'kb-icon' : ''} />
            <View className='kb-spacing-sm-t'>车身照片</View>
          </View>
        </KbImagePicker>
      </View>
      {source != 'settle' && (
        <View className='at-row at-row__align--start kb-spacing-md-tb'>
          <AtIcon
            prefixClass='kb-icon'
            value='info-circle'
            className='kb-size__sm kb-color__greyer'
          />
          <View className='kb-size__sm kb-color__greyer kb-margin-sm-l'>
            建议完整拍摄车辆内外部的照片，可作为回车结算和订单纠纷的依据
          </View>
        </View>
      )}
    </View>
  );
};

TruckImg.options = {
  addGlobalClass: true,
};

export default TruckImg;
