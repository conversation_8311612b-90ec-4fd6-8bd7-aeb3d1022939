/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useMemo } from 'react';
import { useUserType } from '~/components/_pages/userTypeLayout/_utils';
import { handlePayRentOrder } from '~/services/rental';
import { requestPayment } from '~/utils/qy';
import CancelOperate from './components/cancel';
import ConfirmOperate from './components/confirm';
import MoreOperate from './components/more';
import ReturnTruck from './components/return-truck';
import SendOperate from './components/send';

export const useRentalOrderBars = (props) => {
  const { data, onSuccess, onOperate, onRefresh } = props;
  const { isClient } = useUserType();

  const bars = useMemo(() => {
    return (
      isClient
        ? [
            {
              label: '取消',
              key: 'cancel',
              type: 'secondary',
              // confirm: true,
              show: checkRentalOrderCanCancel(data),
              render: <CancelOperate />,
            },
            {
              label: '付款',
              key: 'pay',
              type: 'primary',
              show: checkRentalOrderCanPay(data),
            },
            {
              label: '收车确认',
              key: 'confirm',
              type: 'primary',
              show: checkRentalOrderCanConfirm(data),
              render: <ConfirmOperate />,
            },
            {
              label: '提前还车',
              key: 'returnEarly',
              type: 'primary',
              show: checkRentalOrderCanReturnEarly(data),
              render: <ReturnTruck isEarly />,
            },
            {
              label: '还车',
              key: 'return',
              type: 'secondary',
              show: checkRentalOrderCanReturn(data),
              render: <ReturnTruck />,
            },
            {
              label: '续租',
              key: 'renew',
              type: 'primary',
              show: checkRentalOrderCanRenew(data),
            },
            {
              label: '更多',
              key: 'more',
              type: 'secondary',
              show: checkRentalOrderCanMore(data),
              render: <MoreOperate />,
            },
            {
              label: '同意结算',
              key: 'settle',
              type: 'primary',
              show: checkRentalOrderCanSettle(data),
              confirm: {
                title: '发车提醒',
                content: '确认后，退款退回至您的微信钱包，本次租赁结束',
              },
            },
          ]
        : [
            {
              label: '取消',
              key: 'cancel',
              type: 'secondary',
              show: checkRentalOrderCanCancel(data),
              render: <CancelOperate />,
            },
            {
              label: '发车',
              key: 'send',
              type: 'primary',
              show: checkRentalOrderCanSend(data),
              render: <SendOperate />,
            },
            {
              label: '已发车，待收车',
              key: 'sent',
              type: 'primary',
              className: 'kb-rental-bars-wait-confirm',
              show: checkRentalOrderSended(data),
            },
          ]
    ).filter(({ show }) => show);
  }, [data, isClient]);

  const run = async (key) => {
    let isSuccess;
    console.log(key);
    switch (key) {
      case 'pay':
        const payParams = await handlePayRentOrder({ order_id: data.order_id });
        await requestPayment(payParams).then(() => {
          isSuccess = true;
        });
        break;
    }
    if (isSuccess) {
      onRefresh();
    }
  };

  return {
    run,
    bars,
  };
};

export const checkOrderIsRental = (data) => {
  return data.order_type == '2';
};

export const checkRentalOrderCanCancel = (data) => {
  return data.status < 3;
};

export const checkRentalOrderCanPay = (data) => {
  return data.status == 0;
};

export const checkRentalOrderCanConfirm = (data) => {
  return data.status == 3;
};

export const checkRentalOrderCanReturnEarly = (data) => {
  return false;
};

export const checkRentalOrderCanReturn = (data) => {
  return false;
};

export const checkRentalOrderCanRenew = (data) => {
  return false;
};

export const checkRentalOrderCanMore = (data) => {
  return false;
};

export const checkRentalOrderCanSettle = (data) => {
  return false;
};

export const checkRentalOrderCanSend = (data) => {
  return data.status == 2;
};

export const checkRentalOrderSended = (data) => {
  return data.status == 3;
};

export const checkRentalOrderIsRentalIng = (data) => {
  return data.status == 4;
};

export const checkIsRentalOrderById = (order_id) => {
  // 长数，最后一位为1的是租车订单
  return order_id.length > 10 && order_id?.slice(-1) == '1';
};
