/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useState } from 'react';
import { handleReceiveRent } from '~/services/rental';
import FloatLayoutExtend from '../float-layout';
import TruckImg from '../truck-img';

const ConfirmOperate = (props) => {
  const { trigger, data, onRefresh } = props;
  const [value, setValue] = useState([]);

  const onConfirm = async () => {
    const isSuccess = await handleReceiveRent({ order_id: data.order_id, images: value });
    if (isSuccess) {
      onRefresh();
    }
    return isSuccess;
  };

  return (
    <FloatLayoutExtend trigger={trigger} title='收车凭证' onConfirm={onConfirm}>
      <TruckImg className='kb-spacing-md' value={value} onChange={setValue} />
    </FloatLayoutExtend>
  );
};

ConfirmOperate.options = {
  addGlobalClass: true,
};

export default ConfirmOperate;
