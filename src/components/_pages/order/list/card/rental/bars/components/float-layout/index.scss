/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.float-layout {
  .at-float-layout__container {
    padding-bottom: 0 !important;
  }
  .layout-body {
    max-height: unset;
    &__content {
      max-height: unset;
    }
  }
  .float-layout-scroll {
    max-height: 65vh;
  }
  &-title {
    padding-top: $spacing-h-md;
    padding-left: $spacing-h-md;
    color: $color-black-0;
    font-size: $font-size-lg;
  }
  &-footer {
    gap: $spacing-h-md;
    padding: $spacing-h-md 42px;
    .at-button {
      flex: 1;
    }
    .float-layout-button {
      box-sizing: border-box;
      height: 80px;
      font-size: $font-size-lg;
      line-height: 80px;
      &-cancel {
        color: #969799;
        border-color: #969799;
      }
    }
  }
}
