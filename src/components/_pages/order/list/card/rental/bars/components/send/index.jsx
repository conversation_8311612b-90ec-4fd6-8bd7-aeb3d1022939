/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useState } from 'react';
// import OperateDepartSelector from '~/components/_pages/order/detail/operate/depart/modal/form/selector';
import { handleDepartRent } from '~/services/rental';
import FloatLayoutExtend from '../float-layout';
import TruckImg from '../truck-img';
import './index.scss';

const SendOperate = (props) => {
  const { trigger, data, onRefresh } = props;
  const [imgs, setImgs] = useState([]);
  // const [stop, setStop] = useState('');

  const onConfirm = async () => {
    const isSuccess = await handleDepartRent({
      order_id: data.order_id,
      depart_certificate_list: imgs,
    });
    if (isSuccess) {
      onRefresh();
    }
    return isSuccess;
  };

  return (
    <FloatLayoutExtend
      trigger={trigger}
      title='出车凭证'
      onConfirm={onConfirm}
      cancelText='取消'
      confirmText='开始发车'
      renderFooter={
        <View className='kb-spacing-md kb-color__brand kb-size__sm'>
          发车前，确保电量充足并做好安全检查
        </View>
      }
    >
      <View className='server-send'>
        <TruckImg
          value={imgs}
          onChange={setImgs}
          className='server-send-truck-img'
          icon='camera-fill'
        />
        {/* <View>
          <OperateDepartSelector value={stop} onChange={setStop} label='发车点' className='stop' />
        </View> */}
      </View>
    </FloatLayoutExtend>
  );
};

SendOperate.options = {
  addGlobalClass: true,
};

export default SendOperate;
