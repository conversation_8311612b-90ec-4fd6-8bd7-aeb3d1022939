/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import { isBoolean } from 'lodash';
import React from 'react';
import { AtButton, AtIcon } from 'taro-ui';
import Popconfirm from '~base/components/popconfirm';
import './index.scss';
import { useRentalOrderBars } from './utils';
import classNames from 'classnames';

const RentalOrderBars = (props) => {
  const { data } = props;
  const { bars, run } = useRentalOrderBars(props);

  // 通用按钮属性
  const getButtonProps = (bar) => ({
    key: bar.key,
    type: bar.type,
    circle: true,
    className: classNames('kb-rental-order-btn', bar.className),
    onClick: () => run(bar.key),
  });

  // 按钮内容渲染
  const renderButtonContent = (bar) => (
    <>
      {bar.icon && <AtIcon prefixClass='kb-icon' value={bar.icon} />}
      {bar.label}
    </>
  );

  // 渲染单个按钮
  const renderButton = (bar) => (
    <AtButton {...getButtonProps(bar)}>{renderButtonContent(bar)}</AtButton>
  );

  // 渲染操作栏项目
  const renderBarItem = (bar) => {
    if (bar.confirm) {
      return (
        <Popconfirm
          key={bar.key}
          buttonProps={{
            circle: true,
            type: bar.type,
            className: classNames('kb-rental-order-btn', bar.className),
          }}
          title={`确定${bar.label}？`}
          onConfirm={() => run(bar.key)}
          {...(isBoolean(bar.confirm) ? {} : bar.confirm)}
        >
          {renderButtonContent(bar)}
        </Popconfirm>
      );
    }

    if (bar.render) {
      return React.cloneElement(bar.render, {
        trigger: renderButton(bar),
        ...props,
      });
    }

    return renderButton(bar);
  };

  return (
    <View className='kb-spacing-md-lr kb-rental-order'>
      <View className='at-row at-row__align--center at-row__justify--between'>
        <View className='kb-size__base2'>
          费用明细：<Text className='kb-text__bold kb-size__xl'>{data.amount}</Text>
        </View>
        <View className='kb-rental-order-bars at-row at-row__align--center at-row__justify--end'>
          {bars.map(renderBarItem)}
        </View>
      </View>
    </View>
  );
};

RentalOrderBars.options = {
  addGlobalClass: true,
};

export default RentalOrderBars;
