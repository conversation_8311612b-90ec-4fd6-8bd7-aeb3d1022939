import { useMemo, useState } from 'react';

export function useCreateTabs() {
  const [current, setCurrent] = useState(0);
  const tabs = useMemo(() => {
    // 订单状态。为空查全部，0:待接单, 1:待服务,2:服务中,3:已完成,4:已取消	
    return process.env.MODE_ENV === 'server'
      ? [
          {
            key: '1,2',
            title: '进行中',
          },
          {
            key: '3',
            title: '已完成',
          },
          {
            key: '4',
            title: '已取消',
          },
        ]
      : [
          {
            key: 'all',
            title: '全部',
          },
          {
            key: '0',
            title: '待接单',
          },
          {
            key: '1',
            title: '待服务',
          },
          {
            key: '2',
            title: '服务中',
          },
          {
            key: '3',
            title: '已完成',
          },
          {
            key: '4',
            title: '已取消',
          },
        ];
  }, []);

  const onSwitch = (c) => setCurrent(c);

  return {
    current,
    tabs,
    onSwitch,
  };
}
