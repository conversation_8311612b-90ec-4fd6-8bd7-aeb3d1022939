import { View } from "@tarojs/components";
import { useEffect, useMemo } from "react";
import DatePicker from "~base/components/picker/datePicker";
import { countDateRange, getDateMonth, getRangeEndDate } from "../range/_utils";


const OrderListDateMonth = (props) => {
    const { value = [], onChange, onEnd, onStart, active } = props;
    const triggerChange = (v) => onChange?.(countDateRange(1, v));

    const handleChange = (v) => triggerChange(v.value);

    const dateValue = useMemo(() => {
        return getDateMonth(getRangeEndDate(value));
    }, [value]);

    useEffect(() => {
        if (active) {
            // 月份激活，重新计算下
            triggerChange(getRangeEndDate(value));
        }
    }, [active]);

    return (
        <DatePicker immediateChange disabledAfter fields='month' value={dateValue} change={handleChange} end={onEnd} start={onStart} />
    )

}

export default OrderListDateMonth;