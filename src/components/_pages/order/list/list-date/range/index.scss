.date-range {
  &__wrapper {
    padding: $spacing-v-md $spacing-h-md;
  }

  &__bars-item,
  &__values-item {
    background-color: #F7F8FA;
    border: $width-base solid #F7F8FA;
    border-radius: $border-radius-base;
    padding: $spacing-v-md $spacing-h-md;

    &-active {
      color: $color-brand;
      border-color: $color-brand;
    }
  }

  &__bars {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-v-md;

    &-item {
      margin-right: $spacing-h-md;
      padding: $spacing-v-sm $spacing-h-lg;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  &__values {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-line {
      padding: 0 $spacing-h-md;
      color: #969799;
    }

    &-item {
      width: 280px;
      text-align: center;
    }

    &-placeholder {
      color: #969799;
    }
  }
}
