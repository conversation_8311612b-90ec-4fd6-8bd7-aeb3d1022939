import { View, Text } from "@tarojs/components";
import DatePicker from "~base/components/picker/datePicker";
import { useDateRange, useQuickDateBars } from "./_utils";
import classNames from "classnames";
import './index.scss';

const RangeBar = (props) => {
    const { current, type, value, onClick, placeholder } = props;
    const handleClick = () => onClick(type);

    return (
        <View
            className={classNames("date-range__values-item", { "date-range__values-item-active": current === type })}
            onClick={handleClick}
            hoverClass="kb-hover"
        >
            {
                value
                    ? <Text>{value}</Text>
                    : <Text className="date-range__values-placeholder">{placeholder}</Text>
            }
        </View>
    )
}

const DateRangePicker = (props) => {
    const { value, onEnd, onStart } = props;
    const { bars, current, onSwitch } = useQuickDateBars(props);

    const { current: dateCurrent, date: dateValue, onChange: onDatePickerChange, onSwitch: onDateSwitch } = useDateRange(props);
    const [start = '', end = ''] = value;

    return (
        <View className="date-range">
            <View className="date-range__wrapper">
                <View className="date-range__bars">
                    {
                        bars.map((item) => (
                            <View
                                key={item.range}
                                className={classNames('date-range__bars-item', {
                                    'date-range__bars-item-active': current === item.range
                                })}
                                onClick={() => onSwitch(item.range)}
                                hoverClass="kb-hover"
                            >
                                {item.label}
                            </View>
                        ))
                    }
                </View>
                <View className="date-range__values">
                    <RangeBar type={0} value={start} current={dateCurrent} onClick={onDateSwitch} placeholder='开始日期' />
                    <View className="date-range__values-line">-</View>
                    <RangeBar type={1} value={end} current={dateCurrent} onClick={onDateSwitch} placeholder='结束日期' />
                </View>
            </View>
            <DatePicker disabledAfter immediateChange fields='day' value={dateValue} change={onDatePickerChange} end={onEnd} start={onStart} />
        </View>
    )

}

export default DateRangePicker;