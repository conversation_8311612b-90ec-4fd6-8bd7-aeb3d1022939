import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { dayFormatter, monthFormatter } from '~base/components/dateTimer/_utils';

export function getRangeEndDate(value) {
  return value?.slice(-1)?.[0];
}

// 获取月
export function getDateMonth(date) {
  return dayjs(date || new Date()).format(monthFormatter);
}

// 计算开始和结束日期
export function countDateRange(range = 1, date, by = 'month') {
  let start = '';
  let end = '';
  // 当前日期（结束日期）
  const curDate = dayjs();
  const curDateDay = curDate.endOf('day');

  if (range === 1) {
    // 当月
    start = dayjs(date).startOf(by); // 当月第一天 00:00:00
    end = dayjs(date).endOf(by); // 当月最后一天 23:59:59
    if (end.isAfter(curDateDay)) {
      // 比当前日期大，只能为当前日期
      end = curDateDay;
    }
  } else {
    start = curDate.subtract(range, by).startOf('day');
    end = curDateDay;
  }
  return [start, end].map((v) => (v ? v.format(dayFormatter) : ''));
}

// 检查是否是当前日期范围
export function checkIsCurrentDateRange(value, range, date, by) {
  if (!value || !value.length) return false;
  const [start, end] = countDateRange(range, date, by);
  return value[0] === start && value[1] === end;
}

export function useQuickDateBars(props) {
  const { value, onChange: onChangeProps } = props;
  const [current, setCurrent] = useState('');

  const bars = [
    {
      label: '近3个月',
      range: 3,
    },
    {
      label: '近半年',
      range: 6,
    },
    {
      label: '近一年',
      range: 12,
    },
  ];

  const onSwitch = (c) => {
    setCurrent(c);
    const v = countDateRange(c);
    onChangeProps?.(v);
  };

  useEffect(() => {
    const isInRange = checkIsCurrentDateRange(value, current);
    if (!isInRange) {
      // 如果当前日期范围不在选中范围内，找到符合条件的范围，否则设置为空
      const { range = '' } = bars.find((item) => checkIsCurrentDateRange(value, item.range)) || {};
      setCurrent(range);
    }
  }, [value, current]);

  return {
    bars,
    current,
    onSwitch,
  };
}

// 日期变更
export function useDateRange(props) {
  const { onChange: onChangeProps, value = [] } = props;
  const [current, setCurrent] = useState(0);
  const [date, setDate] = useState('');

  // 切换输入起始还是结束日期
  const onSwitch = (c) => {
    setCurrent(c);
  };

  // 日期变更
  const onChange = ({ value: date }) => {
    setDate(date);
    const newValues = [...value];
    newValues[current] = date;
    onChangeProps?.(newValues);
  };

  useEffect(() => {
    // 日期变更，同步更新picker日期
    const v = value[current] || '';
    setDate(v);
  }, [current, value]);

  return {
    current,
    value,
    date,
    onChange,
    onSwitch,
  };
}
