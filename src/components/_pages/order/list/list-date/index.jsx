import { Text, View } from "@tarojs/components";
import dayjs from "dayjs";
import { useEffect, useMemo, useRef } from "react";
import Taro from "@tarojs/taro";
import { checkIsCurrentDateRange } from "./range/_utils";
import { dayFormatterCN, monthFormatterCN } from "~base/components/dateTimer/_utils";
import './index.scss';

const OrderListDate = (props) => {
    const { value, onChange, actionRef } = props;

    const handleClick = () => {
        actionRef.current.on?.('change', onChange);
        actionRef.current?.open?.(value);
    }

    const isMonth = useMemo(() => checkIsCurrentDateRange(value, 1, value[0]), [value]);

    return (
        <View className="order-list-date" hoverClass="kb-hover-opacity" onClick={handleClick}>
            {value
                ? isMonth
                    ? dayjs(value[0]).format(monthFormatterCN)
                    : value.map(item => dayjs(item).format(dayFormatterCN)).join(' ~ ')
                : <Text className="kb-color__grey">请选择日期</Text>}
        </View>
    )
}

export default OrderListDate;