import { useImperative<PERSON>andle, useRef, useState } from "react";
import { AtButton, AtFloatLayout, AtTabs, AtTabsPane } from "taro-ui";
import { useOrderListDatePicker } from "./_utils/tabs";
import OrderListDateMonth from "../month";
import DateRangePicker from "../range";
import { View } from "@tarojs/components";
import { checkIsCurrentDateRange, countDateRange } from "../range/_utils";
import './index.scss';
import { checkDateRangeIsValid } from "~base/components/picker/_utils/date";
import Taro from "@tarojs/taro";

const OrderListDatePicker = (props) => {
    const { tabs, current, onSwitch } = useOrderListDatePicker();
    const ref = useRef({ methods: {}, defaultValue: [], lock: false });
    const [isOpened, setIsOpened] = useState(false);
    const [value, setValue] = useState([]);
    const { actionRef } = props;

    // 关闭
    const handleClose = () => setIsOpened(false);

    // 日期区间变更
    const handleDateChange = (v) => setValue(v);

    // 重置
    const handleReset = () => {
        const { defaultValue } = ref.current;
        setValue(defaultValue);
        const isMonth = checkIsCurrentDateRange(defaultValue, 1, defaultValue[0]);
        onSwitch(isMonth ? 0 : 1);
    }

    // 确认
    const handleConfirm = () => {
        if (ref.current.lock) return false;
        const isValid = checkDateRangeIsValid(value);
        if (isValid <= 0) {
            Taro.kbToast({
                text: isValid === 0 ? '开始日期大于结束日期，请调整' : `请选择${isValid === -1 ? '开始' : '结束'}日期`,
            });
            return false;
        }

        handleClose();
        ref.current.methods?.change?.(value);
    }

    // 滚动
    const handleStartOrEnd = e => {
        ref.current.lock = e.type === 'pickstart';
    }

    useImperativeHandle(actionRef, () => ({
        open: (v) => {
            ref.current.defaultValue = v;
            handleReset(v);
            setIsOpened(true);
        },
        on: (key, callback) => {
            ref.current.methods[key] = callback;
        }
    }));

    return (
        <AtFloatLayout
            isOpened={isOpened}
            onClose={handleClose}
            className="order-list-date-picker"
        >
            <View className="order-list-date-picker__close" hoverClass="kb-hover-opacity" hoverStopPropagation onClick={handleClose}><View className='at-icon at-icon-close' /></View>
            <AtTabs
                tabList={tabs}
                current={current}
                onClick={onSwitch}
            >
                {
                    tabs.map((item, index) => (
                        <AtTabsPane key={item.key} current={current} index={index}>
                            {
                                index === 0
                                    ? (
                                        <View className="kb-spacing-md-t">
                                            <OrderListDateMonth active={current === index} onChange={handleDateChange} value={value} onStart={handleStartOrEnd} onEnd={handleStartOrEnd} />
                                        </View>
                                    )
                                    : (
                                        <DateRangePicker onChange={handleDateChange} value={value} onStart={handleStartOrEnd} onEnd={handleStartOrEnd} />
                                    )
                            }
                        </AtTabsPane>
                    ))
                }
            </AtTabs>
            <View className="order-list-date-picker__btn">
                <AtButton circle onClick={handleReset}>重置</AtButton>
                <AtButton circle type='primary' onClick={handleConfirm}>确定</AtButton>
            </View>
        </AtFloatLayout>
    )
}

export default OrderListDatePicker;