import { View } from '@tarojs/components';
import classNames from 'classnames';
import { checkOrderIsCancel, getCurrentOrderPaidStatus, getCurrentOrderPayStatus, getCurrentOrderStatus } from './_utils';
import './index.scss';

/**
 * 状态 ,payStatus 为true 时，已取消，显示退款状态
 * @param {{data?:{status?:string};strong?:boolean;long?:boolean;payStatus?:boolean}} props
 * @returns
 */
const OrderStatus = (props) => {
  const { data, strong, long, payStatus } = props;
  const statusLabel = payStatus && checkOrderIsCancel(data) && getCurrentOrderPaidStatus(data?.pay_status) || getCurrentOrderStatus(data, long);
  const rootCls = classNames('order-status', `order-status__${data?.status}`, {
    'order-status__strong': strong
  });

  return <View className={rootCls}>{statusLabel}</View>;
};

export default OrderStatus;
