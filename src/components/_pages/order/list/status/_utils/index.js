import { checkOrderIsRental } from "../../card/rental/bars/utils";

function patchOrderStatus(label, long = false) {
  return !long ? label : `订单${label}`;
}

// 订单是否可再来一单
export function checkOrderCanCopy(data) {
  return process.env.MODE_ENV === 'client' && `${data?.status}` === '4';
}

// 订单活动中：待服务、服务中
export function checkOrderIsActive(data) {
  const activeStatus = ['1', '2'];
  return activeStatus.includes(`${data?.status}`);
}

// 订单线路未开通
export function checkOrderRoteIsNotActivated(data) {
  return `${data?.status}` === '5';
}

// 订单是否已接单
export function checkOrderIsReceived(data) {
  return `${data?.status}` === '2';
}

// 待接单
export function checkOrderIsWaiting(data) {
  return `${data?.status}` === '0';
}

// 是否显示轨迹
export function checkOrderCanShowTracks(data) {
  const enableStatus = ['1', '2', '3'];
  return enableStatus.includes(`${data?.status}`);
}

// 是否显示轨迹状态
export function checkOrderCanShowTracksStatus(data) {
  return checkOrderIsActive(data);
}

// 是否展示形成轨迹路线
export function checkOrderCanShowTrackTimeLine(data) {
  return `${data?.status}` !== '1';
}

/**
 *
 * @description 订单状态,0:待接单, 1:待服务,2:服务中,3:已完成,4:已取消,5:待服务(等待线路开通)
 * @param {{status?:string}} data
 * @param {boolean} long
 * @returns
 */
export function getCurrentOrderStatus(data, long) {
  const statusMap = checkOrderIsRental(data)
    ? {
        // 0:待付款, 1:待接单 2:待交付,3:已发车待交付4:租赁中,5:已还车，待收车,6:已收车，待结算,7:已结算，待确认,8:已完成,9:已取消'
        0: '待付款',
        1: '待接单',
        2: '待交付',
        3: '待交付',
        4: '租赁中',
        5: '待收车',
        6: '待结算',
      }
    : {
        0: '待接单',
        1: '待服务',
        2: '服务中',
        3: '已完成',
        4: '已取消',
        5: '待接单',
      };

  return patchOrderStatus(statusMap[`${data?.status}`], long);
}

/**
 *
 * @description 订单已取消
 * @param {*} data
 * @returns
 */
export function checkOrderIsCancel(data) {
  return `${data?.status}` === '4';
}

/**
 *
 * @description 支付状态：0-未支付，1-已支付，2-已退款
 * @param {*} status
 * @returns
 */
export function getCurrentOrderPayStatus(status) {
  const statusMap = {
    0: '未支付',
    1: '已支付',
    2: '退款成功',
    3: '退款失败',
  };

  return statusMap[`${status}`];
}

/***
 *
 * @description 获取取消订单后的已支付状态
 */
export function getCurrentOrderPaidStatus(status) {
  const paidStatus = ['2', '3'];
  if (!paidStatus.includes(status)) return '';
  return getCurrentOrderPayStatus(status);
}
