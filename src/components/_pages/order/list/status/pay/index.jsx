import { View } from '@tarojs/components';
import classNames from 'classnames';
import { getCurrentOrderPayStatus } from '../_utils';
import './index.scss';

/**
 * 状态
 * @param {{data?:{status?:string}}} props
 * @returns
 */
const OrderPayStatus = (props) => {
  const { data } = props;
  const statusLabel = getCurrentOrderPayStatus(data?.pay_status);
  const rootCls = classNames('order-pay-status', `order-pay-status__${data?.pay_status}`);

  return statusLabel ? <View className={rootCls}>{statusLabel}</View> : null;
};

export default OrderPayStatus;
