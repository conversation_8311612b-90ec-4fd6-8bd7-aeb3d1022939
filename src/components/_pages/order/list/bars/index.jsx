import { View } from '@tarojs/components';
import classNames from 'classnames';
import { AtButton, AtIcon } from 'taro-ui';
import { useOrderOperatorBars } from './_utils';
import Popconfirm from '~base/components/popconfirm';
import './index.scss';

/**
 * 
 * @param {*} props 
 * @returns 
 */
const OrderBars = (props) => {
  const { className, data, onSuccess, onOperate, type = 'list' } = props;
  const { bars, run } = useOrderOperatorBars(data, type);

  const hasBars = bars.length > 0;
  const rootCls = classNames('order-bars', className);

  // 成功回调
  const triggerSuccess = (res) => {
    const isSuccess = `${res.code}` === '0';
    if (isSuccess) {
      onSuccess?.();
    }
    return res;
  }

  // 操作
  const handleOperator = (key) => {
    onOperate?.();
    run(key, data).then(triggerSuccess);
  }

  const btnCls = type === 'list' ? 'kb-button__link' : ''

  return (
    hasBars ? (
      <View className={rootCls}>
        {
          bars.map((item, index) => {
            return (
              <View key={item.key} className='order-bars__item'>
                {
                  item.confirm
                    ? (
                      <Popconfirm buttonProps={{ size: 'small', circle: true, type: '', className: btnCls }} title={`确定${item.label}？`} onConfirm={() => handleOperator(item.key)}>{item.icon && <AtIcon prefixClass='kb-icon' value={item.icon} />}{item.label}</Popconfirm>
                    )
                    : (
                      <AtButton className={btnCls} size="small" circle onClick={() => handleOperator(item.key)}>{item.icon && <AtIcon prefixClass='kb-icon' value={item.icon} />}{item.label}</AtButton>
                    )
                }
              </View>
            )
          })
        }
      </View>
    )
      : null
  );
};

export default OrderBars;
