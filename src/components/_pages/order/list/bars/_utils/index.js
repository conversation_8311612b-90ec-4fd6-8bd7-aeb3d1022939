import Taro from '@tarojs/taro';
import { useMemo } from 'react';
import { removeOrder } from '~/services/order/detail';
import { useActionContext } from '~base/components/actionContext/_utils/hooks';
import longListRefresherManager from '~base/components/long-list/refresher';
import { OperateCancelModalName } from '../../../detail/operate/cancel/_utils';
import { checkOrderCanCancel, checkOrderCanRemove } from '../../../detail/operate/_utils';
import { checkOrderCanCopy } from '../../status/_utils';
import { orderListRefreshKey } from '../../_utils';

/**
 *
 * @description 操作
 */
export function useOrderOperator({ onSuccess } = {}) {
  const { onOpenChange, setSuccessCallback, form } = useActionContext(OperateCancelModalName);
  const run = async (key, item) => {
    const { order_id } = item;
    let res = { code: 9001 };

    switch (key) {
      case 'remove': // 删除
        res = await removeOrder({ order_id });
        break;

      case 'copy': // 重新下单
        Taro.navigator({
          url: 'index',
          options: item,
        });
        break;

      case 'cancel': // 取消
        res = await new Promise((resolve) => {
          form.setFieldsValue({ order_id });
          setSuccessCallback(() => {
            longListRefresherManager.trigger(orderListRefreshKey);
            resolve({ code: 0 });
          });
          onOpenChange(true);
        });
        break;

      case 'detail': // 跳转详情
        longListRefresherManager.record(orderListRefreshKey, onSuccess);
        Taro.navigator({
          url: 'order/detail',
          options: {
            order_id,
          },
        });
        break;

      default:
        break;
    }

    return res;
  };

  return {
    run,
  };
}

/**
 *
 * @description 操作按钮
 * @returns
 */
export function useOrderOperatorBars(data, type = 'list') {
  const { run } = useOrderOperator();

  const bars = useMemo(() => {
    const list =
      type === 'list'
        ? [
            {
              key: 'remove',
              label: '删除订单',
              icon: 'delete',
              confirm: true,
              show: checkOrderCanRemove(data),
            },
          ]
        : [
            {
              key: 'cancel',
              label: '取消订单',
              show: checkOrderCanCancel(data),
            },
            {
              key: 'copy',
              label: '重新下单',
              show: checkOrderCanCopy(data),
            },
          ];

    return list.filter(({ show = true }) => show);
  }, [data]);

  return { bars, run };
}
