/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { createOrderTabs } from '@/components/_pages/order/_utils/order.list';
import { getTabCurrentByType, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import Taro, { Fragment, useMemo, useState, useEffect } from '@tarojs/taro';
import { AtIcon, AtTabs, AtTabsPane } from 'taro-ui';
import OrderListContent from './list-content';
import { useCreateTabs } from './_utils/tabs';
import { useRef } from 'react';
import WPicker from '~base/components/picker';
import './index.scss';
import OrderListDatePicker from './list-date/picker';

const OrderList = () => {
  const { tabs, current, onSwitch } = useCreateTabs();
  const datePickerRef = useRef();

  return (
    <>
      <AtTabs
        className='kb-order__tabs'
        tabList={tabs}
        onClick={onSwitch}
        current={current}
        animated={false}
        swipeable={false}
      >
        {tabs.map((item, index) => {
          return (
            <AtTabsPane key={item.key} current={current} index={index}>
              <OrderListContent
                active={current === index}
                type={item.key}
                actionRef={datePickerRef}
              />
            </AtTabsPane>
          );
        })}
      </AtTabs>
      <OrderListDatePicker actionRef={datePickerRef} />
    </>
  );
};

export default OrderList;
