/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbOrderStatus from '@/components/_pages/order/detail/order-status';
import KbPayInfo from '@/components/_pages/order/detail/pay-info';
import KbToolBar from '@/components/_pages/order/tool-bar';
import KbWelfareInfo from '@/components/_pages/order/welfare-info';
import { getOrderCondition } from '@/components/_pages/order/_utils/order.detail';
import { setClipboardData } from '@/utils/qy';
import KbCheckbox from '@base/components/checkbox';
import { extendMemo } from '@base/components/_utils';
import { noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { Fragment } from '@tarojs/taro';
import classNames from 'classnames';
import { AtAvatar, AtIcon } from 'taro-ui';
import './list-item.scss';

const Index = (props) => {
  const { selectted, data: item, onClick, onUpdate, orderType, orderDate, brands } = props;

  const handleClickItem = () => onClick(item);
  const handleUpdate = (action) => onUpdate(action, item);
  const handleCopy = (waybill) => {
    setClipboardData(waybill, '运单号已复制');
  };

  let logo = '';
  let brandName = '';
  let cityCls = '';
  if (item) {
    const { name } = brands[item.brand] || {};
    logo = item.brand || item.sourceBrand || 'other';
    brandName = name;
    cityCls = classNames('kb-order__info--city', {
      'kb-color__grey': item.order_state === 'canceled',
    });
  }

  const { tcjs_no_brand } = getOrderCondition(orderType, item);

  // 驿站小程序，微快递优寄品牌同步的订单(中通优惠需要展示揽件码)
  const yzOrderFromWkd =
    process.env.MODE_ENV == 'yz' && ['yyj', 'kbyj', 'yhj', 'online'].includes(item.source);
  const ztKbyj = process.env.MODE_ENV == 'yz' && item.brand == 'zt' && item.source == 'kbyj';

  return (
    <Fragment>
      <View hoverClass='kb-hover' onClick={handleClickItem}>
        <View className='kb-list__item kb-order__brand'>
          {!!selectted && (
            <View className='item-checkbox'>
              <KbCheckbox
                disabled={!!item.disabled}
                checked={selectted.includes(item.order_id)}
                onChange={handleClickItem}
              />
            </View>
          )}
          <View className='item-icon'>
            {tcjs_no_brand ? (
              <View className='kb-order__item-tcjs__logo--text'>{tcjs_no_brand.logo}</View>
            ) : (
              <AtAvatar
                image={(brands && brands[logo] && brands[logo].logo_link) || ''}
                circle
                text={process.env.MODE_ENV === 'wkd' ? '微' : '驿'}
                size='small'
              />
            )}
          </View>
          <View className='item-content'>
            <View className='item-content__title'>
              <View className='at-row at-row__align--center at-row__justify--between'>
                {orderType === 'tcjs' ? (
                  <View className='kb-color__grey'>
                    {tcjs_no_brand ? tcjs_no_brand.text : brandName} —— {item.send_city}
                  </View>
                ) : (
                  <View>
                    {item.statusInfo && item.statusInfo.showWaybill ? (
                      <View
                        hoverStopPropagation
                        hoverClass='kb-hover-opacity'
                        onClick={handleCopy.bind(null, item.waybill, (e) => e.stopPropagation())}
                      >
                        <Text className='kb-icon__text--mr'>{item.waybill}</Text>
                        <AtIcon
                          prefixClass='kb-icon'
                          value='copy-text'
                          className='kb-icon-size__sm'
                        />
                      </View>
                    ) : item.collect_code && (ztKbyj || !yzOrderFromWkd) ? (
                      <Fragment>
                        <Text>揽件码 </Text>
                        <Text className='kb-color__brand kb-size__32'>{item.collect_code}</Text>
                      </Fragment>
                    ) : (
                      <Text className='kb-color__grey'>暂无单号</Text>
                    )}
                  </View>
                )}
                <View>
                  <KbOrderStatus data={item.statusInfo} />
                  <KbPayInfo data={item} type='list' orderType={orderType} />
                </View>
              </View>
            </View>
          </View>
        </View>
        {orderType === 'tcjs' ? (
          <View className='kb-order__item-tcjs'>
            <View className='kb-list__item'>
              <View className='item-icon'>
                <View className='kb-order__icon kb-order__icon-send'>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='water'
                    className='kb-icon-size__lg kb-color__green'
                  />
                </View>
              </View>
              <View className='item-content'>
                <View className='item-content__title'>
                  {item.send_address} {item.send_house_num}
                </View>
                <View className='item-content__desc'>
                  {item.send_name} {item.send_mobile}
                </View>
              </View>
            </View>
            <View className='kb-list__item'>
              <View className='item-icon'>
                <View className='kb-order__icon kb-order__icon-receive'>
                  <AtIcon
                    prefixClass='kb-icon'
                    value='water'
                    className='kb-icon-size__lg kb-color__brand'
                  />
                </View>
              </View>
              <View className='item-content'>
                <View className='item-content__title'>
                  {item.receive_address} {item.receive_house_num}
                </View>
                <View className='item-content__desc'>
                  {item.receive_name} {item.receive_mobile}
                </View>
              </View>
            </View>
          </View>
        ) : (
          <View className='kb-list__item'>
            <View className='item-content'>
              {!item.incomplete ? (
                <Fragment>
                  <View className='at-row at-row__align--center'>
                    <View className='kb-order__info'>
                      <View className={cityCls}>{item.send_city}</View>
                      <View className='kb-order__info--name'>{item.send_name}</View>
                    </View>
                    <View className='kb-order__sign'>
                      {item.order_state === 'wait_pickup' && item.dak_pickup_code && (
                        <Text className='kb-order__sign--text'>取件码：{item.dak_pickup_code}</Text>
                      )}
                    </View>
                    <View className='kb-order__info'>
                      <View className={cityCls}>{item.receive_city}</View>
                      <View className='kb-order__info--name'>{item.receive_name}</View>
                    </View>
                  </View>
                  {item.last_logistics && (
                    <View className='kb-size__base kb-order__spacing'>
                      {brandName && (
                        <Text className='kb-color__grey kb-order__fix--l'>【{brandName}】</Text>
                      )}
                      <Text>{item.last_logistics}</Text>
                    </View>
                  )}
                  <View className='kb-size__base kb-order__spacing'>
                    <Text className='kb-color__grey'>下单时间：</Text>
                    <Text>{item.create_time}</Text>
                  </View>
                </Fragment>
              ) : (
                <Text>
                  {orderType === 'send' ? '收' : '发'}
                  件人信息暂未提交
                </Text>
              )}
            </View>
          </View>
        )}
      </View>

      {!selectted && (
        <KbToolBar orderType={orderType} orderDate={orderDate} data={item} onUpdate={handleUpdate}>
          <View className='kb-tool-bar-left'>
            {process.env.MODE_ENV === 'wkd' && <KbWelfareInfo data={item} />}
            {process.env.MODE_ENV === 'yz' && item.source === 'expresslocker' ? (
              <View className='kb-tool-bar-left__kdg'>
                <AtIcon prefixClass='kb-icon' value='kuaidigui' />
              </View>
            ) : null}
          </View>
        </KbToolBar>
      )}
    </Fragment>
  );
};

Index.defaultProps = {
  data: {},
  brands: {},
  orderType: 'send',
  orderDate: '',
  onClick: noop,
  onUpdate: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default extendMemo(Index, ['data', 'brands']);
