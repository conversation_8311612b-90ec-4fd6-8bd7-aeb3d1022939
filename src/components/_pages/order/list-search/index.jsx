/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbScan from '@base/components/scan';
import KbSearch from '@base/components/search';
import { noop } from '@base/utils/utils';
import { View } from '@tarojs/components';
import './index.scss';

const Index = (props) => {
  const { onSearch } = props;
  const handelSearch = (e) => {
    onSearch(e);
  };
  return (
    <KbSearch
      placeholder={
        process.env.MODE_ENV !== 'third'
          ? '请输入快递单号、电话号码查询订单'
          : '请输入快递单号查询订单'
      }
      ghost
      renderButton={
        <View className='kb-spacing-md-r'>
          <KbScan onChange={handelSearch} />
        </View>
      }
      onSearch={handelSearch}
    />
  );
};
Index.defaultProps = {
  onSearch: noop,
};

Index.options = {
  addGlobalClass: true,
};
export default Index;
