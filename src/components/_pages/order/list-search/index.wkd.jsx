/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useCallback, useRef } from '@tarojs/taro';
import { View } from '@tarojs/components';
import KbSearch from '@base/components/search';
import KbScan from '@base/components/scan';
import { noop } from '@base/utils/utils';

import './index.wkd.scss';

const Index = (props) => {
  const { placeholder, onSearch } = props;
  const handleSearch = useCallback(onSearch, []);
  const actionRef = useRef();

  const handleChange = useCallback((value) => actionRef.current.search(value), []);

  return (
    <View className='kb-list-search'>
      <KbSearch
        renderButton={
          <View className='kb-list-search__scan'>
            <KbScan onChange={handleChange} />
          </View>
        }
        actionRef={actionRef}
        theme='white'
        placeholder={placeholder}
        onSearch={handleSearch}
        storageKey={process.env.MODE_ENV === 'wkd' ? 'DeliverSearchHistory' : ''}
      />
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};
Index.defaultProps = {
  placeholder: '请输入运单号/手机号',
  onSearch: noop,
};

export default Index;
