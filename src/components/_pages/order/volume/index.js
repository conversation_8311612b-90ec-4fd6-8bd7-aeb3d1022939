/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbInputNumber from '@base/components/input-number';
import Form from '@base/utils/form';
import { Input, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtSwitch } from 'taro-ui';
import './index.scss';

class Index extends Taro.Component {
  constructor() {
    super(...arguments);
    this.state = {};
  }

  componentDidMount() {
    const { data } = this.props;
    this.createForm(() => {
      if (data) {
        this.formIns.update(data);
      }
    });
  }

  componentDidUpdate(prevProps) {
    const { data } = prevProps;
    const { data: nextData } = this.props;
    if (nextData && nextData != data) {
      nextData.clean && this.formIns.clean();
    }
  }

  // 表单输入变化
  onChange_form = () => {};
  onSubmit_form = () => {};
  createForm = (onReady = () => {}) => {
    const form = {
      checked: {
        value: false,
      },
      volume: {
        value: 0,
        require: false,
      }, //体积
      num: {
        value: 0,
      }, //件数
      length: {
        value: '',
        require: false,
      }, //长
      width: {
        value: '',
        require: false,
      }, //宽
      height: {
        value: '',
        require: false,
      }, //高
    };
    this.formIns = new Form(
      {
        form,
        onChange: ({ value }) => {
          if (/^0\d+/.test(value)) {
            value = value.replace(0, '0.');
          }
          value = 1 * value;
          return value;
        },
        onUpdate: (data) => {
          const { nextData, data: update, formEvent } = data;
          const { width = 0, height = 0, length = 0, volume } = nextData;
          if (formEvent == 'blur') {
            if ('width' in update || 'height' in update || 'length' in update) {
              this.formIns.update({
                volume: width * height * length * 1,
              });
            }
            if ('volume' in update) {
              if (width * height * length * 1 !== volume * 1) {
                this.formIns.update({
                  width: '',
                  height: '',
                  length: '',
                });
              }
            }
          }
          this.props.onChange(nextData);
        },
        onReady,
      },
      this,
    );
  };

  handleChange = (ev) => {
    this.formIns.update({
      checked: ev,
    });
  };

  render() {
    const { form: { data = {} } = {} } = this.state;
    const { showNum, mode } = this.props;
    const switchArea = (
      <AtSwitch
        checked={data.checked || false}
        onChange={this.handleChange.bind(this)}
        color='#009fff'
      />
    );
    return (
      <View>
        {mode == 2 ? (
          <View>
            <View className='kb-form-tag at-row at-row__justify--between'>
              <View>补充体积</View>
              <View>{switchArea}</View>
            </View>
            <View className='kb-color__grey kb-size__sm kb-margin-sm-tb'>
              重量小体积大的货物建议补充体积预估费用更准确
            </View>
          </View>
        ) : (
          <View className='kb-form__item'>
            <View className='at-row at-row__justify--between at-row__align--center'>
              <View className='item-title'>补充体积预估费用更准确</View>
              <View className='at-row at-row__justify--end at-row__align--center kb-spacing-sm-r'>
                {switchArea}
              </View>
            </View>
          </View>
        )}
        {data.checked && (
          <View className='kb-volume-content'>
            <View className='at-row at-row__align--center'>
              <View className='kb-volume-item'>
                <Input
                  className='kb-volume-input'
                  type='digit'
                  maxLength='5'
                  placeholder='长'
                  placeholderClass='kb-volume-input--placeholder'
                  value={data.length}
                  onBlur={this.onChange_form.bind(this, 'length')}
                />
                <Text className='kb-volume-unit'>CM</Text>
              </View>
              <Text className='kb-volume-c'>X</Text>
              <View className='kb-volume-item'>
                <Input
                  className='kb-volume-input'
                  type='digit'
                  maxLength='5'
                  placeholder='宽'
                  placeholderClass='kb-volume-input--placeholder'
                  value={data.width}
                  onBlur={this.onChange_form.bind(this, 'width')}
                />
                <Text className='kb-volume-unit'>CM</Text>
              </View>
              <Text className='kb-volume-c'>X</Text>
              <View className='kb-volume-item'>
                <Input
                  className='kb-volume-input'
                  type='digit'
                  maxLength='5'
                  placeholder='高'
                  placeholderClass='kb-volume-input--placeholder'
                  value={data.height}
                  onBlur={this.onChange_form.bind(this, 'height')}
                />
                <Text className='kb-volume-unit'>CM</Text>
              </View>
            </View>
            <View className='at-row at-row__justify--center at-row__align--center'>
              <View className='kb-volume-item'>
                <KbInputNumber
                  className='kb-volume-input'
                  type='digit'
                  maxLength='5'
                  placeholder='体积'
                  value={data.volume}
                  unit='cm³'
                  mode='2'
                  border={false}
                  cursorSpacing={100}
                  max={999999999}
                  size='xl'
                  onChange={this.onChange_form.bind(this, 'volume')}
                />
              </View>
              {showNum && (
                <View className='kb-volume-item'>
                  <Text className='kb-volume-unit kb-margin-sm-l'>件数</Text>
                  <Input
                    className='kb-volume-input'
                    type='digit'
                    maxLength='5'
                    placeholder=''
                    value={data.num}
                    onBlur={this.onChange_form.bind(this, 'num')}
                  />
                </View>
              )}
            </View>
          </View>
        )}
      </View>
    );
  }
}

Index.defaultProps = {
  mode: 1,
  showNum: false,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
