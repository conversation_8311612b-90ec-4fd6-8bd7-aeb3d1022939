/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { get } from '@/actions/brands';
import { orderAction } from '@/components/_pages/order/_utils/';
import { switchCourierCollect } from '@/components/_pages/order/_utils/courier.detail';
import {
  filterVipCourier,
  formatResponseRelation,
  getApiUrlAndDataRelation,
} from '@/components/_pages/order/_utils/order.relation';
import { refreshControl, REFRESH_KEY_COURIER } from '@/utils/refresh-control';
import KbCheckbox from '@base/components/checkbox';
import KbLongList from '@base/components/long-list';
import KbScan from '@base/components/scan';
import KbSwipeAction from '@base/components/swipe-action';
import { useDidShowCom } from '@base/hooks/page';
import { makePhoneCall, noop } from '@base/utils/utils';
import { Image, Text, View } from '@tarojs/components';
import { useDispatch, useSelector } from '@tarojs/redux';
import Taro, { Fragment, useEffect, useMemo, useRef, useState } from '@tarojs/taro';
import { AtAvatar, AtTag } from 'taro-ui';
import './index.scss';

const swipeActionOptions = [
  {
    key: 'cancel',
    text: '取消收藏',
    style: {
      backgroundColor: '#FF5A7A',
    },
  },
];
const Index = (props) => {
  const { orderDetail, active, type, onReady, current: currentBrand, action = '' } = props;
  const actionRef = useRef({});
  const [list, updateList] = useState(null);
  const dispatch = useDispatch();
  const {
    brands: brandsMap = {},
    relationInfo,
    isVip = false,
  } = useSelector((state) => state.global);
  const {
    brand = '',
    join_code,
    courier_id: current = currentBrand || join_code || brand,
    type: currentType,
    customer: { id: customer_id } = {},
  } = relationInfo || {};
  const listData = {
    pageKey: 'page',
    api: {
      ...getApiUrlAndDataRelation({ type }),
      formatResponse: (res, req) =>
        formatResponseRelation(res, req, { type, action, currentBrand }),
      onThen: (list) => {
        updateList(list);
      },
    },
  };

  const handleReady = (ins) => {
    actionRef.current.listIns = ins;
    onReady(ins);
  };

  // 跳转快递员详情页
  const handleCourierDetail = (item, e) => {
    e.stopPropagation();
    if (!item.account_phone || item.customer) return;
    Taro.navigator({
      url: 'order/station',
      options: {
        phone: item.account_phone,
      },
    });
  };

  // 选择寄件
  const handleSelect = (item) => {
    // 左滑开启，禁止触发点击
    if (openedId) return;
    if (type == 'brand' && item.brand == 'ht') {
      item.brand = 'jt';
    }
    orderAction({
      action: action === 'brand-switch' ? action : 'edit',
      data: {
        ...orderDetail,
        relation: {
          type,
          ...item,
        },
      },
    });
  };

  // 快递员左滑与长按操作
  const [openedId, updateOpenedId] = useState(null);

  const handleSwitchSwipeAction = ({ courier_id = '' } = {}) => {
    updateOpenedId(courier_id || null);
  };
  const handleSwipeActionClick = ({ courier_id, customer }) => {
    let customer_id = customer && customer.id;
    Taro.kbModal({
      content: ['取消收藏将不能向此快递员下单,', '确定要取消收藏吗?'],
      centered: true,
      onConfirm: () => {
        switchCourierCollect({
          courier_id,
          customer_id,
          is_focused: true,
        }).then(() => {
          actionRef.current.listIns && actionRef.current.listIns.loader();
        });
      },
    });
  };

  // 呼叫
  const handleCall = (phone, e) => {
    e.stopPropagation();
    if (!phone) return;
    makePhoneCall(phone);
  };

  useEffect(() => {
    dispatch(get());
  }, []);

  useDidShowCom(() => {
    if (
      type === 'courier' &&
      refreshControl(REFRESH_KEY_COURIER, 'check') &&
      actionRef.current.listIns
    ) {
      actionRef.current.listIns.loader();
    }
  });

  // 根据当前 relationInfo 信息过滤 list
  const filterList = useMemo(() => {
    if (type === 'courier' && list && isVip) {
      return filterVipCourier(relationInfo, list, isVip);
    }
    return list;
  }, [list, isVip, type, relationInfo]);

  return (
    <KbLongList data={listData} active={active} onReady={handleReady}>
      {filterList && (
        <View className='kb-list kb-shop__list'>
          {filterList.map((item) => (
            <View className='kb-margin-md-b' key={item.courier_id || item.brand}>
              {item.title && <View className='kb-color__grey kb-spacing-md-b'>{item.title}</View>}
              <View className='kb-list__item--wrapper'>
                <KbSwipeAction
                  autoClose
                  disabled={type !== 'courier'}
                  isOpened={openedId === item.courier_id}
                  onOpened={handleSwitchSwipeAction.bind(null, item)}
                  onClosed={handleSwitchSwipeAction}
                  onClick={handleSwipeActionClick.bind(null, item)}
                  options={swipeActionOptions}
                >
                  <View
                    className='kb-list__item'
                    hoverClass='kb-hover'
                    onClick={handleSelect.bind(null, item)}
                  >
                    {item.type === 'courier' || item.type === 'dak' ? (
                      <View
                        className='item-icon'
                        hoverClass='kb-hover-opacity'
                        hoverStopPropagation={item.courier_id}
                        onClick={handleCourierDetail.bind(null, item)}
                      >
                        <AtAvatar
                          circle
                          image={
                            item.customer
                              ? item.type == 'dak'
                                ? 'https://cdn-img.kuaidihelp.com/wkd/miniApp/dak.png'
                                : 'https://cdn-img.kuaidihelp.com/wkd/miniApp/courier-pic.png'
                              : `https://upload.kuaidihelp.com/touxiang/counterman_${item.courier_id}.jpg`
                          }
                        />
                        {item.customer ? (
                          <Image
                            className='customer-tag'
                            mode='widthFix'
                            src='https://cdn-img.kuaidihelp.com/wkd/miniApp/customer/qiye.png'
                          />
                        ) : null}
                      </View>
                    ) : (
                      <View className='item-icon'>
                        <AtAvatar
                          size={process.env.PLATFORM_ENV !== 'swan' ? 'normal' : 'small'}
                          circle
                          image={`https://cdn-img.kuaidihelp.com/brand_logo/icon_${type === 'courier' && item.type !== 'brand'
                              ? item.type || item.brand
                              : item.brand
                            }.png?v=20230314`}
                        />
                      </View>
                    )}
                    {type === 'courier' ? (
                      <View className='item-content'>
                        <View className='item-content__title'>
                          <View className='at-row at-row__align--center'>
                            {item.customer ? (
                              <Text className='kb-spacing-sm-r'>{item.customer.customer}</Text>
                            ) : (
                              <Fragment>
                                {item.account_name && (
                                  <Text className='kb-spacing-sm-r'>{item.account_name}</Text>
                                )}
                                <View
                                  className='kb-spacing-sm-l'
                                  hoverClass='kb-hover-opacity'
                                  hoverStopPropagation
                                  onClick={handleCall.bind(null, item.account_phone)}
                                >
                                  {item.account_phone}
                                </View>
                              </Fragment>
                            )}
                          </View>
                        </View>
                        <View className='item-content__desc'>
                          {item.type === 'team' ? (
                            '快递团队'
                          ) : item.customer ? (
                            <Fragment>
                              <Text className='kb-spacing-sm-r'>
                                {item.type === 'dak' ? item.inn_name : item.account_name}
                              </Text>
                              <Text className='kb-spacing-sm-r'>
                                {item.type === 'dak' ? item.dak_mobile : item.account_phone}
                              </Text>
                            </Fragment>
                          ) : (
                            <Fragment>
                              {brandsMap[item.brand] && (
                                <Text className='kb-spacing-sm-r'>
                                  {brandsMap[item.brand].name}
                                </Text>
                              )}
                              <Text>{item.account_shop}</Text>
                              {item.tag && (
                                <View className='kb-courier__tag'>
                                  <AtTag size='small' className='kb-tag__red' type='primary' active>
                                    返
                                  </AtTag>
                                  {item.tag}
                                </View>
                              )}
                            </Fragment>
                          )}
                        </View>
                      </View>
                    ) : (
                      <View className='item-content'>
                        <View className='item-content__title at-row at-row__align--center'>
                          <Text>
                            {item.name ||
                              (brandsMap[item.brand]
                                ? item.brand == 'ht'
                                  ? '极兔（原百世快递）'
                                  : brandsMap[item.brand].name
                                : '')}
                          </Text>
                          {item.brand === 'yjkd' && <Text>为您服务</Text>}
                          {item.hot > 0 && <Text className='item-content__title-hot'>热门</Text>}
                        </View>
                        <View className='item-content__desc'>{item.describe}</View>
                      </View>
                    )}
                    <View className='item-checkbox'>
                      <KbCheckbox
                        checked={
                          (currentType === type ||
                            (currentType === 'dak' && customer_id) ||
                            currentType == 'team') &&
                          (customer_id
                            ? item.customer && item.customer.id == customer_id
                            : item.brand === current ||
                            (!item.customer && item.courier_id === current) ||
                            item.join_code === current)
                        }
                        onChange={handleSelect.bind(null, item)}
                      />
                    </View>
                  </View>
                </KbSwipeAction>
              </View>
            </View>
          ))}
        </View>
      )}
      {type == 'courier' && (
        <View className='kb-courier__scan'>
          <KbScan hoverClass='kb-hover' iconColor='brand' mode='global' />
        </View>
      )}
    </KbLongList>
  );
};

Index.defaultProps = {
  active: false,
  onReady: noop,
  orderDetail: null,
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
