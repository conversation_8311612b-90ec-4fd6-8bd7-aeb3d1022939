/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$zindex-scan: 9;

.kb-courier {
  &__scan {
    position: fixed;
    bottom: $spacing-v-lg;
    left: $spacing-h-lg;
    z-index: $zindex-scan;
    width: 100px;
    height: 100px;
    overflow: hidden;
    color: $color-brand;
    line-height: 98px;
    text-align: center;
    background: $color-white;
    border-radius: $border-radius-circle;
    box-shadow: 0 0 10px 5px rgba($color: $color-grey-0, $alpha: 0.1);
  }

  &__tag {
    padding-top: $spacing-v-xs;
  }
}

.item-content__title {
  &-hot {
    margin-left: $spacing-h-sm;
    padding: $spacing-v-xs/3 $spacing-h-sm;
    color: $color-white;
    font-size: $font-size-xs;
    background: linear-gradient(to right, $color-yellow, $color-red);
    border-radius: $border-radius-md;
  }
}

.item-icon {
  position: relative;
}

.customer-tag {
  position: absolute;
  right: 21px;
  bottom: 29px;
  width: 36px;
  height: 36px;
}
