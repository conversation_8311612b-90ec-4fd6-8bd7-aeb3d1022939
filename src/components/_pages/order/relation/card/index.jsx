/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { setShopInfo } from '@/actions/shop';
import KbLoader from '@base/components/loader';
import {
  checkIsAgent,
  checkIsBrand,
  checkIsCourier,
  checkIsShop,
  openLocation,
} from '@/components/_pages/store-card/_utils';
import { makePhoneCall, noopConnect } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import isObject from 'lodash/isObject';
import { AtIcon } from 'taro-ui';
import './index.scss';

const Index = (props) => {
  const { data, loading } = props;
  // 是否可导航：坐标系是否正常
  const hasLatLng = data && data.latitude && data.longitude;
  // 是否有数据
  const hasInfo =
    checkIsShop(data) || checkIsAgent(data) || checkIsCourier(data) || checkIsBrand(data);
  const identity = checkIsShop(data) ? 'station' : 'other';

  // 电话呼叫
  const handleCall = (e) => {
    e.stopPropagation();
    makePhoneCall(data.phone);
  };

  // 地图导航
  const handleOpenLocation = (e) => {
    e.stopPropagation();
    openLocation(data);
  };

  return hasInfo ? (
    <View className='kb-store__card'>
      {hasInfo && isObject(data) ? (
        <View className='kb-store__card--info'>
          <View className='at-row at-row__align--center at-row__justify--between'>
            <View className='kb-store__card--item'>
              <Text className='kb-icon__text--mr kb-size__lg kb-store__card--name'>
                {decodeURIComponent(data.name)}
              </Text>
              {data.distance && (
                <Text className='kb-icon__text--mr kb-color__brand'>{data.distance}m</Text>
              )}
            </View>

            {data.phone ? (
              <View
                onClick={handleCall}
                hoverClass='kb-hover-opacity'
                className='kb-spacing-md-l'
                hoverStopPropagation
              >
                <AtIcon
                  prefixClass='kb-icon'
                  value='phone'
                  className='kb-icon-size__base kb-color__brand'
                />
              </View>
            ) : null}
          </View>
          {data && data.time && (
            <View className='kb-color__grey kb-size__base kb-store__card--item'>
              营业时间：{decodeURIComponent(data.time)}
            </View>
          )}
          {hasLatLng ? (
            <View className='kb-color__grey kb-size__base kb-store__card--item'>
              <View className='at-row at-row__align--center at-row__justify--between'>
                <Text className='kb-store__address kb-icon__text--mr'>
                  {data.address ? decodeURIComponent(data.address) : ''}
                </Text>
                <View
                  className='kb-store__card--icon'
                  hoverClass='kb-hover-opacity'
                  onClick={handleOpenLocation}
                  hoverStopPropagation
                >
                  <AtIcon
                    prefixClass='kb-icon'
                    value='gps'
                    className='kb-icon-size__base kb-color__brand'
                  />
                </View>
              </View>
            </View>
          ) : identity == 'station' ? (
            <View className='kb-color__grey kb-size__base kb-store__card--item'>
              <Text className='kb-store__address kb-icon__text--mr'>
                {data.address ? decodeURIComponent(data.address) : ''}
              </Text>
            </View>
          ) : null}
        </View>
      ) : loading ? (
        <KbLoader size='small' centered />
      ) : (
        ''
      )}
    </View>
  ) : null;
};

Index.defaultProps = {
  data: null,
  loading: false,
};

Index.options = {
  addGlobalClass: true,
};

export default connect(noopConnect, {
  setShopInfo,
})(Index);
