/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.card {
  padding: $spacing-h-md;
  border-radius: $border-radius-md;
  color: $color-white;
  background: #70c6db;
  &-hd {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &_title {
      display: flex;
      align-items: center;
      font-size: $font-size-lg;
      font-weight: bold;
      &_name {
        margin-right: $spacing-h-sm;
        max-width: 420px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
    &_price {
      font-size: 0;
      &_danwei {
        margin-top: 5px;
        font-size: $font-size-base;
      }
      &_num {
        font-size: 40px;
      }
    }
  }
  &-bd {
    font-size: $font-size-base;
    &-container {
      padding-top: $spacing-h-md;
      display: flex;
      justify-content: space-between;
      border-top: $width-base dashed #fff;
    }
  }
  &-disabled {
    background: $color-grey-4;
  }
}
