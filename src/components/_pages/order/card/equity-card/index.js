/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import KbCheckbox from '@base/components/checkbox';
import classNames from 'classnames';
import './index.scss';

function Index(props) {
  const { className, mode, card, disabled, selected, isShowPrice, onSelect } = props;

  const handleSelect = () => {
    if (disabled) return;
    if (mode == 'selectable') {
      onSelect({ card });
    }
  };

  const handleTips = (ev) => {
    ev.stopPropagation();
    Taro.kbModal({
      content: [
        `1.本产品为您揽件快递员提供的权益产品，由揽件快递员为您负责寄件权益次卡的核销，若有相关使用问题请联系快递员`,
        `2.本产品为寄件权益次卡，可在寄件时抵扣首重运费（1KG内免费寄），重量超出需额外支付续重费给快递员`,
        `3.本产品在有效期内可正常使用，过期无法使用，若使用期间因快递员原因累计2次未及时上门揽件，过期后，按次卡未使用次数对应购卡金额折算后退款给您，如：购买次卡金额￥100/购买10次*5次未使用=退款金额￥50`,
        '4.本产品在规定发往地区可使用',
        '5.本产品每次寄件只可抵扣一张权益次卡',
      ],
      confirmText: '我知道了',
      closable: false,
    });
  };

  const cardCls = classNames('card', className, {
    'card-disabled': disabled,
  });
  return (
    <Fragment>
      {card.id && (
        <View className={cardCls} onClick={handleSelect} hoverClass='kb-hover-opacity'>
          <View className='card-hd'>
            <View
              className='card-hd_title'
              onClick={handleTips}
              hoverClass='kb-hover-opacity'
              hoverStopPropagation
            >
              <View className='card-hd_title_name'>{card.card_name}</View>
              <AtIcon
                prefixClass='kb-icon'
                value='help'
                className='kb-color__white kb-icon-size__base'
              />
            </View>
            {isShowPrice && (
              <View className='card-hd_price'>
                <Text className='card-hd_price_danwei'>￥</Text>
                <Text className='card-hd_price_num'>{card.price}</Text>
              </View>
            )}
          </View>
          <View className='card-bd'>
            <View className='kb-margin-sm-tb'>
              {card.desc &&
                card.desc.map((oItem) => (
                  <View className='kb-margin-sm-b' key={oItem.desc}>
                    {oItem}
                  </View>
                ))}
            </View>
            <View className='card-bd-container'>
              <View className='card-bd_region'>适用发往地区：{card.region}</View>
              {mode == 'selectable' && <KbCheckbox checked={selected} />}
            </View>
          </View>
        </View>
      )}
    </Fragment>
  );
}

Index.defaultProps = {
  className: '',
  mode: 'normal',
  card: {},
  disabled: false,
  selected: false,
  isShowPrice: true,
  onSelect: () => {},
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
