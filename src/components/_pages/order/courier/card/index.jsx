/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { View, Text } from '@tarojs/components';
import { AtIcon } from 'taro-ui';
import { makePhoneCall } from '@base/utils/utils';
import './index.scss';

const Index = (props) => {
  const { data } = props;
  const {
    account_name,
    brandName,
    service_count_msg,
    account_phone,
    name = account_name,
    phone = account_phone,
  } = data || {};

  const handleCall = (phone) => {
    makePhoneCall(phone);
  };

  return (
    <View className='kb-spacing-md'>
      <View className='at-row'>
        <View className='at-col'>
          <View className='kb-size__bold kb-margin-sm-b at-row at-row__justify--between'>
            <Text className='kb-spacing-sm-r'>{name}</Text>
            {phone && (
              <AtIcon
                onClick={handleCall.bind(null, phone)}
                prefixClass='kb-icon'
                value='phone'
                className='kb-icon-size__sm kb-color__brand kb-spacing-sm-l '
              />
            )}
          </View>
          <View className='kb-margin-sm-b kb-black-2'>{brandName}</View>
          <View className=' kb-black-2'>
            <Text>最近30天揽件</Text>
            <Text className='kb-spacing-md-l kb-color__brand'>{service_count_msg}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};
Index.options = {
  addGlobalClass: true,
};

export default Index;
