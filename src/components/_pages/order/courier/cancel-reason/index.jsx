/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import KbCheckbox from '@base/components/checkbox';
import { noop } from '@base/utils/utils';
import { View, Text } from '@tarojs/components';
import { useEffect, useState } from '@tarojs/taro';
import { AtInput } from 'taro-ui';
import { cancelReasons } from '@/components/_pages/order/_utils/courier.detail';

const Index = (props) => {
  const { onConfirm, actionRef } = props;
  const [isOpened, updateIsOpened] = useState(false);
  const [desc, updateDesc] = useState('');
  const [reason, updateReason] = useState('1');
  const handleClose = () => updateIsOpened(false);
  const handleConfirm = () => {
    handleClose();
    onConfirm({
      reason,
      desc,
    });
  };
  const handleInputDesc = (desc) => updateDesc(desc);
  const handleChangeReason = (reason) => updateReason(reason);
  useEffect(() => {
    if (!actionRef) return;
    actionRef.current = {
      open: () => updateIsOpened(true),
    };
  }, []);

  return (
    <KbModal
      isOpened={isOpened}
      title='取消收藏原因'
      onClose={handleClose}
      onCancel={handleClose}
      onConfirm={handleConfirm}
      confirmText='取消收藏'
      cancelText='不取消了'
    >
      <View className='kb-spacing-md-b'>
        {cancelReasons.map((item) => (
          <View key={item.key} className='kb-spacing-md-b'>
            <KbCheckbox
              checked={item.key === reason}
              onChange={handleChangeReason.bind(null, item.key)}
            >
              <Text className='kb-spacing-md-l'>{item.label}</Text>
            </KbCheckbox>
          </View>
        ))}
      </View>
      {isOpened && (
        <AtInput
          placeholder='请填写详细原因'
          border={false}
          cursor={-1}
          maxLength={30}
          className='kb-input__circle'
          value={desc}
          onChange={handleInputDesc}
        />
      )}
    </KbModal>
  );
};

Index.defaultProps = {
  onConfirm: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
