/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import KbModal from '@base/components/modal';
import KbCoupon from '@/components/_pages/order/coupon';
import { noop } from '@base/utils/utils';
import { useEffect, useState } from '@tarojs/taro';
import './index.scss';

const Index = (props) => {
  const { onConfirm, actionRef } = props;
  const [isOpened, updateIsOpened] = useState(false);
  const [coupon, updateCoupon] = useState(null);
  const handleClose = () => updateIsOpened(false);
  const handleConfirm = () => {
    handleClose();
    onConfirm();
  };
  useEffect(() => {
    if (!actionRef) return;
    actionRef.current = {
      open: (data) => {
        const { expiration_time: date, cost, account_name: name } = data || {};
        if (date && cost && name) {
          updateCoupon({
            date,
            cost,
            name,
            type: 'courier',
          });
          updateIsOpened(true);
        }
      },
    };
  }, []);

  return (
    <KbModal
      isOpened={isOpened}
      title='恭喜'
      onClose={handleClose}
      onCancel={handleClose}
      onConfirm={handleConfirm}
      confirmText='继续下单'
      full
    >
      {coupon && (
        <View className='kb-coupon-modal'>
          <View className='kb-spacing-sm-b'>首次扫码关注此快递员获得抵用券1张,</View>
          <View className='kb-spacing-lg-b'>已放入您的账户，在线支付享立减。</View>
          <KbCoupon data={coupon} border={false} color='#e5e5e5' />
        </View>
      )}
    </KbModal>
  );
};

Index.defaultProps = {
  onConfirm: noop,
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
