/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-welfare-info {
  pointer-events: none;

  &__cash {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    &::before,
    &::after {
      content: "";
      display: block;
      background-repeat: no-repeat;
      background-size: 100% auto;
      background-position: center;
      position: relative;
    }

    &::before {
      background-image: url("https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/cash/coin.png");
      width: 24px;
      height: 24px;
      z-index: 2;
    }

    &::after {
      background-image: url("https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/cash/red.png");
      width: 28px;
      height: 26px;
      margin-top: -14px;
      z-index: 1;
    }
  }

  &__spring {
    width: 200px;
    height: 60px;
    background: url("https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/spring/order.png") no-repeat left center;
    background-size: auto 100%;
    position: relative;

    &--tag {
      padding: 0 $spacing-h-xs;
      font-size: $font-size-xs - 2;
      color: $color-white;
      border-radius: $border-radius-arc $border-radius-arc $border-radius-arc 0;
      background: linear-gradient(to right, #fb3f17, #ff6f24);
      animation: jumpAnimation 0.6s ease infinite;
      position: absolute;
      left: 64px;
      top: 0;
    }

  }

  &__coupon {
    width: 170px;
    height: 30px;
    background: url("https://cdn-img.kuaidihelp.com/wkd/miniApp/flzx/return_coupon_tag2.png") no-repeat center;
    background-size: 100% auto;
  }

  &__image {
    &--help1 {
      width: 46px;
      height: 18px;
    }

    &--help2,
    &--help3 {
      width: 58px;
      height: 26px;
    }

    &--avatar {
      width: 35px;
      height: 35px;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateY(-50%);

      &-1 {
        z-index: 2;
        transform: translate(-50%, -50%);
      }

      &-2 {
        left: 0;
        z-index: 1;
      }
    }
  }

  &__help {
    width: 60px;
    height: 60px;
    position: relative;
    border-radius: $border-radius-circle;
    background-color: $color-grey-8;

    &--text {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      z-index: 2;
    }
  }
}

@keyframes jumpAnimation {

  0%,
  100% {
    transform: translateY(-5px);
  }

  50% {
    transform: translateY(5px);
  }
}
