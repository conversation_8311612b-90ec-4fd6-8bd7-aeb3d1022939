/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View, Image } from "@tarojs/components";
import { fillListByCount } from "@/components/_pages/order/_utils/order.welfare";
import "./index.scss";

const helpIconMap = {
  help1: "https://cdn-img.kuaidihelp.com/wkd/miniApp/images/help1.png",
  help2: "https://cdn-img.kuaidihelp.com/wkd/miniApp/images/help2.png",
  help3: "https://cdn-img.kuaidihelp.com/wkd/miniApp/images/help3.png"
};

const Index = props => {
  const { data: item } = props;
  const { help_status } = item;

  let helpImageKey;
  if (help_status && help_status.coupon_type != 4) {
    const { help, help_status: status } = help_status;
    fillListByCount(help);
    helpImageKey = `help${
      status == 1 || status == 2 ? "1" : status == 3 ? "2" : "3"
    }`;
  }

  return (
    <View className="kb-welfare-info">
      {item.activity_double12 || item.activity_newYear ? (
        <View className="kb-welfare-info__cash"></View>
      ) : item.activity_springFestival || item.activity ? (
        <View className="kb-welfare-info__spring">
          <View className="kb-welfare-info__spring--tag">
            {(item.activity_springFestival &&
              item.activity_springFestival.msg) ||
              (item.activity && item.activity[0].msg)}
          </View>
        </View>
      ) : item.help_status && item.help_status.coupon_type == 4 ? (
        <View className="kb-welfare-info__coupon"></View>
      ) : item.help_status ? (
        <View className="kb-welfare-info__help">
          <Image
            lazyLoad
            src={helpIconMap[helpImageKey]}
            mode="widthFix"
            className={`kb-welfare-info__help--text kb-welfare-info__image--${helpImageKey}`}
          />
          {help_status.help &&
            help_status.help.map((iitem, index) => (
              <Image
                lazyLoad
                key={iitem.uid}
                src={
                  iitem.avatar ||
                  "https://cdn-img.kuaidihelp.com/wkd/miniApp/images/help-avatar.png"
                }
                mode="widthFix"
                className={`kb-welfare-info__image--avatar kb-welfare-info__image--avatar-${index}`}
              />
            ))}
        </View>
      ) : null}
    </View>
  );
};

Index.defaultProps = {
  data: {}
};
Index.options = {
  addGlobalClass: true
};

export default Index;
