/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import pick from 'lodash/pick';
import { AtButton } from 'taro-ui';
import { serverIndexOrderRefresherKey } from '~/components/_pages/truck/choose/_utils';
import { checkAndGuideUserAuth } from '~/components/_pages/userTypeLayout/_utils';
import { checkHasServiceAddress } from '~/pages/user/_utils';
import longListRefresherManager from '~base/components/long-list/refresher';

const Operate = (props) => {
  const { data, onSuccess } = props;

  const handleClick = async () => {
    const hasServiceAddr = await checkHasServiceAddress();
    if (!hasServiceAddr) {
      Taro.navigator({
        url: 'user/service/address',
      });
      return;
    }
    const { isAuth } = await checkAndGuideUserAuth();
    if (isAuth) {
      longListRefresherManager.record(serverIndexOrderRefresherKey, onSuccess);
      Taro.navigator({
        url: 'truck/choose',
        options: pick(data, ['order_id', 'goods_volume', 'vehicle_type', 'order_type']),
      });
    }
  };

  return (
    <View className='kb-spacing-lg-b'>
      <AtButton className='kb-button__base' circle type='primary' onClick={handleClick}>
        立即接单
      </AtButton>
    </View>
  );
};

export default Operate;
