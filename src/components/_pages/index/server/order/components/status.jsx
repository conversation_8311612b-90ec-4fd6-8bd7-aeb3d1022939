/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import numeral from 'numeral';
import { dateCalendar } from '~base/utils/utils';

const Status = (props) => {
  const { data } = props;
  const { is_reservation, distance, vehicle_time } = data;
  return (
    <View className='kb-spacing-lg-tb at-row at-row__algn--center at-row__justify--between kb-border-b'>
      <View className='kb-size__lg'>
        {is_reservation == 1 ? (
          <View className='kb-text__ellipsis kb-spacing-md-r'>
            <Text>{dateCalendar(vehicle_time, { timer: true })}</Text>
            <Text className='kb-spacing-md-lr kb-color__grey'>|</Text>
            <Text className='kb-color__brand'>预约</Text>
          </View>
        ) : (
          '即时'
        )}
      </View>
      <View className='kb-size__base2 kb-text__ellipsis'>
        距您
        <Text className='kb-color__brand kb-text__bold'>
          {numeral(distance).format('0.00')}公里
        </Text>
      </View>
    </View>
  );
};

Status.options = {
  addGlobalClass: true,
};
export default Status;
