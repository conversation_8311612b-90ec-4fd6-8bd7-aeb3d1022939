/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { createExtra } from '../utils';

const Extra = (props) => {
  const { data } = props;
  const extra = createExtra(data);

  return extra.map((item, index) => (
    <View
      key={item.key}
      className={classNames(
        'kb-spacing-xxl-t at-row at-row__align--center at-row__justify--between kb-size__base2',
        {
          'kb-spacing-xxl-b': index == extra.length - 1,
        },
      )}
    >
      <Text className='kb-color__greyer'>{item.label}</Text>
      <View className='kb-flex1 kb-color__black kb-text__right kb-text__ellipsis'>
        {item.render ? item.render : item.value}
      </View>
    </View>
  ));
};

Extra.options = {
  addGlobalClass: true,
};
export default Extra;
