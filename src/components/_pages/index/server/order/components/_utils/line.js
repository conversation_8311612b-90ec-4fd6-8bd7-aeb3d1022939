/**
 *
 * @description 起始点
 */
export function checkIsStartPoint(stop_type) {
  return `${stop_type}` === '0' || `${stop_type}` === '1';
}

// 停靠点
export function checkIsStopPoint(stop_type) {
  return `${stop_type}` === '2';
}

// 卸货点
export function checkIsEndPoint(stop_type) {
  return `${stop_type}` === '3';
}

/**
 *
 * @param {'1'|'2'|'3'} stop_type
 * @returns {{key:'1'|'2'|'3';label:string;color:string;aka?:string}|void}
 */
export function getCurrentStopInfo(stop_type) {
  const stopItems = [
    {
      key: '0',
      label: '发车点',
      color: '#0FC7CD',
    },
    {
      key: '1',
      label: '装货点',
      aka: '发车点',
      color: '#0FC7CD',
    },
    {
      key: '2',
      label: '停靠点',
      color: '#07C160',
    },
    {
      key: '3',
      label: '卸货点',
      aka: '返程点',
      color: '#F9B414',
    },
    {
      key: '10',
      label: '返程点',
      color: '#F9B414',
    },
  ];

  return stopItems.find((item) => item.key === `${stop_type}`);
}
