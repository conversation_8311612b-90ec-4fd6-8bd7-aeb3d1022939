/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import { useMemo } from 'react';
import TimeLineExtend from '~base/components/time-line/extend';
import { getCurrentStopInfo } from './_utils/line';
import OperateDepartSelector from '~/components/_pages/order/detail/operate/depart/modal/form/selector';
import { orderModifyTruckStopPoint } from '~/services/order/detail';
import { checkOrderCanModifyStop } from '~/components/_pages/order/detail/operate/_utils';
import '../list.scss';

/**
 *
 * @param {{size?:'small'|'normal';showStopName?:boolean;onSuccess?:()=>void;}} props
 * @returns
 */
const Line = (props) => {
  const { data, hideTitle = false, size, className, showStopName, onSuccess } = props;
  const { order_id, routes = [] } = data || {};

  // 更改停靠点
  const handleModifyStop = (id, stopValue) => {
    orderModifyTruckStopPoint({
      order_id,
      route_id: id,
      stop_id: stopValue.value,
    }).then((res) => {
      const isSuccess = `${res.code}` === '0';
      if (isSuccess) {
        onSuccess?.();
      }
    });
  }

  // 是否可修改停靠点
  const canModifyStop = checkOrderCanModifyStop(data);

  const list = useMemo(() => {
    if (!isArray(routes)) return [];

    const itemCls = 'kb-size__base kb-spacing-xs-t kb-color__grey';

    return routes.map(
      ({ stop_id, contact_name, id, stop_type, stop_name, landmark, address, contact_mobile, contact_phone, vehicle_stop_id, vehicle_stop_name }, index) => ({
        key: `${stop_id || id}-${index}`,
        isCurrent: true,
        className: `kb-order-routes-timeline-color__${stop_type}`,
        content: (
          <View className='at-row at-row__align--start'>
            <View className='kb-spacing-md-r kb-spacing-xs-l'>
              {getCurrentStopInfo(stop_type)?.label || ''}
            </View>
            <View>
              {[
                { label: stop_name || landmark },
                { label: address, className: itemCls },
                {
                  label:
                    contact_name || contact_mobile || contact_phone
                      ? `联系人：${[contact_name, contact_mobile || contact_phone]
                        .filter((item) => !!item)
                        .join(' ')}`
                      : '',
                  className: itemCls,
                },
              ]
                .filter((item) => !!item.label)
                .map((item) => (
                  <View key={item.label} className={item.className}>
                    {item.label}
                  </View>
                ))}
              {
                showStopName && (vehicle_stop_id || canModifyStop) && (
                  <OperateDepartSelector
                    readOnly={!canModifyStop}
                    value={vehicle_stop_id ? { value: vehicle_stop_id, label: `停靠点：${vehicle_stop_name || vehicle_stop_id}` } : null}
                    onChange={v => handleModifyStop(id, v)}
                    placeholder='匹配停靠点'
                    className='kb-order-routes-stop-name kb-size__base kb-spacing-xs-t'
                    hoverClass='kb-hover-opacity'
                  />
                )
              }
            </View>
          </View>
        ),
      }),
    );
  }, [routes]);

  const hasList = list.length > 0;

  return (
    <View className={classNames('kb-order-routes', className)}>
      {!hideTitle && <View className='kb-size__lg kb-color__grey kb-spacing-lg-tb'>配送路线</View>}
      {hasList && (
        <View className='kb-order-routes-timeline'>
          <TimeLineExtend labelWidthAuto centered={false} items={list} size={size} />
        </View>
      )}
    </View>
  );
};

Line.options = {
  addGlobalClass: true,
};
export default Line;
