/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-orderItem {
  padding: 0 $spacing-v-xl;

  .kb-confirm_btn {
    height: 72px;
    line-height: 72px;
  }

  &-rental {
    margin-bottom: $spacing-h-md !important;
    padding: 0;
    overflow: unset;
  }
}

.kb-order-routes {
  &-timeline {
    padding: $spacing-h-md;
    background-color: #f6f7f9;
    border-radius: $border-radius-base;

    &-color__2 {
      .time-line__item-trail-wrap {
        color: $color-green;

        &::after {
          background-color: $color-orange;
        }
      }
    }

    &-color__3 {
      .time-line__item-trail-wrap {
        color: $color-orange;
      }
    }
  }

  &-stop-name {
    min-height: auto;
    padding-left: 0;

    .item-placeholder,
    .item-content {
      flex-grow: 0;
      color: $color-brand;
      text-decoration: underline;
    }

    .item-placeholder {
      color: $color-orange;
    }

    .item-icon {
      display: none;
    }
  }
}
