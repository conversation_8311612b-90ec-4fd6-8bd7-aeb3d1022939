/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { checkOrderIsRental } from '~/components/_pages/order/list/card/rental/bars/utils';
import RentalTruckCard from '~/components/_pages/truck/choose/rental/list/card';
import { serverQueryListApi } from '~/services/index';
import KbLongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import Extra from './components/extra';
import Line from './components/line';
import Operate from './components/operate';
import Status from './components/status';
import './list.scss';
import { createListApiFormatters } from './utils';

const List = () => {
  const { formatResponse, formatRequest } = createListApiFormatters({
    patchKeys: ['last_cargo_order_id', 'last_lease_order_id'],
  });
  const { list, config } = useLongList(serverQueryListApi, {
    api: {
      mastHasMobile: false,
      formatResponse,
      formatRequest,
    },
  });

  const handlerRefresh = () => config.loader();

  return (
    <KbLongList data={config} enableMore noDataText='附近无订单'>
      {list.map((item) => (
        <View key={item.order_id} className='kb-orderItem kb-box'>
          {checkOrderIsRental(item) || true ? (
            <>
              <View className='kb-spacing-md-tb'>租赁车型</View>
              <RentalTruckCard data={item} source='matchList' className='kb-orderItem-rental' />
            </>
          ) : (
            <>
              <Status data={item} />
              <Line data={item} />
              <Extra data={item} />
            </>
          )}
          <Operate data={item} onSuccess={handlerRefresh} />
        </View>
      ))}
    </KbLongList>
  );
};

List.options = {
  addGlobalClass: true,
};
export default List;
