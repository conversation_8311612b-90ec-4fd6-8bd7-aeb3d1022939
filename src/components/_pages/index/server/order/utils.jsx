/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import isUndefined from 'lodash/isUndefined';
import TruckCardType from '~/components/_pages/truck/list/item/card/type';
import { formatResponseDefault } from '~base/components/long-list/create';

export const createExtra = (data) => {
  const { goods_name, note, amount } = data || {};
  return [
    {
      label: '配送物品',
      key: 'goods_name',
      value: goods_name,
    },
    {
      label: '车厢类型',
      key: 'vehicle_type',
      render: <TruckCardType data={data} />,
    },
    {
      label: '订单备注',
      key: 'need_notice',
      value: note,
    },
    {
      label: '订单金额',
      key: 'amount',
      render: amount ? (
        <View>
          <Text className='kb-size__xl kb-text__bold'>{amount}</Text>
          <Text>元</Text>
          <Text className='kb-size__sm kb-color__grey kb-spacing-md-lr'>|</Text>
          <Text>已全额支付</Text>
        </View>
      ) : null,
    },
  ];
};

/**
 * 缓存上次请求结果
 *
 * @param {Object} options
 * @param {string[]} [options.patchKeys] - 使用的req-key
 * @param {Function} [options.responseFormatter] - formatResponse
 * @param {Function} [options.requestFormatter] - formatRequest
 * @returns {{ formatResponse: Function, formatRequest: Function, lastResRef: { current: any } }}
 */
export const createListApiFormatters = (options = {}) => {
  const {
    patchKeys = [],
    responseFormatter = formatResponseDefault,
    requestFormatter = (params) => params,
  } = options || {};
  const lastResRef = { current: null };

  const formatResponse = (res) => {
    lastResRef.current = res?.data;
    return responseFormatter(res);
  };

  const formatRequest = (params = {}) => {
    const nextParams = { ...(params || {}) };

    const data = lastResRef.current;
    if (data && patchKeys && params.page != 1) {
      const ids = Array.isArray(patchKeys) ? patchKeys : [patchKeys];
      ids.forEach((key) => {
        const value = data[key];
        if (!isUndefined(value)) {
          nextParams[key] = value;
        }
      });
    }

    return requestFormatter(nextParams);
  };

  return { formatResponse, formatRequest, lastResRef };
};
