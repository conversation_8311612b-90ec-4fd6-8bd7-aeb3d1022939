/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect } from 'react';
import { AtIcon } from 'taro-ui';
import KbCheckbox from '~base/components/checkbox';
import { useOrderConfig } from '../utils';
import './index.scss';

const TruckType = (props) => {
  const { data = {}, onChange } = props;
  const { vehicle_type } = useOrderConfig();
  const { vehicle_type: type } = data;

  useEffect(() => {
    vehicle_type?.length && onChange({ vehicle_type: vehicle_type[0].value });
  }, [vehicle_type]);

  return (
    <View className='kb-truckType kb-spacing-md-lr'>
      <View className='at-row at-row__align--center at-row__justify--between kb-spacing-md-tb'>
        <View className='kb-size__xl'>用车类型</View>
        <View
          className='kb-truckType__book at-row at-row__align--center at-row__justify--center'
          hoverClass='kb-hover'
          onClick={() => Taro.navigateToDocument('guide')}
        >
          <AtIcon
            prefixClass='kb-icon'
            value='book'
            className='kb-color__brand kb-size__base kb-margin-sm-r'
          />
          <Text>使用教程</Text>
        </View>
      </View>
      <View className='at-row at-row__align--center at-row__justify--between'>
        {vehicle_type?.map((item) => (
          <View
            className='kb-truckType__item'
            key={item.value}
            hoverClass='kb-hover-opacity'
            onClick={() => {
              onChange({ vehicle_type: item.value });
            }}
          >
            <Image
              className='kb-truckType__item-image'
              src={`https://cdn-img.kuaidihelp.com/truck/truck_${item.value}.png`}
            />
            <View className='kb-truckType__item-name'>
              <KbCheckbox
                className='at-row'
                labelClassName='kb-truckType__item-label'
                label={item.label}
                reverse
                checked={type === item.value}
                onChange={() => {
                  onChange({ vehicle_type: item.value });
                }}
              />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

TruckType.options = {
  addGlobalClass: true,
};

export default TruckType;
