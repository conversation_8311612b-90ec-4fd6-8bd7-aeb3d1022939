/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import { addressTypeMap, checkAddressValid } from '../utils';
import './index.scss';

const AddressItem = (props) => {
  const { data, handleRemove, handleEdit, handleClear } = props;
  const { stop_type, province, city, district, address, landmark, contact_name, contact_phone } =
    data;

  return (
    <View className='kb-address-item  at-row at-row__align--center'>
      <View
        className={classNames('kb-address-item__content at-row at-row__align--center', {
          'kb-address-item__empty': !contact_phone,
        })}
      >
        <View className='kb-flex1 at-row at-row__align--center kb-widthUnset'>
          <View
            className={classNames('kb-address-item__type', {
              [`kb-address-item__type-${stop_type}`]: !!stop_type,
            })}
          >
            {addressTypeMap[stop_type]?.short}
          </View>
          <View
            className='kb-address-item__info'
            hoverClass='kb-hover-opacity'
            onClick={() => handleEdit('edit')}
          >
            {!contact_phone ? (
              <View className='kb-address-item__placeholder'>
                {addressTypeMap[stop_type]?.placeholder}
              </View>
            ) : (
              <>
                <View className='kb-size__base2 kb-spacing-sm-b'>{landmark}</View>
                <View className='kb-size__sm kb-color__grey kb-text__ellipsis'>
                  {province}
                  {city}
                  {district}
                  {address}
                </View>
                <View className='kb-size__sm kb-color__grey'>
                  <Text>{contact_name}</Text>
                  <Text> {contact_phone}</Text>
                </View>
              </>
            )}
          </View>
          {checkAddressValid(data) && (
            <View
              className='kb-spacing-lg-r'
              hoverClass='kb-hover-opacity'
              onClick={() => handleClear()}
            >
              <AtIcon prefixClass='kb-icon' value='jia' className='kb-size__xl kb-clear__icon' />
            </View>
          )}
        </View>
        <View
          className='kb-address-item__edit kb-spacing-xl-r'
          hoverClass='kb-hover-opacity'
          onClick={() => handleEdit('address')}
        >
          <AtIcon prefixClass='kb-icon' value='addr' className='kb-size__xl kb-color__black' />
        </View>
      </View>
      {stop_type == 2 && (
        <View
          className='at-row at-row__align--center kb-delete-btn'
          hoverClass='kb-hover-opacity'
          onClick={handleRemove}
        >
          <AtIcon prefixClass='kb-icon' value='jian' className='kb-size__xl kb-spacing-md-l' />
        </View>
      )}
    </View>
  );
};

export default AddressItem;
