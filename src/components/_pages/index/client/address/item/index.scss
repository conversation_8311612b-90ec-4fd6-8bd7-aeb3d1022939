/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

$color-start: #11c0c6;
$color-stop: #0bb955;
$color-end: #f8ab14;

.kb-address-item {
  margin-bottom: 24px;

  &__content {
    flex: 1;
    box-sizing: border-box;
    width: unset;
    min-width: 0;
    min-height: 88px;
    padding-left: 12px;
  }
  &__empty {
    color: $color-grey-0;
    font-weight: 400;
    font-size: 32px;
    line-height: 88px;
    background-color: #f5f7f9;
    border-radius: $border-radius-base;
    .kb-address-item__type {
      position: relative;
      color: transparent !important;
      background-color: transparent !important;
      &::after,
      &::before {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 1;
        width: 12px;
        height: 12px;
        border-radius: $border-radius-circle;
        transform: translate(-50%, -50%);
        content: '';
      }
      &::after {
        z-index: 2;
        width: 24px;
        height: 24px;
      }
      &-1,
      &-4 {
        &::before {
          background-color: $color-start;
        }
        &::after {
          background-color: rgba($color: $color-start, $alpha: 0.1);
        }
      }
      &-2 {
        &::before {
          background-color: $color-stop;
        }
        &::after {
          background-color: rgba($color: $color-stop, $alpha: 0.1);
        }
      }
      &-3 {
        &::before {
          background-color: $color-end;
        }
        &::after {
          background-color: rgba($color: $color-end, $alpha: 0.1);
        }
      }
    }
  }
  &__placeholder {
    color: $color-black-0;
    font-weight: 400;
    font-size: 32px;
  }
  &__type {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    color: $color-white;
    font-weight: 400;
    font-size: 28px;
    line-height: 48px;
    text-align: center;
    border-radius: $border-radius-base;
    &-1,
    &-4 {
      background-color: $color-start;
    }
    &-2 {
      background-color: $color-stop;
    }
    &-3 {
      background-color: $color-end;
    }
  }
  &__info {
    flex: 1;
    padding: 0 $spacing-h-md;
    overflow: hidden;
  }
  &__edit {
    position: relative;
    padding-left: $spacing-h-md;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: $width-base;
      height: 40px;
      background-color: #ebedf0;
      transform: translateY(-50%);
      content: '';
    }
  }
  .kb-icon-jian {
    color: #df444f;
  }
  .kb-clear__icon {
    color: #d7d9dc !important;
    transform: rotate(45deg);
  }
  .kb-flex1 {
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }
  .kb-delete-btn {
    flex-shrink: 0;
    width: unset;
  }
}
