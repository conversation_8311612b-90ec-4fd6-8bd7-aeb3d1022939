/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import isUndefined from 'lodash/isUndefined';
import { useEffect } from 'react';
import { useDidShowCom } from '~base/hooks/page';
import { createListener } from '~base/utils/utils';
import { checkAddressSupport } from '../utils';

export const addressTypeMap = {
  1: {
    short: '装',
    placeholder: '请输入装货点',
  },
  2: {
    short: '停',
    placeholder: '请输入停靠点',
  },
  3: {
    short: '卸',
    placeholder: '请输入卸货点',
  },
  4: {
    short: '收',
    placeholder: '请输入收车地址',
  },
};

export const clientAddressEditKey = 'client-address-edit';
export const clientLocationKey = 'client_location';

export const clientAddressForm = [
  'contact_name',
  'contact_phone',
  'landmark',
  'province',
  'city',
  'district',
  'address',
  'lon',
  'lat',
];

export const checkAddressValid = (data) => {
  return clientAddressForm.every((item) => data[item] && `${data[item]}`.trim());
};

const addStopSortAndNotice = (routes) => {
  return routes.map((item, index) => {
    return {
      ...item,
      stop_sort: index + 1,
      need_notice: isUndefined(item.need_notice) ? 1 : +item.need_notice,
    };
  });
};

export const useAddressEdit = (props) => {
  const { data, onChange, location } = props;
  const { routes = [] } = data || {};

  const handleAdd = () => {
    const filterRoutes = routes.filter((item) => item.stop_type === 2);
    if (filterRoutes.length === 3) {
      Taro.kbToast({
        text: '最多添加三个停靠点',
      });
      return;
    }
    // 从倒数第二的位置插入
    const newRoutes = addStopSortAndNotice([
      ...routes.slice(0, -1),
      { stop_type: 2 },
      routes[routes.length - 1],
    ]);
    onChange({
      routes: newRoutes,
    });
  };

  const handleRemove = (index) => {
    const newRoutes = addStopSortAndNotice([...routes.slice(0, index), ...routes.slice(index + 1)]);
    onChange({
      routes: newRoutes,
    });
  };

  const handleEdit = (index, type) => {
    const data = routes[index];
    const isStop = data.stop_type == 2;
    createListener(clientAddressEditKey, async (v) => {
      const { city } = location || {};
      if (!city) {
        setTimeout(() => {
          Taro.kbToast({
            text: '请选择当前地址',
          });
        }, 100);
        return;
      }
      if (v.city != city) {
        setTimeout(() => {
          Taro.kbToast({
            text: '请选择当前定位城市的地址',
          });
        }, 100);
        return;
      }
      const inServices = await checkAddressSupport(v);
      if (!inServices) {
        setTimeout(() => {
          Taro.kbToast({
            text: '抱歉，该地区未开通服务',
          });
        }, 100);
        return;
      }
      onChange({
        routes: addStopSortAndNotice([
          ...routes.slice(0, index),
          {
            ...data,
            ...v,
          },
          ...routes.slice(index + 1),
        ]),
      });
    });
    if (type == 'edit') {
      Taro.kbSetGlobalData(clientLocationKey, location);
    }
    Taro.navigator({
      url: type == 'edit' ? 'address/edit' : 'address',
      key: 'routerParamsChange',
      options: {
        data: {
          ...data,
          pageSource: 'index',
          label:
            type == 'edit'
              ? isStop
                ? '确定停靠点地址'
                : index == 0
                ? '确定装货点地址'
                : '确定卸货点地址'
              : '',
        },
      },
    });
  };

  const handleClear = (index) => {
    const newRoutes = addStopSortAndNotice([
      ...routes.slice(0, index),
      {
        ...clearAddressItem(routes[index]),
      },
      ...routes.slice(index + 1),
    ]);
    onChange({
      routes: newRoutes,
    });
  };

  useEffect(() => {
    if (location?.city && location?.city !== routes[0]?.city) {
      onChange({
        routes: [
          {
            stop_type: 1,
          },
          {
            stop_type: 3,
          },
        ],
      });
    }
  }, [location?.city]);

  useDidShowCom(() => {
    Taro.kbRemoveGlobalData(clientLocationKey);
  });

  return {
    routes,
    handleAdd,
    handleRemove,
    handleEdit,
    handleClear,
  };
};

export const clearAddressItem = (data) => {
  return Object.fromEntries(
    Object.entries(data).filter(([key]) => !clientAddressForm.includes(key)),
  );
};
