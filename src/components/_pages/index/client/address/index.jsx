/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';
import AddressItem from './item';
import { useAddressEdit } from './utils';

const Address = (props) => {
  const { routes, handleAdd, handleRemove, handleEdit, handleClear } = useAddressEdit(props);

  // const { inServices } = useCheckAddressSupport(routes[0]);

  return (
    <View
      className={classNames('kb-margin-md-t', {
        // 'kb-address--unservice': !inServices,
      })}
    >
      {/* {!inServices && (
        <AtNoticebar icon='amaze'>
          <Text className='kb-size__sm'>此线路暂未开通，若继续下单，预计会在1-2天内开通路线</Text>
        </AtNoticebar>
      )} */}
      <View className='kb-spacing-md-lr'>
        <View className='kb-box kb-spacing-md-lr kb-spacing-lg-tb'>
          {routes.map((item, index) => {
            return (
              <AddressItem
                key={index}
                data={item}
                handleRemove={() => handleRemove(index)}
                handleEdit={(type) => handleEdit(index, type)}
                handleClear={() => handleClear(index)}
              />
            );
          })}
          <View
            className='at-row at-row__align--center kb-spacing-lg-t kb-border-t'
            hoverClass='kb-hover-opacity'
            onClick={handleAdd}
          >
            <AtIcon prefixClass='kb-icon' value='plus-circle' className='kb-size__lg' />
            <View className='kb-spacing-md-l'>添加停靠点</View>
          </View>
        </View>
      </View>
    </View>
  );
};

Address.options = {
  addGlobalClass: true,
};

export default Address;
