import Taro from '@tarojs/taro';
import merge from 'lodash/merge';
import CacheMap from '~base/utils/cache';
import { randomCode } from '~base/utils/utils';

export const OrderCacheManagerKey = 'OrderCacheManagerKey';

const OrderCacheManager = new CacheMap({
  key: OrderCacheManagerKey,
});

// 下一步
export function orderNext(opts) {
  const { data, key, ...restNavigatorCfg } = opts || {};

  const { data: preData } = OrderCacheManager.get()?.data || {};
  const nextData = merge(preData, data);

  OrderCacheManager.set({
    data: nextData,
  });

  if (key) {
    restNavigatorCfg.options = {
      data: nextData,
    };
  }

  Taro.navigator({
    key,
    ...restNavigatorCfg,
  });
}

// 下单结束
export function orderDone(){
  OrderCacheManager.delete();
}
