/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import classNames from 'classnames';
import { createListener } from '~base/utils/utils';
import './index.scss';

const othersList = [
  {
    label: '备注',
    key: 'note',
    url: 'index/notice',
  },
  {
    label: '联系电话',
    key: 'contact_phone',
    url: 'index/mobile',
  },
];
const Others = (props) => {
  const { data, onChange, readOnly, className } = props;

  const handleEdit = ({ key, url }) => {
    if (readOnly) return;
    createListener(`${key}-edit`, (v) => {
      onChange({
        [key]: v,
      });
    });
    Taro.navigator({
      url,
      key: 'routerParamsChange',
      options: {
        data: data[key],
      },
    });
  };

  return (
    <View className={classNames('kb-spacing-md-lr kb-spacing-md-t', className)}>
      <View className='kb-box'>
        {othersList.map((item) => (
          <View
            className='kb-navigator kb-navigator-r'
            key={item.key}
            hoverClass={readOnly ? '' : 'kb-hover-opacity'}
            onClick={() => handleEdit(item)}
          >
            <View className='kb-navigator__label'>{item.label}</View>
            <View
              className={classNames({
                'kb-navigator__value': !!data[item.key],
                'kb-navigator__desc': !data[item.key],
              })}
            >
              {data[item.key] || '请输入'}
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

Others.options = {
  addGlobalClass: true,
};

export default Others;
