/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  getCarTypeListIgnoreBrand,
  getRentDayListIgnoreBrand,
  getTruckTypeListIgnoreBrand,
  getTruckVolumeListIgnoreBrand,
} from '~/services/truck/add';
import { getSupportLocation } from '~/services/user/address';
import { useForm } from '~base/hooks/form';
import { usePostMessage } from '~base/hooks/page';
import { useRequest } from '~base/utils/request/hooks';

export function getGoodsTypeList() {
  return new Promise((resolve) => {
    resolve([
      {
        label: '快递包裹',
        value: '快递包裹',
      },
      {
        label: '零售包裹',
        value: '零售包裹',
      },
      {
        label: '生活用品',
        value: '生活用品',
      },
      {
        label: '其他物品',
        value: '其他物品',
      },
    ]);
    // request({
    //   url: '',
    //   toastLoading: false,
    //   data,
    //   onThen: (res) => {
    //     const { data } = res;
    //     resolve({
    //       ...res,
    //       data: isArray(data) ? data.map((item) => ({ label: item.name, value: item.id })) : [],
    //     });
    //   },
    // });
  });
}

export const useOrderConfig = (formData) => {
  const { vehicle_type: selectVehicleType, car_type: selectCarType } = formData || {};

  const { data: car_type } = useRequest(getCarTypeListIgnoreBrand);

  const { data: vehicle_type, run: runTruckTypeList } = useRequest(getTruckTypeListIgnoreBrand, {
    manual: true,
  });
  const { data: goods_volume, run: runTruckVolumeList } = useRequest(
    getTruckVolumeListIgnoreBrand,
    { manual: true },
  );
  const { data: rental_money } = useRequest(getRentDayListIgnoreBrand);

  const { data: goods_name } = useRequest(getGoodsTypeList);

  useEffect(() => {
    if (selectVehicleType) {
      runTruckVolumeList({ type: selectVehicleType });
    }
  }, [selectVehicleType]);

  useEffect(() => {
    if (selectCarType) {
      runTruckTypeList({ vehicle_type: selectCarType });
    }
  }, [selectCarType]);

  return {
    vehicle_type,
    goods_volume,
    goods_name,
    car_type,
    rental_money,
  };
};

export const getDefaultRentalTime = () => {
  const now = dayjs();
  const nextHour = now.add(1, 'hour').startOf('hour');
  const endTime = nextHour.add(4, 'hour');
  return [nextHour.format('YYYY-MM-DD HH:mm:ss'), endTime.format('YYYY-MM-DD HH:mm:ss')];
};

export const useClientForm = () => {
  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo: { mobile } = {} } = loginData || {};
  const { tab, onChangeTab, tabs } = useClientHomeTab();

  const formConfig = useMemo(() => {
    const isRental = tab == 1;
    return {
      form: {
        vehicle_type: {
          value: '',
          required: true,
        },
        car_type: {
          value: '',
          required: isRental,
        },
        goods_name: {
          value: '',
          required: !isRental,
        },
        goods_volume: {
          value: '',
          required: true,
        },
        routes: {
          value: [
            {
              stop_type: 1,
            },
            {
              stop_type: 3,
            },
          ],
          required: true,
        },
        note: {
          value: '',
        },
        contact_phone: {
          value: mobile,
          required: true,
        },
        rental_money: {
          value: '',
          required: isRental,
        },
        rental_address: {
          value: { stop_type: 4 },
          required: isRental,
        },
        rental_time: {
          value: getDefaultRentalTime(),
          required: isRental,
        },
      },
    };
  }, [mobile, tab]);
  const {
    data: { data },
    setFieldsValue,
  } = useForm(formConfig);

  usePostMessage(
    (key, data) => {
      const { params } = data;
      if (key === 'routerParamsChange') {
        const { action } = params;
        if (action === 'rent') {
          // 切换为租车
          onChangeTab(1);
        }
        setFieldsValue({
          ...params,
          contact_phone: params.sender_phone,
        });
      }
    },
    [setFieldsValue],
  );

  const { vehicle_type, car_type } = useOrderConfig(data);

  useEffect(() => {
    car_type?.length && setFieldsValue({ car_type: car_type[0].value });
  }, [car_type, tab]);

  useEffect(() => {
    vehicle_type?.length && setFieldsValue({ vehicle_type: vehicle_type[0].value });
  }, [vehicle_type, tab]);

  return {
    data,
    onChange: setFieldsValue,
    tab,
    tabs,
    onChangeTab,
  };
};

export const checkAddressSupport = (data) => {
  return new Promise(async (resolve) => {
    const { city, district } = data || {};
    if (!city || !district) return resolve(true);
    const supportData = await getSupportLocation();
    if (
      supportData.find((item) => {
        const [_city, _district] = item.split('-');
        return (
          (_city.indexOf(city) > -1 && _district.indexOf(district) > -1) ||
          (city.indexOf(_city) > -1 && district.indexOf(_district) > -1)
        );
      })
    ) {
      resolve(true);
    } else {
      resolve(false);
    }
  });
};

export const useCheckAddressSupport = (data) => {
  const [inServices, setInServices] = useState(true);

  useEffect(() => {
    if (!data) return;
    checkAddressSupport(data).then((res) => {
      setInServices(res);
    });
  }, [data]);

  return { inServices };
};

export const useClientHomeTab = () => {
  const [tab, setTab] = useState(0);
  const tabs = [
    {
      label: '即时用车',
      value: 0,
    },
    {
      label: '租车',
      value: 1,
    },
  ];

  return {
    tab,
    onChangeTab: setTab,
    tabs,
  };
};
