/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { getShopDetail } from '~/services/user/shop';
import { useRequest } from '~base/utils/request/hooks';
import './index.scss';

const Entity = () => {
  const { data } = useRequest(getShopDetail);

  return (
    <>
      {data?.name && (
        <View className='kb-entity-wrapper'>
          <View className='kb-entity'>
            <View className='kb-entity-title'>{data?.name}</View>
            <View className='kb-color__grey kb-margin-sm-t kb-size__sm'>{data?.address}</View>
          </View>
        </View>
      )}
    </>
  );
};

Entity.options = {
  addGlobalClass: true,
};

export default Entity;
