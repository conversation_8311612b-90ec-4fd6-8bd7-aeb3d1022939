/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useMemo } from 'react';

export const useExtraList = (tab) => {
  const list = useMemo(() => {
    const isRental = tab == 1;
    return [
      {
        label: '车辆类型',
        key: 'car_type',
      },
      {
        label: '车厢类型',
        key: 'vehicle_type',
      },
      {
        label: '装载容积',
        key: 'goods_volume',
      },
      isRental
        ? {
            label: '日租金',
            key: 'rental_money',
          }
        : {
            label: '配送物品',
            key: 'goods_name',
          },
    ];
  }, [tab]);
  return {
    list,
  };
};
