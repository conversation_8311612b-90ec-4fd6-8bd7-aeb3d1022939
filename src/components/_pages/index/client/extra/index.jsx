/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbListBox from '@/components/_pages/order/listbox';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useOrderConfig } from '../utils';
import './index.scss';
import { useExtraList } from './utils';

const Extra = (props) => {
  const { data, onChange, tab } = props;
  const { list: extraList } = useExtraList(tab);
  const defaultConfig = useOrderConfig(data);

  const handleChange = (key, v) => onChange?.({ [key]: v.value });

  return (
    <View className='kb-spacing-md-lr kb-spacing-md-t'>
      <View className='kb-box kb-spacing-md-lr'>
        {extraList.map((item, index) => (
          <View
            className={classNames('kb-spacing-md-tb', {
              'kb-border-b': index !== extraList.length - 1,
            })}
            key={item.key}
          >
            <View className='kb-size__base2'>{item.label}</View>
            <View>
              <KbListBox
                className='kb-package__base'
                selectedActive='ghost'
                list={defaultConfig?.[item.key]}
                onChange={(v) => handleChange(item.key, v)}
                selectted={
                  item.key !== 'goods_name'
                    ? defaultConfig?.[item.key]?.find((v) => v.value == data[item.key])?.label
                    : data[item.key]
                }
              />
            </View>
          </View>
        ))}
      </View>
    </View>
  );
};

Extra.options = {
  addGlobalClass: true,
};

export default Extra;
