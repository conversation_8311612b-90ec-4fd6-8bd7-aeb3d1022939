/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-client {
  &-type {
    box-sizing: border-box;
    padding: $spacing-h-md;
  }
  &-tabs {
    flex: 1;
    gap: $spacing-h-md;
    box-sizing: border-box;
    width: unset;
    &__button {
      height: 64px;
      margin: 0;
      line-height: 64px;
      &-sm {
        width: 156px;
      }
    }
    .at-button--secondary {
      color: $color-grey-0;
      background-color: rgba(231, 249, 252, 1);
      border-color: transparent;
    }
    &__book {
      width: 160px;
      height: 44px;
      color: $color-grey-0;
      font-size: 22px;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 23px 23px 23px 23px;
    }
  }
}
