/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { AtButton, AtIcon } from 'taro-ui';
import Taro from '@tarojs/taro';
import './index.scss';

const Tabs = (props) => {
  const { active, onChange, tabs } = props;

  return (
    <View className='at-row at-row__align--center at-row__justify--between kb-client-type'>
      <View className='at-row at-row__align--center kb-client-tabs'>
        {tabs.map((tab, index) => (
          <AtButton
            type={active === tab.value ? 'primary' : 'secondary'}
            circle
            size='small'
            key={tab.value}
            className={classNames('kb-client-tabs__button', {
              'kb-client-tabs__button-sm': index === 1,
            })}
            onClick={() => onChange(tab.value)}
          >
            {tab.label}
          </AtButton>
        ))}
      </View>
      <View
        className='kb-client-tabs__book at-row at-row__align--center at-row__justify--center'
        hoverClass='kb-hover'
        onClick={() => Taro.navigateToDocument('guide')}
      >
        <AtIcon
          prefixClass='kb-icon'
          value='book'
          className='kb-color__brand kb-size__base kb-margin-sm-r'
        />
        <Text>使用教程</Text>
      </View>
    </View>
  );
};

Tabs.options = {
  addGlobalClass: true,
};

export default Tabs;
