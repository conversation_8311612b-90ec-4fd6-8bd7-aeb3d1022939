/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-location {
  &__img {
    width: 40px;
    height: 40px;
    margin-right: $spacing-h-xs;
  }
  &__icon {
    margin-left: $spacing-h-sm;
    transform: rotate(90deg);
  }
}
.notInServices {
  width: 126px;
  height: 30px;
  color: #ed6a0c;
  font-size: $font-size-xs;
  line-height: 30px;
  text-align: center;
  border: $width-base solid #ed6a0c;
  border-radius: $border-radius-md;
  margin-left: 44px;
}
