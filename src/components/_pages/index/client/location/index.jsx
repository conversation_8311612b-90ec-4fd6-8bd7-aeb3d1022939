/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useEffect, useMemo, useState } from 'react';
import { AtIcon } from 'taro-ui';
import { getLocationDesc, useLocation } from '~base/components/location/_utils/useLocation';
import { getLocationByLat } from '~/services/user/address';
import { useUpdate } from '~base/hooks/page';
import { useRequest } from '~base/utils/request/hooks';
import { createListener } from '~base/utils/utils';
import { useCheckAddressSupport } from '../utils';
import './index.scss';

const Location = (props) => {
  const { onGetLocation, location: locationProps } = props;
  const [location, setLocation] = useState();
  const { locationInfo } = useLocation();

  const { data, run } = useRequest(getLocationByLat, {
    manual: true,
  });

  useEffect(() => {
    if (data?.province) {
      setLocation(data);
      onGetLocation(data);
    }
  }, [data]);

  useUpdate(
    (loginData) => {
      if (!loginData.logined || !locationInfo) return;
      run({
        lat: locationInfo.latitude,
        lon: locationInfo.longitude,
      });
    },
    [locationInfo],
  );

  const handleChooseLocation = () => {
    createListener('locationChange', (value) => {
      setLocation(value);
      onGetLocation(value);
    });
    Taro.navigator({
      url: 'address/choose',
      options: {
        page: 'serviceIndex',
        ...locationProps,
      },
    });
  };

  const { inServices } = useCheckAddressSupport(location);

  const label = useMemo(() => {
    return getLocationDesc(location);
  }, [location]);

  return (
    <View>
      <View
        className='at-row at-row__align--center'
        hoverClass='kb-hover'
        onClick={handleChooseLocation}
      >
        <Image
          className='kb-location__img'
          src='https://cdn-img.kuaidihelp.com/truck/location.png'
        />
        <Text className='kb-location__text'>{label}</Text>
        <AtIcon prefixClass='kb-icon' value='arrow' className='kb-size__sm kb-location__icon' />
      </View>
      {!inServices && <View className='notInServices'>地区无服务</View>}
    </View>
  );
};

Location.options = {
  addGlobalClass: true,
};

export default Location;
