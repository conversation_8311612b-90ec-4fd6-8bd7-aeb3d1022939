/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
// import RentalAddress from './address';
import RentalTime from './timeRange';

const Rental = (props) => {
  return (
    <View className='kb-spacing-md-lr kb-spacing-md-t'>
      <View className='kb-box kb-spacing-md-lr'>
        <RentalTime {...props} />
        {/* <RentalAddress {...props} /> */}
      </View>
    </View>
  );
};

Rental.options = {
  addGlobalClass: true,
};

export default Rental;
