/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import AddressItem from '../../address/item';
import { useAddressRental } from './utils';

const RentalAddress = (props) => {
  const { data, handleEdit, handleClear } = useAddressRental(props);
  return <AddressItem data={data} handleEdit={handleEdit} handleClear={handleClear} />;
};

export default RentalAddress;
