/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { createListener } from '~base/utils/utils';
import { clientAddressEditKey, clientLocationKey } from '../../address/utils';

export const useAddressRental = (props) => {
  const { data, onChange, location } = props;
  const { rental_address } = data || {};

  const handleEdit = (type) => {
    createListener(clientAddressEditKey, async (v) => {
      const { city } = location || {};
      if (!city) {
        setTimeout(() => {
          Taro.kbToast({
            text: '请选择当前地址',
          });
        }, 100);
        return;
      }
      if (v.city != city && process.env.NODE_ENV !== 'development') {
        setTimeout(() => {
          Taro.kbToast({
            text: '请选择当前定位城市的地址',
          });
        }, 100);
        return;
      }
      onChange({
        rental_address: {
          ...rental_address,
          ...v,
        },
      });
    });
    if (type == 'edit') {
      Taro.kbSetGlobalData(clientLocationKey, location);
    }
    Taro.navigator({
      url: type == 'edit' ? 'address/edit' : 'address',
      key: 'routerParamsChange',
      options: {
        data: {
          ...data,
          pageSource: 'index',
          label: '确定收车地址',
        },
      },
    });
  };

  const handleClear = () => {
    onChange({ rental_address: { stop_type: 4 } });
  };

  return {
    data: rental_address,
    handleEdit,
    handleClear,
  };
};
