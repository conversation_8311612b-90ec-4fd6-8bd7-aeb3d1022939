/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { RootPortal, View } from '@tarojs/components';
import dayjs from 'dayjs';
import { forwardRef, useImperativeHandle, useMemo, useState } from 'react';
import { AtFloatLayout, AtIcon } from 'taro-ui';
import DatePicker from '~base/components/picker/datePicker';
import './picker.scss';
import { validateTimeRange } from './utils';

const RentalTimePicker = forwardRef((props, ref) => {
  const { value, readOnly } = props;
  const [open, setOpen] = useState(false);
  const [conf, setConf] = useState({
    title: '',
    index: 0,
  });
  const startYear = dayjs().year();
  const endYear = startYear + 1;

  const handleOpen = (open, index) => {
    if (readOnly) return;
    if (open) {
      setConf({
        title: index === 0 ? '起租时间' : '结束时间',
        index,
      });
    }
    setOpen(open);
  };

  const onDatePickerChange = (v) => {
    if (readOnly) return;
    if (!validateTimeRange({ selectedValue: v.value, index: conf.index, value })) {
      return;
    }
    props.onChange(conf.index, v.value);
  };

  const dateValue = useMemo(() => {
    return value[conf.index];
  }, [conf.index, value]);

  useImperativeHandle(ref, () => ({
    handleOpen,
  }));

  return (
    <RootPortal>
      <AtFloatLayout isOpened={open} onClose={() => handleOpen(false)}>
        <View className='timepicker-header'>
          <View>{conf.title}</View>
          <View hoverClass='kb-hover-opacity' onClick={() => handleOpen(false)}>
            <AtIcon prefixClass='kb-icon' value='add' className='timepicker-close' />
          </View>
        </View>
        <DatePicker
          immediateChange
          fields='hour'
          value={dateValue}
          change={onDatePickerChange}
          startYear={startYear}
          endYear={endYear}
        />
      </AtFloatLayout>
    </RootPortal>
  );
});

export default RentalTimePicker;
