/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useRef } from 'react';
import { noop } from '~base/utils/utils';

export const useRentalTime = (props) => {
  const { data, onChange = noop, readOnly } = props;
  const { rental_time = [] } = data || {};
  const pickerRef = useRef(null);

  const onDateChange = (i, v) => {
    const _date = [...rental_time];
    _date[i] = v;
    onChange({ rental_time: _date });
  };

  const handleOpenPicker = (i) => {
    if (readOnly) return;
    pickerRef.current.handleOpen(true, i);
  };

  return {
    value: rental_time,
    pickerRef,
    onChange: onDateChange,
    handleOpenPicker,
    readOnly,
  };
};

export const dateFormat = (date) => {
  const timeFormat = dayjs(date).format('HH:mm');
  const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  let time;
  const isToday = dayjs().isSame(date, 'day');
  const isYesterday = dayjs().subtract(1, 'day').isSame(date, 'day');
  const isTomorrow = dayjs().add(1, 'day').isSame(date, 'day');
  if (isToday) {
    time = `今天 ${timeFormat}`;
  } else if (isYesterday) {
    time = `昨天 ${timeFormat}`;
  } else if (isTomorrow) {
    time = `明天 ${timeFormat}`;
  } else {
    time = `${week[dayjs(date).day()]} ${timeFormat}`;
  }

  return {
    year: dayjs(date).year(),
    month: dayjs(date).month() + 1,
    day: dayjs(date).date(),
    hour: dayjs(date).hour(),
    date: `${dayjs(date).month() + 1}月${dayjs(date).date()}日`,
    time,
  };
};

// 时间间隔
export const timeInterval = (date) => {
  const [start, end] = date;
  const startDate = dayjs(start);
  const endDate = dayjs(end);
  const interval = endDate.diff(startDate, 'day');
  const totalHour = endDate.diff(startDate, 'hour');
  const hour = totalHour % 24;
  // return `${interval ? `${interval}天` : ''}${hour ? ` ${hour}小时` : ''}`;
  return {
    day: interval,
    hour,
  };
};

// 时间验证配置常量
const TIME_VALIDATION_CONFIG = {
  MIN_HOURS: 4,
  MAX_DAYS: 60,
  MESSAGES: {
    START_TIME_PAST: '起租时间不能早于当前时间',
    START_TIME_AFTER_END: '开始时间不能晚于结束时间',
    END_TIME_BEFORE_START: '结束时间不能早于开始时间',
    MIN_INTERVAL: '时间间隔不能小于4小时',
    MAX_INTERVAL: '时间间隔最长不能超过60天',
  },
};

/**
 * 验证时间间隔是否符合要求
 * @param {dayjs.Dayjs} startTime 开始时间
 * @param {dayjs.Dayjs} endTime 结束时间
 * @returns {string|null} 错误信息，null表示验证通过
 */
const validateTimeInterval = (startTime, endTime) => {
  const diffHours = endTime.diff(startTime, 'hour');
  const diffDays = endTime.diff(startTime, 'day');

  if (diffHours < TIME_VALIDATION_CONFIG.MIN_HOURS) {
    return TIME_VALIDATION_CONFIG.MESSAGES.MIN_INTERVAL;
  }

  if (diffDays > TIME_VALIDATION_CONFIG.MAX_DAYS) {
    return TIME_VALIDATION_CONFIG.MESSAGES.MAX_INTERVAL;
  }

  return null;
};

/**
 * 验证开始时间
 * @param {dayjs.Dayjs} selectedTime 选中的时间
 * @param {dayjs.Dayjs} now 当前时间
 * @param {dayjs.Dayjs|null} currentEndTime 当前结束时间
 * @returns {string|null} 错误信息，null表示验证通过
 */
const validateStartTime = (selectedTime, now, currentEndTime) => {
  // 起租时间不能早于当前时间
  if (selectedTime.isBefore(now)) {
    return TIME_VALIDATION_CONFIG.MESSAGES.START_TIME_PAST;
  }

  // 如果已有结束时间，进行相关验证
  if (currentEndTime) {
    if (selectedTime.isAfter(currentEndTime)) {
      return TIME_VALIDATION_CONFIG.MESSAGES.START_TIME_AFTER_END;
    }

    // 验证时间间隔
    return validateTimeInterval(selectedTime, currentEndTime);
  }

  return null;
};

/**
 * 验证结束时间
 * @param {dayjs.Dayjs} selectedTime 选中的时间
 * @param {dayjs.Dayjs|null} currentStartTime 当前开始时间
 * @returns {string|null} 错误信息，null表示验证通过
 */
const validateEndTime = (selectedTime, currentStartTime) => {
  if (!currentStartTime) return null;

  // 结束时间不能早于开始时间
  if (selectedTime.isBefore(currentStartTime)) {
    return TIME_VALIDATION_CONFIG.MESSAGES.END_TIME_BEFORE_START;
  }

  // 验证时间间隔
  return validateTimeInterval(currentStartTime, selectedTime);
};

export const validateTimeRange = ({ selectedValue, index, value }) => {
  const now = dayjs();
  const selectedTime = dayjs(selectedValue);
  const currentStartTime = value[0] ? dayjs(value[0]) : null;
  const currentEndTime = value[1] ? dayjs(value[1]) : null;

  let errorMessage = null;

  if (index === 0) {
    // 验证开始时间
    errorMessage = validateStartTime(selectedTime, now, currentEndTime);
  } else if (index === 1) {
    // 验证结束时间
    errorMessage = validateEndTime(selectedTime, currentStartTime);
  }

  if (errorMessage) {
    Taro.kbToast({ text: errorMessage });
    return false;
  }

  return true;
};
