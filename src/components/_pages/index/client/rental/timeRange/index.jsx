/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import './index.scss';
import RentalTimePicker from './picker';
import { dateFormat, timeInterval, useRentalTime } from './utils';

const RentalTime = (props) => {
  const { value, pickerRef, onChange, handleOpenPicker, readOnly } = useRentalTime(props);

  return (
    <View className='kb-spacing-md-tb kb-range-box'>
      <View className='at-row at-row__align--center at-row__justify--between'>
        {['起租日期', '', '结束日期'].map((item, index) => {
          const _index = index == 0 ? 0 : 1;
          const timeLabel = value[_index] ? dateFormat(value[_index]) : {};
          const timeInterval_day = timeInterval(value)?.day;
          const timeInterval_hour = timeInterval(value)?.hour;
          return index == 1 ? (
            <View
              key={item}
              className='kb-range-arrow at-row at-row__direction--column at-row__align--center at-row__justify--center'
            >
              <View className='kb-text__sm kb-margin-sm-b'>
                共
                {!!timeInterval_day && (
                  <>
                    <Text className='kb-color__orange'>{timeInterval_day}</Text>天
                  </>
                )}
                {!!timeInterval_hour && (
                  <>
                    <Text className='kb-color__orange'>{timeInterval_hour}</Text>小时
                  </>
                )}
              </View>
              <AtIcon prefixClass='kb-icon' value='arrow-long' />
            </View>
          ) : (
            <View
              className='kb-range-item'
              key={item}
              hoverClass={readOnly ? '' : 'kb-hover-opacity'}
              onClick={() => handleOpenPicker(_index)}
            >
              <View
                className={classNames('at-row at-row__align--center kb-margin-md-b', {
                  'at-row__justify--end': index == 2,
                })}
              >
                <View className='kb-size__xl'>{timeLabel?.date || item}</View>
                {!readOnly && (
                  <AtIcon
                    prefixClass='kb-icon'
                    value='arrow'
                    className='kb-size__sm kb-margin-sm-l kb-color__black'
                  />
                )}
              </View>
              <View
                className={`kb-size__sm kb-color__grey kb-text__${index == 0 ? 'left' : 'right'}`}
              >
                {timeLabel?.time || '--'}
              </View>
            </View>
          );
        })}
      </View>
      <RentalTimePicker ref={pickerRef} value={value} onChange={onChange} readOnly={readOnly} />
    </View>
  );
};

RentalTime.options = {
  addGlobalClass: true,
};

export default RentalTime;
