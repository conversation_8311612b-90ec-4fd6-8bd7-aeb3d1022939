/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { AtButton } from 'taro-ui';
import { orderNext } from '../_utils/orderProcess';

const Footer = (props) => {
  const { data, tab } = props;

  // // 检查两个地址是否相同
  // const isSameAddress = (addr1, addr2) =>
  //   addr1.address === addr2.address &&
  //   addr1.city === addr2.city &&
  //   addr1.landmark === addr2.landmark &&
  //   addr1.district === addr2.district &&
  //   addr1.province === addr2.province;

  // 下一步
  const handleNext = () => {
    if (tab == 1) {
      if (dayjs(data.rental_time[0]).isBefore(dayjs())) {
        Taro.kbToast({ text: '起租时间不能早于当前时间' });
        return;
      }
      // const gapDay = timeInterval(data.rental_time).day;
      // if (gapDay > 99999) {
      //   Taro.kbToast({ text: '车辆租赁时间过长，请调整时间' });
      //   return;
      // }
    }
    const orderUseType = tab == 1 ? 'rental' : 'normal';
    orderNext({
      url: 'user/partner',
      options: {
        action: 'choose',
        orderUseType,
      },
      data: {
        ...data,
        orderUseType,
      },
    });
  };

  const disabled = useMemo(() => {
    if (!data) return true;
    if (tab == 1) {
      return (
        !data.car_type ||
        !data.vehicle_type ||
        !data.goods_volume ||
        !data.rental_time ||
        !data.rental_money ||
        !data.contact_phone
      );
    }
    return !data.car_type || !data.vehicle_type || !data.goods_volume || !data.goods_name;
  }, [data, tab]);

  return data ? (
    <View className='kb-spacing-md kb-background__white'>
      <AtButton
        type='primary'
        circle
        className='kb-button__base'
        onClick={handleNext}
        disabled={disabled}
      >
        下一步
      </AtButton>
    </View>
  ) : null;
};

Footer.options = {
  addGlobalClass: true,
};
export default Footer;
