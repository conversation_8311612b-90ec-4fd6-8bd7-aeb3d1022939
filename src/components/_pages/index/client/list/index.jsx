/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useBoundingClientRect, useObserver } from '@base/hooks/observer';
import { getPage, isAvailableValue } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { Fragment, useCallback, useEffect, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtTabs, AtTabsPane } from 'taro-ui';
import KbListContent from './list-content';
import './index.scss';

const Index = (props) => {
  const { loadMore, active, topID, mode, onGet, ...rest } = props;

  // 更新翻页
  const [loadMore_, updateLoadMore] = useState(null);
  // 更新统计数据
  const [stateCounts, updateStateCounts] = useState(0);
  // 状态切换
  const [current, updateCurrent] = useState(0);
  // 状态切换标签配置
  const tabs = [
    // {
    //   title: '待取件',
    //   // count: true,
    //   key: 'inn_pickup',
    // },
    // {
    //   title: '已取件',
    //   key: 'inn_signed',
    // },
    {
      title: '全部',
      key: 'inn_all',
    },
  ];

  // 切换状态
  const handleSwitchTab = useCallback((current) => {
    // 清空翻页状态，防止切换其他tab引发翻页错误
    updateLoadMore(null);
    updateCurrent(current);
  }, []);

  // @微快递，设置统计
  const handleSetStateCount = useCallback(
    (_, res, req) => {
      if (process.env.MODE_ENV === 'wkd') {
        if (req.pageNum > 1) {
          return;
        }
        const { data: { count } = {} } = res;
        updateStateCounts(count);
      }
      if (onGet) {
        onGet(_, res, req);
      }
    },
    [onGet],
  );

  // 监听更新翻页状态
  useEffect(() => {
    updateLoadMore(loadMore);
  }, [loadMore]);

  // 监听状态切换栏当前位置
  const { customNavBarHeight } = getPage();

  return (
    <View className='kb-query__list'>
      <View className='kb-query__tab--box'>
        <View className='kb-query__tab at-row at-row__align--center at-row__justify--between'>
          <View className='at-col'>
            <View className='at-row'>
              {tabs.map((item, index) => {
                const itemCls = classNames('kb-query__tab--item', {
                  'kb-query__tab--active': index === current,
                });
                return (
                  <View
                    key={item.key}
                    className={itemCls}
                    hoverClass='kb-hover'
                    onClick={handleSwitchTab.bind(null, index)}
                  >
                    {item.title}
                    {item.count && stateCounts > 0 && (
                      <Fragment>
                        (<Text className='kb-color__red'>{stateCounts}</Text>)
                      </Fragment>
                    )}
                  </View>
                );
              })}
            </View>
          </View>
        </View>
      </View>
      <AtTabs
        tabList={tabs}
        className='kb-tabs__hidetab'
        onClick={handleSwitchTab}
        current={current}
        height='auto'
        swipeable={false}
      >
        {tabs.map((item, index) => (
          <AtTabsPane key={item.key} current={current} index={index}>
            <View className='kb-spacing-md-lr'>
              <KbListContent
                {...rest}
                active={current === index && active}
                loadMore={current === index && loadMore_}
                type={item.key}
                onGetted={handleSetStateCount}
                show={current === index}
              />
            </View>
          </AtTabsPane>
        ))}
      </AtTabs>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  topID: '',
  dakId: null,
  mode: 'normal',
};

export default Index;
