/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLongList from '@base/components/long-list';
import { dateCalendar, noop } from '@base/utils/utils';
import { Text, View } from '@tarojs/components';
import { useEffect, useRef } from '@tarojs/taro';
import classNames from 'classnames';
import CabinetOpen from '~/components/_pages/cabinetOpen/cabinetOpen';
// import { padZero } from '~/components/_pages/kdg/_utils';
import TruckStatus from '~/components/_pages/truck/list/item/status';
import { queryList, reportAfterPickup } from '~/services/query';
import { useLongList } from '~base/components/long-list/hooks';
import './list-content.scss';

const Index = (props) => {
  const { show, height, active, enableRefresh, loadMore, onGetted, onListReady } = props;

  const actionRef = useRef({});

  const { list, config: listData } = useLongList(queryList, {
    api: {
      onThen: (list, res, req) => {
        onGetted(list, res, req);
      },
    },
  });

  // 列表准备就绪
  const handleLongListReady = (ins) => {
    actionRef.current.listIns = ins;
    onListReady(ins);
  };

  // 加载更多
  useEffect(() => {
    // 通过父组件触发翻页
    if (loadMore) {
      actionRef.current.listIns.more();
    }
  }, [loadMore]);

  // 刷新
  const handleRefresh = (item) => {
    if (actionRef.current.listIns) {
      actionRef.current.listIns.loader();
    }
    reportAfterPickup(item);
  };

  return (
    <KbLongList
      active={active}
      data={listData}
      enableMore={false}
      enableRefresh={enableRefresh}
      scrollY={height !== 'auto'}
      onReady={handleLongListReady}
      height={height}
      topSpaceFix
    >
      {show &&
        list.map((item) => (
          <View key={item.id || item.car_id} className='kb-list-content__item'>
            {
              // 有车辆id，兼容不按车辆分组的情况
              item.car_id ? (
                <View className='kb-spacing-md-tb kb-border-b'>
                  <View className='at-row at-row__align--center at-row__justify--between'>
                    <View className='kb-size__xl kb-text__bold'>{item.license_plate}</View>
                    <TruckStatus data={item} />
                  </View>
                  <View className='at-row at-row__align--center at-row__justify--between'>
                    <View className='kb-color__grey kb-size__base '>{item.car_name}</View>
                    <View className='kb-color__grey kb-size__base'>{item.station_name}</View>
                  </View>
                </View>
              ) : null
            }
            {item.pickup_list?.map((iitem, index) => (
              <View
                key={index}
                className={classNames('kb-spacing-lg-tb', {
                  'kb-border-t': index != 0,
                })}
              >
                <View className='at-row at-row__align--center at-row__justify--between'>
                  <View className='kb-size__lg'>{iitem.shop_name}</View>
                  <View className='kb-size__base kb-color__grey'>
                    {dateCalendar(iitem.create_time)}
                  </View>
                </View>
                <View
                  key={index}
                  className='at-row at-row__align--center at-row__justify--between kb-spacing-md-t'
                >
                  <View className='kb-size__base kb-color__grey'>
                    {/* {iitem.grid_row
                      ? `${padZero(iitem.grid_row)}号柜 ${padZero(iitem.grid_row)}${padZero(
                          iitem.grid_col,
                        )}`
                      : ''} */}
                    {iitem.grid_number && `格口号${iitem.grid_number}`} | {iitem.pickup_code}
                    <Text
                      className={classNames('kb-color__grey kb-size__sm kb-margin-sm-l', {
                        'kb-status1': iitem.status == 1,
                        'kb-status2': iitem.status == 2,
                        'kb-status3': iitem.status == 3,
                      })}
                    >
                      {iitem.status == 0
                        ? '取消投递'
                        : iitem.status == 1
                        ? '待取件'
                        : iitem.status == 2
                        ? '已取件'
                        : iitem.status == 3
                        ? '滞留件'
                        : ''}
                    </Text>
                  </View>
                  {iitem.show_open_button && (
                    <CabinetOpen
                      data={{
                        car_cabinet_id: item.car_id,
                        device_id: item.device_id,
                        number: iitem.grid_number,
                        row: iitem.grid_row,
                        col: iitem.grid_col,
                        column: iitem.grid_column,
                        size: iitem.gird_size,
                      }}
                      updateList={() =>
                        handleRefresh({
                          car_cabinet_id: item.car_id,
                          order_id: iitem.order_id,
                        })
                      }
                    />
                  )}
                </View>
              </View>
            ))}
          </View>
        ))}
    </KbLongList>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  dakId: null,
  brands: {},
  type: 'all',
  enableRefresh: true,
  show: true,
  height: 'auto',
  onGetted: noop,
  onReady: noop,
  onListReady: noop,
};

export default Index;
