/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbScrollView from '@base/components/scroll-view';
import { View } from '@tarojs/components';
// import Address from './address';
import Extra from './extra';
import Footer from './footer';
import './index.scss';
import Others from './others';
import Rental from './rental';
import Tabs from './tabs';
import { useClientForm } from './utils';

const ClientIndex = (props) => {
  const { location } = props;
  const formConfig = useClientForm();
  const { tab, onChangeTab, tabs } = formConfig;

  return (
    <KbScrollView
      scrollY
      className='kb-scrollview kb-hide__footer'
      renderFooter={process.env.MODE_ENV === 'client' && <Footer {...formConfig} />}
    >
      <View className='kb-spacing-md-b'>
        <Tabs active={tab} onChange={onChangeTab} tabs={tabs} />
        <Extra {...formConfig} />
        {/* <Address location={location} {...formConfig} /> */}
        {tab == 1 && <Rental location={location} {...formConfig} />}
        <Others {...formConfig} />
      </View>
    </KbScrollView>
  );
};

export default ClientIndex;
