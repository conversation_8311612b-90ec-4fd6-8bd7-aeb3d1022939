/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { useMemo } from 'react';
import { AtIcon } from 'taro-ui';
import Kb<PERSON>ongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import './list.scss';

const DeliverRecordList = (props) => {
  const { active, search, car_cabinet_id } = props;

  const { list, config } = useLongList('/g_lqm/api/driverlesscar/Order/carOrderList', {
    pageKey: 'page',
  });

  const activeParams = useMemo(() => {
    return {
      ...active,
      search: search,
      car_cabinet_id,
    };
  }, [active, search, car_cabinet_id]);

  return (
    <KbLongList active={activeParams} data={config} enableMore>
      <View className='kb-record__list'>
        {list.map((item, index) => (
          <View key={item.id}>
            <View
              className={classNames('kb-record__item', {
                'kb-border-t': index != 0,
              })}
            >
              <View className='at-row at-row__justify--between'>
                <View>
                  <Text className='kb-size__lg kb-margin-sm-r'>{item.shop_name} </Text>
                  <Text className='kb-size__base kb-color__grey'>
                    {item.number ? `#${item.number}` : ''}
                  </Text>
                </View>
                <View className='kb-color__brand kb-size__lg'>{item.pickup_code}</View>
              </View>
              <View className='at-row at-row__justify--between kb-color__grey'>
                <Text>{item.mobile}</Text>
                <Text className='kb-size__sm'>{item.create_time}</Text>
              </View>
              <View className='at-row at-row__justify--between kb-color__grey kb-size__sm kb-margin-sm-tb'>
                <View>
                  <AtIcon prefixClass='kb-icon' value='msg' className='kb-icon__msg' size='16' />
                  <Text className='kb-color__brand'>
                    {item.notify == 0
                      ? '未通知'
                      : item.notify == 1
                      ? '短信通知'
                      : item.notify == 2
                      ? '微信通知'
                      : ''}
                  </Text>
                </View>
                <Text
                  className={classNames('kb-size__sm', {
                    'kb-status1': item.status == 1,
                    'kb-status2': item.status == 2,
                    'kb-status3': item.status == 3,
                  })}
                >
                  {item.status == 0
                    ? '取消投递'
                    : item.status == 1
                    ? '待取件'
                    : item.status == 2
                    ? '已取件'
                    : item.status == 3
                    ? '滞留件'
                    : ''}
                </Text>
              </View>
              <View className='kb-color__grey kb-size__sm'>
                <AtIcon
                  prefixClass='kb-icon'
                  value='kdg'
                  className='kb-size__sm kb-margin-sm-r'
                  size='12'
                />
                <Text>{item.car_cabinet_name}</Text>
              </View>
            </View>
          </View>
        ))}
      </View>
    </KbLongList>
  );
};

DeliverRecordList.options = {
  addGlobalClass: true,
};

export default DeliverRecordList;
