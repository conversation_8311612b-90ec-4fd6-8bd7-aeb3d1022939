/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbLoginAuth from '@base/components/login/auth';
import KbModal from '@base/components/modal';
import KbNoticeBarUgly from '@base/components/notice-bar/ugly';
import { useUpdate } from '@base/hooks/page';
import useReport from '@base/hooks/report';
import request from '@base/utils/request';
import { frequencyLimitByMinute, noop, reportAnalytics } from '@base/utils/utils';
import { Image, OfficialAccount, View } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { Fragment, useEffect, useRef, useState } from '@tarojs/taro';
import classNames from 'classnames';
import { AtButton } from 'taro-ui';
import './index.scss';
import {
  getOfficialConfig,
  isShowOfficialAccount,
  useGetFollowStatus,
  useGetFollowStatus_FLS,
} from './_utils';

const Index = (props) => {
  const {
    navigateId = false,
    fixed,
    onlyCustom,
    full,
    showModal,
    modalReportDesc,
    onlyModal,
    phone,
    bindPhone = false,
    modalOfficialName,
    customImg,
  } = props;
  const { followAccount: { is_focus = true, name: officialName, follow_url } = {} } = useSelector(
    (state) => state.global,
  );

  const { isFollowAccount_FLS = null } = useSelector((state) => state.global);
  const [showChild, updateShowChild] = useState(onlyCustom);
  const [showPhone, updateShowPhone] = useState(false); // 已关注会员号、公众号未绑定手机号
  const [adConfig, setAdConfig] = useState();
  const [isOpened, updateIsOpened] = useState(false);
  const [isOpenedModal, updateIsOpenedModal] = useState(false);
  const actionRef = useRef({ officialCfg: {} });

  useUpdate((loginData) => {
    const { logined } = loginData;
    if (!logined) return;
    if (customImg && process.env.PLATFORM_ENV === 'weapp' && process.env.MODE_ENV === 'wkd') {
      request({
        url: '/g_tbk/v2/AdConfig/getAdConfig',
        toastLoading: false,
        data: { type: 'miniapp', platform: 'wkdmini', position: '16' },
        onThen: ({ data = [] } = {}) => {
          setAdConfig(data[0]);
        },
      });
    }
  }, []);
  const [unlockGet] = useGetFollowStatus();
  const [unlockGet_FLS] = useGetFollowStatus_FLS();
  const reportKey = 'foucs_gzh_ad';

  const report = useReport({
    title: '关注组件',
  });

  const isReal = () => typeof isFollowAccount_FLS === 'boolean';

  useEffect(() => {
    // console.log('是否关注会员号',isFollowAccount_FLS)
    // console.log('是否关注微快递',is_focus)
    // console.log('是否关注展示关注公众号组件',!isFollowAccount_FLS || !is_focus)
    // console.log("是否符合展示原生公众号组件场景值",isShowOfficialAccount())
    if (!isReal()) return;
    actionRef.current.officialCfg = getOfficialConfig(!isFollowAccount_FLS);
    const open = !isFollowAccount_FLS || !is_focus;
    if (!isFollowAccount_FLS && showModal && isShowOfficialAccount()) {
      frequencyLimitByMinute('check', 'isShowOfficialAccountModal', 7 * 24 * 60).then((limit) => {
        if (!limit) {
          updateIsOpenedModal(true);
          actionRef.current.timer && clearTimeout(actionRef.current.timer);
          actionRef.current.timer = setTimeout(() => {
            updateIsOpenedModal(false);
          }, 10000);
          frequencyLimitByMinute('limit', 'isShowOfficialAccountModal');
          reportAnalytics({
            key: reportKey,
            options: modalReportDesc || `弹窗原生关注组件-${actionRef.current.officialCfg.name}`,
          });
        } else {
          isFollowAccount_FLS && updateIsOpenedModal(false);
          !onlyModal && updateIsOpened(open);
        }
      });
    } else {
      !onlyModal && updateIsOpened(open);
    }
  }, [isFollowAccount_FLS, is_focus, showModal]);

  useEffect(() => {
    if (!isReal()) return;
    const show = isFollowAccount_FLS && is_focus && bindPhone && !phone;
    if (show) {
      reportAnalytics({
        key: reportKey,
        options: `绑定手机号组件-展示-${actionRef.current.officialCfg.name}`,
      });
    }
    updateShowPhone(show);
  }, [isFollowAccount_FLS, is_focus, bindPhone, phone]);

  useEffect(() => {
    props.onChange({ isOpened, showChild });
  }, [isOpened, showChild]);

  const handleCallBack = () => {
    reportAnalytics({
      key: reportKey,
      options: `绑定手机号组件-点击-${actionRef.current.officialCfg.name}`,
    });
  };

  const handleToFollow = () => {
    if (process.env.MODE_ENV === 'wkd') {
      Taro.previewImage({
        urls: [
          !isFollowAccount_FLS
            ? 'https://cdn-img.kuaidihelp.com/wkd/gzh_huiyuan.png'
            : 'https://cdn-img.kuaidihelp.com/wkd/gzh_wkd.png',
        ],
      });
      reportAnalytics({
        key: reportKey,
        options: `${actionRef.current.officialCfg.name}点击广告位`,
      });
    } else {
      report('click');
      Taro.navigator({
        url: follow_url,
        target: 'webview',
        report: {
          key: reportKey,
          options: `${officialName}点击跳转广告位`,
        },
      });
    }
    unlockGet();
    unlockGet_FLS();
  };

  // 原生关注组件加载成功
  const handleLoad = () => {
    if (process.env.MODE_ENV === 'wkd') {
      reportAnalytics({
        key: reportKey,
        options: `${actionRef.current.officialCfg.name}展示微信关注公众号组件`,
      });
    }
  };

  const handleClose = () => updateIsOpened(false);

  // 原生关注组件加载失败
  const handleError = () => {
    console.log('原生关注组件加载失败');
    updateShowChild(true);
    if (process.env.MODE_ENV === 'wkd') {
      // 加载失败，且未关注
      reportAnalytics({
        key: reportKey,
        options: `${actionRef.current.officialCfg.name}展示广告位`,
      });
    }
  };

  const rootCls = classNames('kb-official-account', {
    [`kb-official-account__${fixed}`]: !!fixed,
    'kb-official-account__ghost': process.env.MODE_ENV === 'wkd',
    'kb-official-account__full': full,
  });

  return process.env.MODE_ENV !== 'third.post' &&
    process.env.MODE_ENV !== 'third' &&
    process.env.MODE_ENV !== 'third.pro' ? (
    <Fragment>
      {isOpened ? (
        !showChild && (process.env.MODE_ENV !== 'wkd' || !isFollowAccount_FLS) ? (
          <View className={rootCls}>
            <OfficialAccount onError={handleError} onLoad={handleLoad} />
          </View>
        ) : navigateId ? (
          <View className={rootCls}>
            <KbNoticeBarUgly
              onClose={handleClose}
              type='secondary'
              showFollow
              radius={!full}
              renderMore={
                <View>
                  <AtButton
                    className='kb-button__small'
                    type='primary'
                    circle
                    onClick={handleToFollow}
                  >
                    去关注
                  </AtButton>
                </View>
              }
            >
              {process.env.MODE_ENV == 'wkd'
                ? `关注“微快递${!isFollowAccount_FLS ? '会员”' : '”公众号'}，有快递早知道！`
                : `关注"${officialName}"公众号,物流信息早知道`}
            </KbNoticeBarUgly>
          </View>
        ) : null
      ) : null}
      {process.env.MODE_ENV === 'wkd' && showPhone ? (
        <View className={rootCls}>
          <KbNoticeBarUgly
            onClose={handleClose}
            type='secondary'
            showFollow
            radius={!full}
            renderMore={
              <View>
                <KbLoginAuth
                  size='small'
                  useOpenType
                  text='去绑定'
                  scope='phoneNumber'
                  showIcon={false}
                  onAuthComplete={handleCallBack}
                />
              </View>
            }
          >
            绑定手机号，及时接收快递物流消息
          </KbNoticeBarUgly>
        </View>
      ) : null}
      <KbModal
        isOpened={isOpenedModal}
        rootClass='kb-modalOfficialAccount'
        confirmText=''
        top={false}
        onCancel={() => updateIsOpenedModal(false)}
      >
        {adConfig && adConfig.imgUrl ? (
          <View className='kb-wkdModal'>
            <Image className='kb-wkdModal--img' src={adConfig.imgUrl} mode='widthFix'>
              {isOpenedModal ? <OfficialAccount className='kb-wkdModal--official' /> : null}
            </Image>
          </View>
        ) : (
          <View className='kb-gzhModal'>
            <View className='kb-gzhModal--tips'>
              <View>关注“{modalOfficialName || '微快递助手'}”公众号</View>
              {process.env.MODE_ENV == 'wkd' ? '及时获取物流状态' : '及时接收快递物流消息'}
            </View>
            {isOpenedModal ? <OfficialAccount /> : null}
          </View>
        )}
      </KbModal>
    </Fragment>
  ) : (
    <Fragment />
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  fixed: '',
  onlyCustom: false,
  full: false,
  onChange: noop,
};

export default Index;
