import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { AtGrid, AtIcon } from "taro-ui";
import './index.scss';

const grids = [
    {
        key: 'truck/manage',
        value: '无人车管理',
        icon: 'car-2'
    },
    {
        key: 'task/manage',
        value: '任务管理',
        icon: 'task'
    },
    {
        key: 'line/manage',
        value: '常用路线',
        icon: 'location-2'
    },
    {
        key: 'contacts',
        value: '联系人',
        icon: 'user'
    }
]

const TruckDispatchGrid = () => {

    const handleClickGrid = (item) => {
        Taro.navigator({
            url: item.key
        });
    }

    return (
        <View className="kb-spacing-md-lr">
            <View className="truck-dispatch-grid kb-box">
                {
                    grids.map(item => (
                        <View key={item.key} className="truck-dispatch-grid__item" hoverClass="kb-hover" onClick={() => handleClickGrid(item)}>
                            <AtIcon prefixClass="kb-icon" value={item.icon} />
                            <View>{item.value}</View>
                        </View>
                    ))
                }
            </View>
        </View>
    )
}

export default TruckDispatchGrid;