import { View } from "@tarojs/components";
import { useRequest } from "~base/utils/request/hooks";
import { getTruckDispatchStatistics } from "~/services/truck/dispatch";
import { AtButton } from "taro-ui";
import Taro from "@tarojs/taro";
import './index.scss';

const TruckDispatchStatistics = () => {
    const { data } = useRequest(getTruckDispatchStatistics);

    const handleClick = (status) => {
        // status 0 派车中 1 今日配车任务；
        Taro.navigator({
            url: 'task/manage',
            options: {
                status
            }
        });
    }

    // 我要租车
    const handleClickLease = () => {
        Taro.navigator({
            url: 'index',
            options: {
                action: 'rent'
            }
        });
    }

    return (
        <View className="truck-dispatch-statistics">
            <View className="truck-dispatch-statistics__item" onClick={() => handleClick('dispatching')} hoverClass="kb-hover-opacity">
                <View>{data?.driving_total || 0}</View>
                <View className="truck-dispatch-statistics__item--label">派车中</View>
            </View>

            <View className="truck-dispatch-statistics__item" onClick={() => handleClick('todayDispatched')} hoverClass="kb-hover-opacity">
                <View>{data?.today_using_total || 0}</View>
                <View className="truck-dispatch-statistics__item--label">今日派车任务</View>
            </View>

            <View onClick={handleClickLease} className="truck-dispatch-statistics__btn" hoverClass="kb-hover-opacity">
                我要租车
            </View>
        </View>
    )
}

export default TruckDispatchStatistics;