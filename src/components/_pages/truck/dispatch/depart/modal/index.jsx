import { ScrollView, View } from "@tarojs/components";
import KbNoticeBar from '@base/components/notice-bar';
import Taro from "@tarojs/taro"
import TruckDispatchDepartContent from "./content";

const modalInfoMap = {
    save: {
        title: '保存任务，稍后发车',
        confirmText: '确定'
    },
    depart: {
        title: '立即发车',
        tips: '发车前，请确保车辆电量充足并做好安全检查！',
        confirmText: '开始发车'
    }
}

/**
 * 
 * @description 发车
 */
export function useTruckDispatchDepart(editRef) {

    /**
     * 
     * @param {'depart'|'save'} action depart：立即发车；save: 保存任务，稍后发车
     */
    const run = (action) => {
        const { title, tips, confirmText } = modalInfoMap[action] || {};
        const data = editRef.current.getFieldsValue();

        if (!data?.start?.point?.value) {
            Taro.kbToast({
                text: '请先选择发车点'
            });
            return;
        }

        Taro.kbModal({
            title,
            rootClass: 'truck-dispatch-depart-modal',
            confirmText,
            cancelText: '取消',
            full: true,
            onConfirm: () => {
                editRef.current.submit({
                    action
                })
            },
            content: (
                <TruckDispatchDepartContent tips={tips} data={data} />
            )
        });
    }

    return {
        run
    }
}