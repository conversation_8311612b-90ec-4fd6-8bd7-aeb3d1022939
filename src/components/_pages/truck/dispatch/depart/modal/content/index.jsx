import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>wi<PERSON>, SwiperI<PERSON>, View } from "@tarojs/components";
import KbNoticeBar from '~base/components/notice-bar';
import { useMemo } from "react";
import { randomCode } from "~base/utils/utils";
import isArray from "lodash/isArray";
import './index.scss';
import { AtIcon, AtNoticebar } from "taro-ui";

/**
 * 
 * @param {{
 *   tips?: string;
 *   data?: {
 *     start:{point:{value:string;label:string;};contact:{value:string;label:string;}}[];
 *     stop:{point:{value:string;label:string;};contact:{value:string;label:string;}}[];
 *     end:{point:{value:string;label:string;};contact:{value:string;label:string;}}[];
 *   };
 * }} props 
 * @returns 
 */
const TruckDispatchDepartContent = (props) => {
    const { data, tips } = props;

    // 分组
    const [groups, list] = useMemo(() => {
        if (!data) return [];
        const { start, stop, end } = data;
        const l = [
            {
                key: 'start',
                title: '发车点',
                label: start?.point?.label,
            },
            ...(isArray(stop) ? stop.map((item, index) => ({
                key: `stop-${index}`,
                title: '停靠点',
                label: item.point.label,
            })) : []),
            {
                key: 'end',
                title: '返程点',
                label: end?.point?.label
            }
        ].filter(item => !!item.label);
        const g = [];
        for (let i = 0, len = l.length; i < len; i += 2) {
            g.push({
                key: randomCode(),
                list: l.slice(i, i + 2)
            });
        }
        return [g, l];
    }, [data]);

    return (
        <View className="truck-dispatch-depart-content">
            <View className="kb-spacing-lg-lr">
                <ScrollView scrollX className="content-scrollview">
                    <View className="content-swiper__item-row">
                        {
                            list.map((item, index) => (
                                <Block key={item.key}>
                                    {
                                        index > 0 && (<AtIcon className="content-swiper__item-icon" prefixClass="kb-icon" value='arrow-long' />)
                                    }
                                    <View>
                                        <View className="content-swiper__item-title">{item.title}</View>
                                        <View className="content-swiper__item-label">{item.label}</View>
                                    </View>
                                </Block>
                            ))
                        }
                    </View>
                </ScrollView>

                {/* <Swiper className="content-swiper">
                {
                    groups.map((item, index) => (
                        <SwiperItem key={item.key} className="content-swiper__item">
                            <View className="content-swiper__item-row">
                                {
                                    item.list.map((iitem, iindex) => (
                                        <Block key={iitem.key}>
                                            {
                                                iindex > 0 && (<AtIcon className="content-swiper__item-icon" prefixClass="kb-icon" value='arrow-long' />)
                                            }
                                            <View>
                                                <View className="content-swiper__item-title">{iitem.title}</View>
                                                <View className="content-swiper__item-label">{iitem.label}</View>
                                            </View>
                                        </Block>
                                    ))
                                }
                            </View>
                        </SwiperItem>
                    ))
                }
            </Swiper> */}
            </View>
            {
                tips && (
                    <AtNoticebar icon='amaze'>{tips}</AtNoticebar>
                )
            }
        </View>
    )
}

export default TruckDispatchDepartContent;