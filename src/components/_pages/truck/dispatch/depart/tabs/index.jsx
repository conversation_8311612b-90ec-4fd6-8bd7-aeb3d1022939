import { AtTabs, AtTabsPane } from "taro-ui"
import { useCreateTabs } from "./_utils";
import { ScrollView, View } from "@tarojs/components";
import TruckDispatchDepartPane from "./pane";
import './index.scss';

const TruckDispatchDepartTabs = (props) => {
    const { defaultValue, params } = props;
    const { tabs, current, onSwitch } = useCreateTabs();

    return (
        <AtTabs tabList={tabs} current={current} onClick={onSwitch}>
            {
                tabs.map((item, index) => {
                    return (
                        <AtTabsPane key={item.key} current={current} index={index}>
                            <TruckDispatchDepartPane defaultValue={defaultValue} params={params} current={current} index={index} />
                        </AtTabsPane>
                    )
                })
            }
        </AtTabs>
    )
}

export default TruckDispatchDepartTabs;