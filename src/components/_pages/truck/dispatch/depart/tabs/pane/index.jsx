import { <PERSON><PERSON>ort<PERSON>, <PERSON><PERSON>View, Text, View } from "@tarojs/components"
import LineList from "~/components/_pages/line/list";
import LineEdit from "~/components/_pages/line/edit"
import AffixExtend from "~base/components/affixExtend";
import { AtButton, AtIcon } from "taro-ui";
import { useCallback, useMemo, useRef, useState } from "react";
import { useTruckDispatchDepart } from "../../modal";
import './index.scss';
import { formatLineEditForm } from "~/components/_pages/line/edit/_utils";
import { getTaskLastRoute, saveTask } from "~/services/task";
import pick from "lodash/pick";

/**
 * 
 * @param {{
 *   current:number;
 *   index:number;
 * }} props 
 * @returns 
 */
const TruckDispatchDepartPane = (props) => {
    const { defaultValue, current, index, params } = props;
    const editRef = useRef();
    const [addrSelected, setAddrSelected] = useState(null);
    const [addrEdit, setAddrEdit] = useState(false);

    const active = current === index;
    const showTools = active
        ? index === 0
            ? true
            : addrEdit
        : false;

    const { run: runDepart } = useTruckDispatchDepart(editRef);

    // 保存任务或发车
    const handleClick = (action) => runDepart(action);

    // 派车
    const handleDispatch = (d) => {
        setAddrEdit(true);
        setAddrSelected(formatLineEditForm(d.route_item));
    }

    const toolsRender = useMemo(() => {
        return showTools
            ? (
                <AffixExtend
                    height={'132rpx'}
                >
                    <View className="kb-spacing-md kb-background__white at-row truck-dispatch-depart-pane__tools">
                        <AtButton type='secondary' circle onClick={() => handleClick('save')}>保存任务，稍后发车</AtButton>
                        <AtButton className="kb-margin-md-l" type='primary' circle onClick={() => handleClick('depart')}>立即发车</AtButton>
                    </View>
                </AffixExtend>
            )
            : null
    }, [showTools]);

    // 使用上次派车记录
    const handleUseRecord = () => {
        const { vehicle_no } = params || {};
        getTaskLastRoute({ vehicle_no: decodeURIComponent(vehicle_no) }).then(res => {
            const { route_item } = res.data || {};
            const formData = formatLineEditForm(route_item);
            if (formData) {
                editRef.current?.update(formData);
            }
        });
    }

    // 切换线路
    const handleSwitchLine = () => {
        setAddrEdit(false);
    }

    // 提交
    const lineEditRequest = useCallback((d) => saveTask({
        ...d,
        vehicle_no: params?.vehicle_no
    }), [params]);

    return (
        <View className="truck-dispatch-depart-pane">
            {
                index === 0
                    ? (
                        <ScrollView className="kb-scrollview" scrollY>
                            <LineEdit
                                toolsRender={
                                    <View className="line-edit-form__tools--btn" hoverClass="kb-hover" onClick={handleUseRecord}>
                                        <AtIcon prefixClass="kb-icon" className="kb-color__brand kb-icon-size__base" value='clock' />
                                        <Text className="kb-icon__text--ml">使用上次派车记录</Text>
                                    </View>
                                }
                                action='line-edit'
                                request={lineEditRequest}
                                actionRef={editRef}
                                params={params}
                                defaultValue={defaultValue}
                            />
                            {toolsRender}
                        </ScrollView>
                    )
                    : (
                        <>
                            {
                                addrEdit
                                    ? (
                                        <ScrollView className="kb-scrollview" scrollY>
                                            <LineEdit
                                                toolsRender={
                                                    <View className="truck-dispatch-depart-pane__btn" hoverClass="kb-hover" onClick={handleSwitchLine}>
                                                        <AtIcon prefixClass="kb-icon" className="kb-color__brand kb-icon-size__base" value='switch' />
                                                        <Text className="kb-icon__text--ml">更换路线</Text>
                                                    </View>
                                                }
                                                defaultValue={addrSelected}
                                                actionRef={editRef}
                                                clearable={false}
                                                request={lineEditRequest}
                                            />
                                            {toolsRender}
                                        </ScrollView>
                                    )
                                    : (
                                        <LineList active={active && pick(params, ['vehicle_owner_id'])} onDispatch={handleDispatch} />
                                    )
                            }
                        </>
                    )
            }
        </View>
    )
}

export default TruckDispatchDepartPane;