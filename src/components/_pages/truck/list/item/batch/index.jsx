import { Text, View } from "@tarojs/components"
import { AtButton } from "taro-ui";
import { TruckSelectContext, useTruckSelectContextValue } from "../select/_utils/context";
import Checkbox from "~base/components/checkbox";
import { truckDelete, truckDelisting } from "~/services/truck/operator";
import Popconfirm from "~base/components/popconfirm";
import { useTruckOperator } from "../bars/_utils";
import { useRef } from "react";
import './index.scss';

const TruckBatch = (props) => {
    const ref = useRef({ selectedVehicleNos: [] });
    const { ids, value, selected, onSwitch, onSelectAll, isSelectedAll, operableType, operableTypeLabel } = useTruckSelectContextValue({
        ...props,
        onSelect: (selectedList) => {
            ref.current.selectedVehicleNos = selectedList.map(item => item.vehicle_no);
        }
    });

    const selectedCount = selected?.length;

    // 成功回调
    const triggerSuccess = (res) => {
        const isSuccess = `${res.code}` === '0';
        if (isSuccess) {
            props?.onSuccess?.();
        }
        return res;
    }

    const { run } = useTruckOperator();

    // 删除
    const handleDelete = () => run('remove', { vehicle_nos: ref.current.selectedVehicleNos }).then(triggerSuccess);

    // 上下架
    const handleOperator = () => run(operableType, { vehicle_nos: ref.current.selectedVehicleNos }).then(triggerSuccess);

    const buttonProps = {
        disabled: selectedCount === 0,
        type: 'primary',
        size: 'small',
        circle: true,
    }

    return (
        <View className="truck-batch">
            {
                ids?.length > 0 && (
                    <View className='truck-batch__header'>
                        <AtButton circle size='small' type={value?.enable ? 'secondary' : ''} onClick={onSwitch}>批量操作</AtButton>
                    </View>
                )
            }
            <View className="truck-batch__body">
                <View className="truck-batch__body--inner">
                    <TruckSelectContext.Provider value={value}>
                        {props.children}
                    </TruckSelectContext.Provider>
                </View>
            </View>
            {
                value?.enable && (
                    <View className="truck-batch__footer">
                        <View className="truck-batch__footer--row">
                            <Checkbox
                                onChange={onSelectAll}
                                checked={isSelectedAll}
                                label={
                                    <View>
                                        <Text>全选</Text>
                                        <Text className="kb-spacing-md-l">已选</Text>
                                        <Text className="kb-color__brand">{selectedCount}</Text>
                                        <Text>个</Text>
                                    </View>
                                }
                            />
                            <View hoverClass="kb-hover-opacity" onClick={onSwitch}>取消</View>
                        </View>
                        <View className="truck-batch__footer--row">
                            <Popconfirm
                                buttonProps={{
                                    ...buttonProps,
                                    type: 'secondary',
                                    className: "kb-button__danger"
                                }}
                                title='确定删除车辆？'
                                onConfirm={handleDelete}
                            >删除</Popconfirm>
                            {operableType && (
                                <Popconfirm
                                    buttonProps={buttonProps}
                                    onConfirm={handleOperator}
                                    title={`确定${operableTypeLabel}车辆？`}
                                >{operableTypeLabel}</Popconfirm>
                            )}
                        </View>
                    </View>
                )
            }
        </View>
    )
}

export default TruckBatch