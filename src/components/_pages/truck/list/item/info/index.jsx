import { View } from "@tarojs/components"
import TruckStatus from "../status"
import BatteryIcon from "../../../battery";
import TruckServerStatus from "../status/server";
import classNames from "classnames";
import './index.scss';
import Taro from "@tarojs/taro";

const TruckInfo = (props) => {
    const { data, border = true, className, isDetail } = props;

    const rootCls = classNames('truck-info kb-navigator kb-navigator-ghost', className, {
        'kb-navigator-noborder': !border,
        'kb-navigator-noarrow': isDetail
    });

    // 跳转详情
    const handleClick = (e) => {
        e.stopPropagation();
        if (isDetail) return;
        Taro.navigator({
            url: 'truck/detail',
            options: {
                vehicle_no: data?.vehicle_no
            }
        });
    }

    return (
        <View className={rootCls} hoverClass={isDetail ? 'none' : "kb-hover"} hoverStopPropagation onClick={handleClick}>
            <View className="kb-navigator__content at-row at-row__align--center">
                <View>{data?.vehicle_no}</View>
                <TruckStatus data={data} />
                <BatteryIcon quantity={data?.battery_power} />
            </View>
            {
                !isDetail && (
                    <View className="kb-navigator__extra">
                        <TruckServerStatus data={data} strong />
                    </View>
                )
            }
        </View>
    )
}

export default TruckInfo;