import { View } from '@tarojs/components';
import classNames from 'classnames';
import { getCurrentTruckServerStatus } from '../_utils';
import './index.scss';

/**
 * 状态
 * @param {{data?:{order_status?:string};strong?:boolean;long?:boolean;}} props
 * @returns
 */
const TruckServerStatus = (props) => {
  const { data, strong, long } = props;
  const statusLabel = getCurrentTruckServerStatus(data, long);
  const rootCls = classNames('truck-server-status', `truck-server-status__${data?.order_status}`, {
    'truck-server-status__strong': strong
  });

  return <View className={rootCls}>{statusLabel}</View>;
};
export default TruckServerStatus;
