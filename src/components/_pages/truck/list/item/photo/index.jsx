import { Image, View } from "@tarojs/components"
import classNames from "classnames";
import Taro from "@tarojs/taro";
import { useMemo } from "react";
import { formatTruckPhotoToForm } from "../add/_utils/form";
import './index.scss';

const labels = ['前', '后', '左', '右'];

const TruckPhoto = (props) => {
    const { data, className } = props;
    const rootCls = classNames('truck-photo', className);

    const photos = useMemo(() => formatTruckPhotoToForm(data), [data]);

    const handlePreview = item => {
        Taro.previewImage({
            current: item,
            urls: photos,
        });
    }

    return (
        <View className={rootCls}>
            {
                photos.map((item, index) => (
                    <View key={item} className="truck-photo__item" hoverClass="kb-hover-opacity" onClick={() => handlePreview(item)}>
                        <Image mode="widthFix" src={item} className="truck-photo__item-img" />
                        <View className="truck-photo__item-bottom">{labels[index]}</View>
                    </View>
                ))
            }
        </View>
    )
}

export default TruckPhoto