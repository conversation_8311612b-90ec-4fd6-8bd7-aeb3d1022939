import { Text, View } from "@tarojs/components";
import classNames from "classnames";
import { formatNumeral } from "~base/utils/numeral";
import './index.scss';

const TruckRent = (props) => {
    const { data, className } = props;
    const cls = classNames('truck-rent', className);
    const { lease_config } = data || {};
    const isRent = `${lease_config?.lease_switch}` === '1';

    return (
        <>
            {
                isRent
                    ? (
                        <View className={cls}>
                            <View className="at-row kb-spacing-md-b">
                                <View>
                                    <Text className="kb-color__grey-1-1">日租价格：</Text><Text>{lease_config?.rent}元/天</Text>
                                </View>
                                <View className="truck-rent__mortgage">
                                    <Text className="kb-color__grey-1-1">租金押金：</Text><Text>{formatNumeral(lease_config?.mortgage)}元</Text>
                                </View>
                            </View>
                            <View className="kb-spacing-md-b">
                                <Text className="kb-color__grey-1-1">最长租赁天数：</Text><Text>{lease_config?.longest_lease_day}天</Text>
                            </View>
                            <View className="kb-spacing-md-b">
                                <Text className="kb-color__grey-1-1">提前退租是否收取违约金：</Text><Text>{`${lease_config?.penalty_switch}` === '1' ? '是' : '否'}</Text>
                            </View>
                            <View className="truck-rent-config">
                                {
                                    lease_config?.discount_config?.map((item, index) => (
                                        <View key={`d-${index}`} className="truck-rent-config__item">
                                            <View className="truck-rent-config__item--title">
                                                <Text className="kb-color__grey-1-1">当租赁天数：</Text>
                                                {
                                                    item.min_day > 0 || item.max_day > 0
                                                        ? (
                                                            <>
                                                                {item.min_day > 0 && <Text>大于等于{item.min_day}天</Text>}
                                                                {item.max_day > 0 && <Text>小于等于{item.max_day}天</Text>}
                                                            </>
                                                        )
                                                        : (
                                                            <Text>不限</Text>
                                                        )
                                                }
                                            </View>
                                            <View className="truck-rent-config__item--desc">折扣价：{formatNumeral(item.discount_rent)}元</View>
                                        </View>
                                    ))
                                }
                            </View>
                        </View>
                    )
                    : null
            }
        </>
    )
}

export default TruckRent;