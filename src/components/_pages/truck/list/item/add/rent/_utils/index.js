import { checkDiscountValue, patchDiscountValue } from '../formDiscountField/discountList/_utils';

// Switch 值转换
const patchSwitchValue = (v) => `${v}` === '1';

/**
 *
 * @description 租赁表单
 */
export const patchRentForm = (detailData) => {
  const { lease_config } = detailData || {};
  return {
    lease_switch: { value: patchSwitchValue(lease_config?.lease_switch) },
    discount_switch: { value: patchSwitchValue(lease_config?.discount_switch) },
    discount_config: { required: false, value: patchDiscountValue(lease_config?.discount_config) },
    mortgage: { required: false, value: lease_config?.mortgage || '' },
    penalty_switch: { value: patchSwitchValue(lease_config?.penalty_switch) },
    longest_lease_day: { value: lease_config?.longest_lease_day || 180 },
    daily_rental_price: { value: lease_config?.rent || 0, required: false },
  };
};

/**
 *
 * @description 检查租赁表单
 * @param {*} req
 * @returns
 */
export function checkRentForm(req) {
  const { longest_lease_day, lease_switch, discount_switch, discount_config, daily_rental_price } =
    req;
  if (!lease_switch) {
    // 未开启租赁
    return null;
  }

  if (discount_switch) {
    // 开启租赁折扣
    const checkRes = checkDiscountValue({
      value: discount_config || patchDiscountValue(),
      maxPrice: daily_rental_price,
    });
    if (checkRes.code > 0) {
      return checkRes;
    }
  }

  if (!longest_lease_day || `${longest_lease_day}` === '0') {
    return {
      code: 9002,
      msg: '请输入最长租赁天数',
    };
  }
}
