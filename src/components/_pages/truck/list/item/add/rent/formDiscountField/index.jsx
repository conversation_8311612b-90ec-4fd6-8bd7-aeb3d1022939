import { View } from "@tarojs/components";
import KbSwitch from '~base/components/switch';
import FormDiscountList from "./discountList";
import './index.scss';

const FormDiscountField = (props) => {
    const { formData, onChange } = props;

    const handleChange = (key, value) => {
        onChange?.(key, value)
    }

    return (
        <View className="form-discount-field">

            <View className="kb-form__item">
                <View className='item-title'>设置折扣价</View>
                <View className='item-content'>
                    <KbSwitch
                        checked={formData?.discount_switch}
                        onChange={(checked) => handleChange('discount_switch', checked)}
                    />
                </View>
            </View>

            {
                formData?.discount_switch && (
                    <FormDiscountList
                        maxPrice={formData?.daily_rental_price}
                        value={formData?.discount_config}
                        onChange={(val) => handleChange('discount_config', val)}
                    />
                )
            }

        </View>
    )
}

export default FormDiscountField;