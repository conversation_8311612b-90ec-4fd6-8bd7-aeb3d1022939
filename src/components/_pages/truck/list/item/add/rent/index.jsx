import { View } from "@tarojs/components";
import KbSwitch from '~base/components/switch';
import KbInputNumber from '~base/components/input-number';
import { AtInput } from "taro-ui";
import FormDiscountField from "./formDiscountField";
import { getTruckRentList } from "~/services/truck/add";
import pick from "lodash/pick";
import { useEffect, useMemo } from "react";
import isArray from "lodash/isArray";
import './index.scss';

const TruckAddRent = (props) => {
    const { formData, onChange } = props;

    const handleChange = (key, value) => {
        onChange?.(key, value)
    }

    // 用于检查是否可获取日租金额
    const [valuesStr, allReady] = useMemo(() => {
        const truckTypes = pick(formData, ['truckload', 'brand', 'model', 'vehicle_type', 'type']);
        const truckTypesValues = Object.values(truckTypes);
        const ready = truckTypesValues.length > 0 && truckTypesValues.every(v => !!v?.value);
        const str = truckTypesValues.map(item => item?.value).join('|');
        return [str, ready];
    }, [formData]);

    useEffect(() => {
        if (allReady) {
            getTruckRentList(formData).then(items => {
                if (isArray(items)) {
                    onChange?.('daily_rental_price', items[0]?.value);
                }
            });
        }
    }, [valuesStr, allReady]);

    return (
        <View className="truck-rent-form">
            <View className="truck-rent-form__tips">租赁信息</View>
            <View className="kb-form__item">
                <View className='item-title'>车辆对外出租</View>
                <View className='item-content'>
                    <KbSwitch
                        checked={formData?.lease_switch}
                        onChange={(checked) => handleChange('lease_switch', checked)}
                    />
                </View>
            </View>
            <View className="truck-rent-form__tips">开启后，用户可以按天按时租用车辆</View>

            {
                formData?.lease_switch && (
                    <>
                        <View className="kb-form__item">
                            <View className='item-title'>日租价格</View>
                            <View className='item-content kb-spacing-md-r'>
                                {formData?.daily_rental_price}元
                            </View>
                        </View>
                        <View className="truck-rent-form__tips">由平台按照车型统一定价，车主可根据实际需要设置折扣价</View>

                        <FormDiscountField
                            formData={formData}
                            onChange={onChange}
                        />

                        <View className="kb-form__item">
                            <View className='item-title'>押金金额</View>
                            <View className='item-content'>
                                <AtInput
                                    placeholder="请输入，建议1000~3000"
                                    value={formData?.mortgage}
                                    onChange={(val) => handleChange('mortgage', val)}
                                    type='digit'
                                />
                            </View>
                            <View className="kb-spacing-md-r">元</View>
                        </View>

                        <View className="kb-form__item">
                            <View className='item-title'>提前退租违约金</View>
                            <View className='item-content'>
                                <KbSwitch
                                    checked={formData?.penalty_switch}
                                    onChange={(checked) => handleChange('penalty_switch', checked)}
                                />
                            </View>
                        </View>
                        <View className="truck-rent-form__tips">
                            开启后，若用户提前退租，系统按照违约金规则自动计算违约金
                        </View>
                        <View className="truck-rent-form__desc">
                            <View>违约金规则如下：</View>
                            <View>
                                提前退租按照车辆剩余租金的20%作为违约金，例如货主租赁时间为5天，
                            </View>
                            <View>
                                租金100元/天，若提前2天退租，
                            </View>
                            <View>
                                违约金 = 剩余租金 * 20% = (2 * 100) * 20% = 40元
                            </View>
                        </View>

                        <View className="kb-form__item kb-form__item--required">
                            <View className='item-title'>最长租赁天数</View>
                            <View className='item-content'>
                                <KbInputNumber
                                    placeholder='天数'
                                    value={formData?.longest_lease_day}
                                    onChange={(val) => handleChange('longest_lease_day', val)}
                                    min={1}
                                    step={10}
                                    max={9999}
                                />
                            </View>
                        </View>
                    </>
                )
            }
        </View>
    )
}

export default TruckAddRent;