import cloneDeep from 'lodash/cloneDeep';
import isArray from 'lodash/isArray';
import { randomCode } from '~base/utils/utils';

const discountDefaultValue = {
  min_day: '',
  max_day: '',
  discount_rent: '',
  minDisabled: false,
};

// 最大折扣配置数
export const patchDiscountMax = 5;

// 折扣表单列表
export function patchDiscountValue(val, patchItem) {
  if (isArray(val)) {
    const needPatchKey = val.some((item) => !item.key);
    return !needPatchKey
      ? val
      : val.map((item) => ({
          key: randomCode(),
          ...item,
        }));
  }
  return [
    {
      key: randomCode(),
      ...discountDefaultValue,
      ...patchItem,
    },
  ];
}

function checkDiscountItem({ item, index = -1, pre, maxPrice }) {
  let msg = '';
  const { min_day: min, max_day: max, discount_rent: price } = item;
  if (`${min}` === '0' || `${max}` === '0') {
    msg = '天数不可为0';
  } else if (min && max && 1 * min >= 1 * max) {
    msg = `大于等于${min}天 且 小于等于${max}天，规则设置错误`;
  } else if (maxPrice && (!price || `${price}` === '0' || 1 * price > 1 * maxPrice)) {
    // 传入maxPrice，需要检查日租折扣价
    msg = 1 * price > 1 * maxPrice ? '折扣价不能超过平台的定价' : '请输入折扣价';
  } else if (max && pre && 1 * max < 1 * pre.max_day) {
    // 传入上一条，需要检查当前最小值是否大于上一条最大值
    msg = `起始天数不能小于${pre.max_day}`;
  }

  if (msg && index >= 0) {
    msg = `第${index + 1}条，${msg}`;
  }

  return msg;
}

// 检查
export function checkDiscountValue({ value, action, maxPrice }) {
  let msg = '';
  if (isArray(value)) {
    switch (action) {
      case 'add': // 添加
        const last = value.slice(-1)[0];
        msg = checkDiscountItem({ item: last });
        if (!msg && (!last.max_day || !last.max_day)) {
          msg = '请先完善上一个折扣规则';
        }
        break;
      default:
        for (let i = 0, len = value.length; i < len; i++) {
          msg = checkDiscountItem({ item: value[i], pre: value[i - 1], index: i, maxPrice });
          if (msg) {
            break;
          }
        }
        break;
    }
  }
  return msg
    ? {
        code: 9001,
        msg,
      }
    : {
        code: 0,
      };
}

export function patchDiscountInputValue(item, type, e, value) {
  const valueCopy = cloneDeep(value);
  const { value: val } = e.detail;
  const index = valueCopy.findIndex((i) => i.key === item.key);
  if (index >= 0) {
    // 下一条数据
    if (type === 'max') {
      // 输入最大值，判断下一条是否有最小值，有的话，要更新为max+1
      const nextIndex = index + 1;
      const { min_day: nextMin } = valueCopy[nextIndex] || {};
      if (val > 0 && nextMin > 0) {
        valueCopy.splice(nextIndex, 1, {
          ...valueCopy[nextIndex],
          min_day: 1 + 1 * val,
        });
      }
    }

    valueCopy.splice(index, 1, {
      ...valueCopy[index],
      [type]: val,
    });
  }
  return valueCopy;
}
