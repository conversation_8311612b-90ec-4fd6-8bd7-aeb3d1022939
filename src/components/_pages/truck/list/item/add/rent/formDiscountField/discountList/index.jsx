import { Input, Text, View } from "@tarojs/components";
import { useMemo } from "react";
import { checkDiscountValue, patchDiscountInputValue, patchDiscountMax, patchDiscountValue } from "./_utils";
import { AtIcon } from "taro-ui";
import Taro from "@tarojs/taro";
import classNames from "classnames";
import './index.scss';

const FormDiscountList = (props) => {
    const { value: valueProps, onChange, maxPrice } = props;
    const value = useMemo(() => patchDiscountValue(valueProps), [valueProps]);
    const len = value.length;
    const showAdd = len < patchDiscountMax;
    const showRemove = len > 1;

    // 添加
    const handleAdd = () => {
        const checkRes = checkDiscountValue({ value, action: 'add' });
        if (checkRes.code === 0) {
            onChange?.(value.concat(patchDiscountValue(null, {
                min_day: 1 + (1 * value.slice(-1)[0].max_day),
                minDisabled: true
            })));
        } else {
            Taro.kbToast({
                text: checkRes.msg
            });
        }
    }

    // 移除
    const handleRemove = () => {
        onChange?.(value.slice(0, len - 1));
    }

    // 输入
    const handleInput = (item, type, e) => {
        const patchedValue = patchDiscountInputValue(item, type, e, value);
        onChange?.(patchedValue);
    }

    return (
        <View className="form-discount-list">
            <View className="form-discount-list__tips">
                设置享受折扣价条件，默认任意租赁天数均享受折扣价
            </View>
            <View className="form-discount-list__body">
                {
                    value?.map(item => (
                        <View key={item.key} className="form-discount-list__body--item">
                            <View>当租赁天数</View>
                            <View className="at-row at-row__align--center at-row__justify--between kb-spacing-md-t">
                                <View>大于等于</View>
                                <Input disabled={item.minDisabled} cursor={-1} value={item.min_day} onInput={(e) => handleInput(item, 'min_day', e)} placeholder="请输入" type='number' className={
                                    classNames("form-discount-list__input", {
                                        "form-discount-list__input--disabled": item.minDisabled
                                    })
                                } />
                                <View>天，且小于等于</View>
                                <View>
                                    <View className="at-row at-row__align--center">
                                        <Input cursor={-1} value={item.max_day} onInput={(e) => handleInput(item, 'max_day', e)} placeholder="请输入" type='number' className="form-discount-list__input" />
                                        <View>天</View>
                                    </View>
                                </View>
                            </View>
                            <View className="at-row at-row__align--center at-row__justify--between kb-spacing-md-t">
                                <View>
                                    <Text className="kb-color__red">*</Text><Text>日租折扣价</Text>
                                </View>
                                <View>
                                    <View className="at-row at-row__align--center">
                                        <Input cursor={-1} value={item.discount_rent} onInput={(e) => handleInput(item, 'discount_rent', e)} placeholder="请输入" type='digit' className="form-discount-list__input" />
                                        <View>元</View>
                                    </View>
                                </View>
                            </View>
                        </View>
                    ))
                }
                <View className="form-discount-list__footer">
                    {showAdd && (
                        <View className="form-discount-list__footer--add" hoverClass="kb-hover-opacity" onClick={handleAdd}>
                            <AtIcon prefixClass="kb-icon" value='add' className="kb-icon-size__base" />
                            <Text className="kb-icon__text--ml">添加折扣规则</Text>
                        </View>
                    )}
                    {showRemove && (
                        <View className="form-discount-list__footer--remove" hoverClass="kb-hover-opacity" onClick={handleRemove}>移除</View>
                    )}
                </View>
            </View>

        </View>
    )
}

export default FormDiscountList;