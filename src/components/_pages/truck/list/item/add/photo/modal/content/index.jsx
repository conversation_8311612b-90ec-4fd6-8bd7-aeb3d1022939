import { View } from "@tarojs/components";
import KbCheckbox from '~base/components/checkbox';
import { useEffect, useState } from "react";
import './index.scss';

const TruckNoModalContent = (props) => {
    const { data, onChange } = props;
    const [value, setValue] = useState('');
    const handleChange = val => {
        setValue(val);
        onChange?.(val);
    }

    useEffect(() => {
        onChange?.('');
    }, []);

    return (
        <View className="truck-no-modal-content">
            <View>识别出多个车牌号，请选择：</View>
            <View className="truck-no-modal-content__checkbox">
                {
                    data.map(item => (
                        <KbCheckbox key={item} checked={value === item} onChange={() => handleChange(item)} labelClassName='kb-color__brand' label={item} />
                    ))
                }
            </View>
        </View>
    )
}

export default TruckNoModalContent;