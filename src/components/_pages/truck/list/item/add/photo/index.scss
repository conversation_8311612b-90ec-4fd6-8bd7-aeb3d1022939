.truck-form-photo {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: $spacing-v-md 0;

    &__item {
        box-sizing: border-box;
        text-align: center;
        border-radius: $border-radius-base;
        background-color: $color-white;
        padding: $spacing-v-md $spacing-h-md;
        padding-bottom: 0;

        &-wrapper {
            width: 50%;
            box-sizing: border-box;

            &:nth-child(2n) {
                padding-left: $spacing-h-md;
            }
        }


        .kb-image-picker {
            width: 300px;
            height: 300px;
        }

        .picker-preview,
        .picker-preview .at-avatar,
        .picker-bar,
        .picker-bar__custom,
        .picker-preview__item,
        .picker-preview__item--image {
            width: 100%;
            height: 100%;
        }

        .picker-preview,
        .picker-bar {
            background-color: rgba($color: $color-brand, $alpha: 0.04);
            border: $width-base dashed $color-brand;
            border-radius: $border-radius-lg;
        }

        .picker-bar {
            &__custom {
                display: flex;
                align-items: center;
                justify-content: center;

                &.kb-hover-opacity {
                    background-color: rgba($color: $color-brand, $alpha: 0.05);
                }
            }

            .at-icon-add {
                font-size: $icon-font-size-lg !important;
                color: $color-brand;
            }
        }
    }
}