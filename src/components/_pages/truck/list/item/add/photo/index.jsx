import { View } from "@tarojs/components";
import classNames from "classnames";
import KbImagePicker from '~base/components/image-picker';
import KbCheckbox from '~base/components/checkbox';
import { truckPhotoPositions } from "./_utils";
import { useMemo, useRef, useState } from "react";
import { AtIcon } from "taro-ui";
import isArray from "lodash/isArray";
import Taro from "@tarojs/taro";
import TruckNoModal from "./modal";
import './index.scss';

const TruckFormPhotoField = (props) => {
    const {
        className,
        value = [],
        onChange,
        onChangeLicensePlate  // 识别到车牌
    } = props;
    const truckNoModalRef = useRef();
    const [ocr, setOcr] = useState('1');

    const rootCls = classNames('truck-form-photo', className);

    // 选中车牌
    const handleConfirmVehicleNo = (no) => {
        onChangeLicensePlate?.({ vehicle_no: no });
        setOcr('0');
    }

    // 图片选择
    const handleImagePickerChange = (index, list, action, res) => {
        value[index] = list[0];
        if (action === 'upload') {
            const { vehicle_no } = res.data || {};
            if (vehicle_no) {
                const vehicle_nos = vehicle_no.split(',');
                const onlyOne = vehicle_nos.length === 1;
                // 识别出多个车牌
                if (!onlyOne) {
                    truckNoModalRef.current.open(vehicle_nos);
                    return;
                }

                handleConfirmVehicleNo(vehicle_nos[0]);
            }
        }
        onChange?.([...value]);
    }

    // 图片上传
    const api = useMemo(() => ({
        url: '/g_autovd/v2/Vehicle/Vehicle/uploadImage',
        data: {
            ocr
        },
    }), [ocr]);

    return (
        <View className={rootCls}>
            {
                truckPhotoPositions.map((item, index) => (
                    <View key={item.key} className="truck-form-photo__item-wrapper">
                        <View className="truck-form-photo__item">
                            <KbImagePicker api={api} maxSize={1 * 1024 * 1024} compressedWidth={1000} cropScale="1:1" custom files={[value[index]].filter(item => !!item)} onChange={(...arg) => handleImagePickerChange(index, ...arg)}>
                                <AtIcon value="add" />
                            </KbImagePicker>
                            <View className="kb-spacing-md-tb">{item.label}</View>
                        </View>
                    </View>
                ))
            }
            <TruckNoModal actionRef={truckNoModalRef} onConfirm={handleConfirmVehicleNo} />
        </View>
    )
}

export default TruckFormPhotoField;