import { useImperativeHandle, useRef } from "react";
import TruckNoModalContent from "./content";
import Taro from "@tarojs/taro";

const TruckNoModal = (props) => {
    const { actionRef, onConfirm } = props;
    const ref = useRef({ vehicleNo: '' });

    const handleChange = (no) => {
        ref.current.vehicleNo = no;
    }

    useImperativeHandle(actionRef, () => ({
        open: data => {
            Taro.kbModal({
                title: '车牌号识别',
                content: (
                    <TruckNoModalContent data={data} onChange={handleChange} />
                ),
                cancelText: '取消',
                onConfirm: () => {
                    const { vehicleNo } = ref.current;
                    if (!vehicleNo) {
                        Taro.kbToast({ text: '请选择车牌号' });
                        return false;
                    }
                    onConfirm?.(vehicleNo);
                }
            });
        }
    }));

    return (
        <></>
    )
}

export default TruckNoModal;