import { Image, View } from "@tarojs/components"
import classNames from "classnames";
import Taro from "@tarojs/taro";
import TruckServerStatus from "../status/server";
import './index.scss';

const TruckDetailTitle = (props) => {
    const { data, className } = props;
    const { order_id = data?.order_id } = data?.order || {};
    const hasOrder = !!order_id;

    const rootCls = classNames('truck-detail-title kb-navigator kb-navigator-ghost kb-navigator-noborder', {
        'kb-navigator-noarrow': !hasOrder
    }, className);

    const handleClick = () => {
        if (!hasOrder) return;
        Taro.navigator({
            url: 'order/detail',
            options: {
                order_id
            }
        });
    }

    return (
        <View className={rootCls} hoverClass={hasOrder ? "kb-hover" : "none"} onClick={handleClick}>
            <View className="kb-navigator__content">
                <TruckServerStatus data={data} strong long />
            </View>
            {
                hasOrder && (
                    <View className="kb-navigator__desc">
                        查看订单
                    </View>
                )
            }
        </View>
    )
}

export default TruckDetailTitle