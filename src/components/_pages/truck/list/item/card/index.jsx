import { Image, View } from "@tarojs/components"
import classNames from "classnames";
import { checkTruckIsRemove, useTruckCardList } from "./_utils";
import { makePhoneCall } from "~base/utils/utils";
import './index.scss';

const TruckCard = (props) => {
    const { data, className, showPhone, isOrderDetail, isDetail } = props;
    const { contact_phone } = data || {};
    const hasPhone = showPhone && !!contact_phone;
    const truckCardList = useTruckCardList({isOrderDetail, isDetail});

    const rootCls = classNames('truck-card', {
        'truck-card-flex-start': hasPhone
    }, className);
    const truckIsRemove = checkTruckIsRemove(data);
    const handleCall = () => makePhoneCall(contact_phone);
    const isValid = !!data?.id;  // 有效数据

    return (
        isValid
            ? (
                <View className={rootCls}>
                    <View className="truck-card__img--box">
                        <Image src={data?.images?.front} className="truck-card__img" mode="widthFix" />
                        {truckIsRemove && <View className="truck-card__img--remove">已下架</View>}
                    </View>
                    <View className="truck-card__content">
                        {
                            truckCardList.map(({ label, dataIndex, render }) => {
                                const val = data?.[dataIndex];
                                return (
                                    <View key={label} className="truck-card__content--item at-row at-row__align--center at-row__justify--between">
                                        <View className="kb-color__grey">{label}</View>
                                        <View>{render ? render(val, data) : val}</View>
                                    </View>
                                )
                            })
                        }
                        {
                            hasPhone && (
                                <View hoverClass="kb-hover" onClick={handleCall} className="truck-card__content--item at-row at-row__align--center at-row__justify--between truck-card__content--item-phone">
                                    <View className="kb-icon kb-icon-tel" />
                                    <View>车主电话</View>
                                </View>
                            )
                        }
                    </View>
                </View>
            )
            : null
    )
}

export default TruckCard;