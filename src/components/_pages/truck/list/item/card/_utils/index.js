import { View } from '@tarojs/components';
import TruckCardType from '../type';
import TruckCardTypeKind from '../type/kind';
import { renderTruckload } from '../type/_utils';

export function useTruckCardList({ isOrderDetail, isDetail }) {
  const item0 = isOrderDetail
    ? {
        label: '车牌号',
        dataIndex: 'vehicle_no',
      }
    : {
        label: '品牌型号',
        dataIndex: 'model',
        render: (v, record) => `${record?.brand}-${v}`,
      };
  const truckCardList = [
    item0,
    {
      label: '车厢类型',
      dataIndex: 'type',
      render: (v, record) => (
        <View className='at-row'>
          <View>
            <TruckCardTypeKind data={record} />
          </View>
          <View className='kb-spacing-xs-lr'>|</View>
          <View>
            <TruckCardType data={{ type: v }} short />
          </View>
          <View className='kb-spacing-xs-lr'>|</View>
          <View>{renderTruckload(record?.truckload)}</View>
        </View>
      ),
    },
    {
      label: '续航',
      dataIndex: 'full_load_endurance',
      render: (v) => `${v || 0}km`,
    },
    {
      label: '最大速度',
      dataIndex: 'max_speed',
      render: (v) => `${v || 0}m/s`,
    },
  ];
  if (isDetail) {
    return truckCardList.concat([
      {
        label: '货箱尺寸',
        dataIndex: 'vehicle_size',
      },
    ]);
  }
  return truckCardList;
}
/**
 *
 * @description 业务状态：0-下架，1-上架
 */

export function checkTruckIsRemove(data) {
  return `${data?.biz_status}` === '0';
}
