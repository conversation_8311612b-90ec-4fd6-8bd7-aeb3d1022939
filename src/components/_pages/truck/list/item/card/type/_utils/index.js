import { useDispatch, useSelector } from 'react-redux';
import { getTruckTypeListIgnoreBrand } from '~/services/truck/add';
import { save } from '~base/actions/global';
import { useUpdate } from '~base/hooks/page';

//车辆类型
export function getTruckTypeKindLabel(vehicle_type) {
  const vehicleTypeLabels = {
    1: '常规',
    2: '冷链',
  };
  return vehicleTypeLabels[`${vehicle_type}`];
}


// 容积
export function renderTruckload(v) {
  return v ? `${v?.value || v}m³` : '';
}

// label
export function getTruckTypeLabel(type, labels) {
  const typeItem = labels.find((item) => `${item.value}` === `${type}`);
  return typeItem?.label || type;
}

// 渲染容积+类型
export const renderTruckLabel = (data, labels, short) => {
  const { goods_volume, vehicle_type, truckload = goods_volume, type = vehicle_type } = data || {};
  if (!labels) return '';

  const typeLabel = type ? `${getTruckTypeLabel(type, labels)}${!short ? '货车' : ''}` : '';
  const truckloadLabel = renderTruckload(truckload);

  return [truckloadLabel, typeLabel].filter((item) => !!item).join(' - ');
};

export function useTruckTypeLabels() {
  const dispatch = useDispatch();
  const { truckTypeLabels } = useSelector((state) => state.global);

  const run = () =>
    getTruckTypeListIgnoreBrand().then((t) => {
      dispatch(
        save({
          truckTypeLabels: t,
        }),
      );
      return t;
    });

  useUpdate(({ logined }) => {
    if (logined) {
      run();
    }
  });

  return {
    labels: truckTypeLabels,
    run,
  };
}
