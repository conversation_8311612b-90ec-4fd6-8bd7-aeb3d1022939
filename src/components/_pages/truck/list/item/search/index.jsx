import { View } from "@tarojs/components";
import KbSearch from '~base/components/search';
import TruckSearchHistory from "./history";
import { useRef, useState } from "react";
import './index.scss';

const TruckSearch = (props) => {
    const { value, onChange } = props;
    const searchRef = useRef();
    const searchHistoryRef = useRef();

    // 搜索
    const handleSearch = v => {
        onChange?.(v);
        searchHistoryRef.current?.setHistory?.(v);
    }

    // 点击历史记录
    const handleHistoryChange = (v) => {
        searchRef.current?.update(v);
        onChange?.(v);
    }


    return (
        <View className="truck-search">
            <View className="truck-search__header">
                <KbSearch actionRef={searchRef} trigger='click' onSearch={handleSearch} theme='white' ghostSearchButton placeholder='请输入搜索车辆' />
            </View>

            <View className="truck-search__body">
                <View className="truck-search__body--inner">
                    {
                        value
                            ? props.children
                            : <TruckSearchHistory actionRef={searchHistoryRef} onChange={handleHistoryChange} />
                    }
                </View>
            </View>

        </View>
    )
}

export default TruckSearch