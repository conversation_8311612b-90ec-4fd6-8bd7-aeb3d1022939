import { ScrollView, View } from "@tarojs/components"
import { useStorage } from "~base/utils/storage";
import { useEffect, useImperativeHandle, useMemo, useState } from "react";
import { isArray } from "lodash";
import classNames from "classnames";
import './index.scss';


const defaultMax = 10; // 默认展示的最大数量，超过10个收起

const TruckSearchHistory = (props) => {
    const [deleteActive, setDeleteActive] = useState(false);
    const [collapsed, setCollapsed] = useState(true);
    const { actionRef, value, onChange } = props;
    const { data, update, remove } = useStorage('truckSearchHistory', true);

    // 清除
    const handleSwitchDeleteActive = (a) => setDeleteActive(a);
    const handleDelete = (i) => {
        if (i >= 0) {
            update(pre => {
                const newData = [...pre.data];
                newData.splice(i, 1);
                return newData;
            });
        } else {
            remove();
        }
    };

    // 点击
    const handleClick = (v, index) => {
        if (deleteActive) {
            handleDelete(index);
            return;
        }
        onChange?.(v);
    }

    useImperativeHandle(actionRef, () => ({
        setHistory: (v) => {
            if(!v) return;
            update(pre => {
                const { data } = pre || {};
                if (isArray(data)) {
                    if (!data.includes(v)) { data.unshift(v); }
                    return data;
                }
                return [v];
            })
        }
    }));

    // 展开或收起
    const handleSwitchCollapsed = () => setCollapsed(pre => !pre);

    // 渲染列表
    const { list = [], total = 0 } = useMemo(() => {
        const { data: d } = data || {};
        if (isArray(d)) {
            return {
                list: collapsed ? d.slice(0, defaultMax) : d,
                total: d.length
            }
        }
        return {};
    }, [collapsed, data]);

    const collapsedCls = classNames('history-list__item', {
        'history-list__item-collapsed': collapsed
    });

    useEffect(() => {
        if (total === 0) {
            // 全部清楚
            setDeleteActive(false);
        }
    }, [total]);

    return (
        <ScrollView scrollY className="kb-scrollview">
            <View className="truck-search-history">
                {
                    total > 0 && (
                        <View className="history-header">
                            <View className="history-header__title">历史记录</View>
                            {
                                !deleteActive
                                    ? (
                                        <View className="kb-icon kb-icon-delete" hoverClass="kb-hover-opacity" onClick={() => handleSwitchDeleteActive(true)} />
                                    )
                                    : (
                                        <View className="history-delete">
                                            <View className="history-delete__item" hoverClass="kb-hover-opacity" onClick={() => handleDelete()}>全部删除</View>
                                            <View className="history-delete__item-line" />
                                            <View className="history-delete__item" hoverClass="kb-hover-opacity" onClick={() => handleSwitchDeleteActive(false)}>完成</View>
                                        </View>
                                    )
                            }
                        </View>
                    )
                }

                <View className="history-list">
                    {
                        list.map((item, index) => (
                            <View key={item} className="history-list__item" hoverClass="kb-hover" onClick={() => handleClick(item, index)}>
                                {item}
                                {deleteActive && <View className="at-icon at-icon-close" hoverStopPropagation />}
                            </View>
                        ))
                    }
                    {
                        total > defaultMax && (
                            <View className={collapsedCls} hoverClass="kb-hover" onClick={handleSwitchCollapsed}>
                                <View className="kb-icon kb-icon-arrow" />
                            </View>
                        )
                    }
                </View>
            </View>

        </ScrollView>
    )
}

export default TruckSearchHistory