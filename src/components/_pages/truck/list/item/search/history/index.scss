.truck-search-history {
    .history {
        &-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: $spacing-v-md $spacing-h-md;
            background-color: $color-grey-9;
            position: sticky;
            left: 0;
            top: 0;

            &__title {
                font-weight: bold;
            }

            .kb-icon-delete {
                font-size: $icon-font-size-base;
                color: #969799;
            }
        }

        &-list {
            padding: 0 $spacing-h-md;
            padding-bottom: $spacing-v-md;
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: $spacing-v-md;

            &__item {
                border-radius: $border-radius-arc;
                padding: $spacing-v-xs $spacing-h-lg;
                color: #646566;
                background-color: $color-white;
                display: flex;
                align-items: center;

                .kb-icon-arrow {
                    transform: rotate(-90deg);
                    transition: all 0.3s;
                }

                &-collapsed .kb-icon-arrow {
                    transform: rotate(90deg);
                }

                .at-icon-close {
                    font-size: $icon-font-size-sm;
                    color: #C8C9CC;
                    padding-left: $spacing-h-sm;
                }
            }
        }

        &-delete {
            display: flex;
            align-items: center;
            font-size: $font-size-base;

            &__item {
                &-line {
                    width: $width-base;
                    height: 20px;
                    background-color: $color-grey-1;
                    margin: 0 $spacing-h-lg;
                }
            }
        }
    }
}