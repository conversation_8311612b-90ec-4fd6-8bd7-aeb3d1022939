import { useEffect, useMemo, useState } from "react";
import Truck<PERSON>istItem from "..";
import { TruckSelectContext, useTruckSelectContextValue } from "./_utils/context";
import TruckSearch from "../search";
import TruckSelectConfirm from "./confirm";
import PageLayout from "~base/components/page/layout";
import { View } from "@tarojs/components";

const TruckSelect = (props) => {
    const { search, api, data: dataProps, onConfirm } = props;
    const [selectedData, setSelectedData] = useState([]);
    const { ids, value, selected, onSwitch, onSelectAll, isSelectedAll } = useTruckSelectContextValue({
        defaultEnable: true,
        multiple: false,
        onSelect: setSelectedData // 包含所有车辆数据
    });

    const [active, setActive] = useState({ keywords: '' });
    const handleSearchChange = v => setActive({ keywords: v });

    const activeMerge = useMemo(() => {
        return {
            ...active,
            order_id: dataProps?.order_id
        }
    }, [active, dataProps]);

    // 确认选择
    const handleConfirm = () => onConfirm?.(selectedData, dataProps);

    const isRenderConfirm = !search || !!active.keywords;

    return (
        <TruckSelectContext.Provider value={value}>
            <View className="kb-page kb-page-cover-auto">
                <PageLayout
                    renderFooter={
                        isRenderConfirm
                            ? <TruckSelectConfirm type={dataProps?.type} data={selectedData} onConfirm={handleConfirm} />
                            : null
                    }
                >
                    {
                        search
                            ? (
                                <TruckSearch value={active.keywords} onChange={handleSearchChange}>
                                    <TruckListItem enableSelect active={activeMerge} api={api} />
                                </TruckSearch>
                            )
                            : (
                                <TruckListItem enableSelect api={api} active={activeMerge} />
                            )
                    }
                </PageLayout>
            </View>
        </TruckSelectContext.Provider>
    )
}

export default TruckSelect;