import { View } from "@tarojs/components"
import { AtButton } from "taro-ui";
import { useCallback, useState } from "react";
import debounce from "lodash/debounce";
import TruckCardType from "../../card/type";
import './index.scss';

const confirmTypeMap = {
    '0': '确认车辆',
    '1': '确认更换车辆'
}

const TruckSelectConfirm = (props) => {
    const { data, onConfirm, type = '0' } = props;
    const [loading, setLoading] = useState(false);

    const handleConfirm = useCallback(debounce(
        async () => {
            if (loading) return;
            setLoading(true);
            try {
                await onConfirm(data);
            } catch (error) { }
            setLoading(false);
        },
        500,
        {
            leading: true,
            trailing: false,
        }
    ), [data]);

    const hasSelected = !!data?.vehicle_no;

    const confirmText = confirmTypeMap[type];

    return (
        hasSelected
            ? (
                <View className="kb-page__safe-area truck-select-confirm__wrapper">
                    <View className="truck-select-confirm">
                        <View>
                            <View className="kb-size__base kb-color__grey">已选车辆：</View>
                            <View className="kb-color__brand kb-spacing-sm-t">{data?.vehicle_no} | <TruckCardType data={data} /></View>
                        </View>
                        <View>
                            <AtButton loading={loading} type='primary' circle onClick={handleConfirm}>{confirmText}</AtButton>
                        </View>
                    </View>
                </View>
            )
            : null
    )
}

export default TruckSelectConfirm;