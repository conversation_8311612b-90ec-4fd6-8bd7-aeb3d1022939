import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { checkTruckIsRemove } from '../../card/_utils';

// 操作类型
const operableTypeLabels = {
  up: '上架',
  down: '下架',
};

// 获取操作类型
function getOperableType(list) {
  if (list.length === 0) return '';
  const everyIsDown = list.every((item) => checkTruckIsRemove(item));
  const everyIsUp = !everyIsDown ? list.every((item) => !checkTruckIsRemove(item)) : false;
  return everyIsDown ? 'up' : everyIsUp ? 'down' : '';
}

export const TruckSelectContext = createContext({});

export function useTruckSelectContext() {
  const ctx = useContext(TruckSelectContext);
  return ctx;
}

/**
 *
 * @param {{
 *   defaultEnable?:boolean;
 *   multiple?:boolean;
 * }} props
 * @returns
 */
export function useTruckSelectContextValue(props) {
  const { defaultEnable = false, multiple = true, onSelect } = props || {};
  const [ids, setIds] = useState([]);
  const [enable, setEnable] = useState(defaultEnable);
  const [selected, setSelected] = useState([]);
  const ref = useRef({ list: [] });

  // 清空
  const clean = useCallback(() => {
    setSelected([]);
  }, []);

  // 加载数据
  const load = useCallback((_, list) => {
    setIds(list.filter((item) => !item.disabled).map((item) => item.id));
    ref.current.list = list;
  }, []);

  // 勾选事件
  const onChange = useCallback(({ disabled, id }) => {
    if (disabled) return;
    setSelected((pre) => {
      if (!multiple) {
        // 单选
        return !pre.includes(id) ? [id] : [];
      }
      const n = [...pre];
      const index = n.indexOf(id);
      if (index >= 0) {
        n.splice(index, 1);
      } else {
        n.push(id);
      }
      return n;
    });
  }, []);

  // 全选
  const onSelectAll = (checked) => {
    checked ? setSelected(ids) : clean();
  };

  // 检查是否已选中
  const check = useCallback((data) => selected.includes(data?.id), [selected]);

  // 切换批量操作
  const onSwitch = () => {
    setEnable((pre) => !pre);
    clean();
  };

  const value = useMemo(() => {
    return {
      enable,
      onChange,
      check,
      clean,
      load,
    };
  }, [enable, selected, onChange, check]);

  const isSelectedAll = useMemo(
    () =>
      selected.length > 0 &&
      ids.length === selected.length &&
      ids.every((item) => selected.includes(item)),
    [ids, selected],
  );

  // 可操作类型：根据选中的数据，全是下架的：up，全是上架的：down，部分：为空
  const [operableType, setOperableType] = useState('');
  const operableTypeLabel = operableTypeLabels[operableType];

  // 选中
  useEffect(() => {
    const selectedList = ref.current.list.filter((item) => selected.includes(item.id));

    // 可操作类型
    setOperableType(getOperableType(selectedList));

    if (multiple) {
      onSelect?.(selectedList);
    } else {
      onSelect?.(selectedList[0]);
    }
  }, [selected]);

  return {
    ids,
    value,
    selected,
    isSelectedAll,
    operableType,
    operableTypeLabel,
    onSelectAll,
    onSwitch,
  };
}
