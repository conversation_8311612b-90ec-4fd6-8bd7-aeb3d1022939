import { Text, View } from "@tarojs/components"
import classNames from "classnames";
import { checkTruckIsRented, checkTruckIsServicing } from "../status/_utils";
import './index.scss';
import { countTruckDateSpace, formatTruckDate } from "./_utils";

const TruckDate = (props) => {
    const { className, data } = props;
    const cls = classNames('truck-date', className);
    const isServicing = checkTruckIsServicing(data);
    const isRented = checkTruckIsRented(data);

    const isRender = isServicing || isRented;

    return (
        <>
            {
                isRender
                    ? (
                        <View className="truck-date__wrapper">
                            <View className={cls}>
                                {
                                    isServicing
                                        ? (
                                            <>
                                                <View className="kb-spacing-sm-b">用车时间</View>
                                                <View className="kb-color__brand">
                                                    {formatTruckDate(data?.delivery_using_time)}
                                                </View>
                                            </>
                                        )
                                        : (
                                            <>
                                                <View className="kb-spacing-sm-b">租期<Text className="kb-color__brand kb-spacing-sm-lr">{countTruckDateSpace(data?.lease_start_day, data?.lease_end_day)}</Text>天</View>
                                                <View>
                                                    <Text className="kb-color__brand">
                                                        {formatTruckDate(data?.lease_start_day)}
                                                    </Text>
                                                    <Text className="kb-color__grey kb-spacing-sm-lr">至</Text>
                                                    <Text className="kb-color__brand">
                                                        {formatTruckDate(data?.lease_end_day)}
                                                    </Text>
                                                </View>
                                            </>
                                        )
                                }
                            </View>
                        </View>
                    )
                    : null
            }
        </>
    )
}

export default TruckDate;