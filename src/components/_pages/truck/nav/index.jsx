import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { AtIcon } from 'taro-ui';
import TruckAdd from '../list/item/add';
import './index.scss';

const TruckNav = (props) => {
  const { onSuccess } = props;
  const handleSearch = () => {
    Taro.navigator({
      url: 'truck/search',
      force: false,
    });
  };

  return (
    <View className='truck-nav'>
      <TruckAdd onSuccess={onSuccess} />
      <View className='truck-nav__search' onClick={handleSearch} hoverClass='kb-hover'>
        <AtIcon prefixClass='kb-icon' value='search' />
        搜索车辆
      </View>
    </View>
  );
};

export default TruckNav;
