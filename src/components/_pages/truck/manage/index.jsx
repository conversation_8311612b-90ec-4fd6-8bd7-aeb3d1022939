import KbLongList from '@base/components/long-list';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { serviceURL } from '~/services/_utils';
import { useLongList } from "~base/components/long-list/hooks";
import BatteryIcon from '../battery';
import { AtButton, AtIcon } from 'taro-ui';
import { useState } from 'react';
import { truckOpenOrClose } from '~/services/truck/dispatch';
import { getTruckRentListApi } from '~/services/truck';
import './index.scss';
import TruckManageStatus from './status';

const TruckManageList = () => {
    const { list, config } = useLongList(getTruckRentListApi);
    const [openedId, setOpenedId] = useState('');

    // 任务管理
    const handleClickTask = (item) =>
        Taro.navigator({
            url: 'task/manage',
            options: {
                vehicle_no: item.vehicle_no
            }
        });

    // 派车
    const handleClick = (item) => {
        Taro.navigator({
            url: 'truck/dispatch/depart',
            options: {
                vehicle_no: item.vehicle_no,
                vehicle_owner_id: item.vehicle_owner_id
            }
        });
    }

    // 切换开启操作工具：开机/关机按钮
    const handleOpenTools = (item) => {
        setOpenedId(openedId === item.vehicle_owner_id ? '' : item.vehicle_owner_id);
    }

    // 开机/关机
    const handleClickTools = (action, item) => {
        truckOpenOrClose({
            action,
            vehicle_no: item.vehicle_no
        });
    }

    return (
        <KbLongList data={config} enableMore className='truck-manage-list'>
            <View className="kb-list">
                {
                    list.map(item => (
                        <View key={item.vehicle_owner_id} className="kb-list__item--wrapper kb-box">
                            <View className="kb-list__item" hoverClass='kb-hover' onClick={() => handleClickTask(item)}>
                                <View className="item-content">
                                    <View className='at-row at-row__align--center'>
                                        <View className='kb-spacing-md-r'>{item.vehicle_no}</View>
                                        <View><BatteryIcon quantity={item.battery_power || '0'} unlock={`${item.unlock}` === '1'} charging={`${item.battery_status}` === '1'} /></View>
                                    </View>
                                </View>
                                <View className="item-extra">
                                    <TruckManageStatus data={item} />
                                    <AtIcon prefixClass='kb-icon' value='arrow' className='kb-color-greyer kb-icon-size__xs' />
                                </View>
                            </View>
                            <View className="at-row at-row__align--center at-row__justify--between kb-spacing-md-b">
                                <View className='kb-spacing-md-l'>
                                    <AtButton onClick={() => handleClick(item)} circle size='small' type='secondary'>派车</AtButton>
                                </View>
                                <View hoverClass='kb-hover-opacity' className='kb-spacing-md-r kb-size__base kb-color__greyer' onClick={() => handleOpenTools(item)}>
                                    {openedId === item.vehicle_owner_id ? '收起' : '更多'}
                                </View>
                            </View>
                            {
                                openedId === item.vehicle_owner_id && (
                                    <View className="truck-manage-list__tools">
                                        <View className='truck-manage-list__tools--item' hoverClass='kb-hover' onClick={() => handleClickTools('close', item)}>
                                            关机
                                        </View>
                                        {/* <View className='truck-manage-list__tools--item' hoverClass='kb-hover' onClick={() => handleClickTools('open', item)}>
                                            开机
                                        </View> */}
                                    </View>
                                )
                            }
                        </View>
                    ))
                }
            </View>
        </KbLongList>
    )
}

export default TruckManageList;