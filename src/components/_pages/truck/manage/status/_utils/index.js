/**
 *
 * @description vehicle_business_status 车辆状态 0 初始化 1 空闲 2 车辆分配了订单或已装货 3 路径规划中 4 去停靠点路上 5 停靠在停靠点 6 车辆在回停靠点路上
 * @param {*} data
 */
export function formatTruckManagerStatus(data) {
  const { online, vehicle_business_status } = data;

  if (`${online}` !== '1') {
    return '0';
  }
  return ['0', '1'].includes(`${vehicle_business_status}`) ? '1' : '2';
}

export function getTruckManagerStatusLabel(data) {
  const statusMap = {
    0: '离线',
    1: '空闲',
    2: '使用中',
  };

  return statusMap[`${data?.status}`];
}
