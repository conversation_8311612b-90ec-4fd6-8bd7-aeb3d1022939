import classNames from "classnames";
import { formatTruckManagerStatus, getTruckManagerStatusLabel } from "./_utils";
import { View } from "@tarojs/components";
import './index.scss';

const TruckManageStatus = (props) => {
    const { data } = props;
    const status = formatTruckManagerStatus(data);
    const label = getTruckManagerStatusLabel({ status });
    const cls = classNames("truck-manage-status", `truck-manage-status__${status}`);

    return (
        <View className={cls}>
            {label}
        </View>
    )
}

export default TruckManageStatus;