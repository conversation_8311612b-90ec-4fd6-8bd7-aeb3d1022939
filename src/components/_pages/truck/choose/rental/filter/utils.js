/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useMemo, useState } from 'react';
import { useExtraList } from '~/components/_pages/index/client/extra/utils';
import { useOrderConfig } from '~/components/_pages/index/client/utils';

export const useFilter = (props) => {
  const { active: value, updateActive } = props;
  const [searchData, setSearchData] = useState({
    car_type: [],
    vehicle_type: [],
    goods_volume: [],
    rental_money: [],
  });
  const [isOpened, setIsOpened] = useState(false);

  const defaultConfig = useOrderConfig(searchData);
  const { list: filterList = [] } = useExtraList(1);

  const onOk = () => {
    updateActive(transSearchData(searchData));
    setIsOpened(false);
  };

  const onClose = () => {
    setSearchData(value);
    setIsOpened(false);
  };

  const handleChange = (key, v) => {
    setSearchData((prev) => {
      const newData = { ...prev };
      newData[key] = Array.isArray(v) ? v : [v.value || v.key];
      if (!newData['vehicle_type'].length) {
        newData['goods_volume'] = [];
      }
      return newData;
    });
  };

  useEffect(() => {
    if (isOpened && !filterSearchData2list(value).length) {
      setSearchData({
        vehicle_type: [defaultConfig.vehicle_type[0].value],
        ...transSearchData(value, true),
      });
    }
  }, [isOpened, value, defaultConfig.vehicle_type]);

  const count = useMemo(
    () =>
      filterSearchData2list(value).reduce((acc, key) => {
        return acc + value[key].split(',').length;
      }, 0),
    [value],
  );

  return {
    searchData,
    count,
    isOpened,
    setIsOpened,
    onOk,
    defaultConfig,
    filterList,
    handleChange,
    onClose,
  };
};

const transSearchData = (searchData, toArray = false) => {
  return filterSearchData2list(searchData).reduce((acc, key) => {
    const v = searchData[key];
    acc[key] = toArray ? v : v.join(',');
    return acc;
  }, {});
};

const filterSearchData2list = (value) => {
  return Object.keys(value).filter((key) => value[key].length);
};
