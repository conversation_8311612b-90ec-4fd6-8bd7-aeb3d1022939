/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { AtBadge, AtButton, AtFloatLayout, AtIcon } from 'taro-ui';
import KbListBox from '~/components/_pages/order/listbox';
import './index.scss';
import { useFilter } from './utils';

const TruckChooseRentalFilter = (props) => {
  const {
    searchData,
    count,
    isOpened,
    setIsOpened,
    handleChange,
    onOk,
    onClose,
    filterList,
    defaultConfig,
  } = useFilter(props);

  return (
    <>
      <AtBadge value={count}>
        <AtIcon
          prefixClass='kb-icon'
          value='filter'
          className={`kb-size__lg kb-spacing-sm-l ${
            count > 0 ? 'kb-color__brand' : 'kb-color__greyer'
          }`}
          hoverClass='kb-hover-opacity'
          onClick={() => setIsOpened(true)}
        />
      </AtBadge>
      <AtFloatLayout className='kb-choose-rental-filter' isOpened={isOpened} onClose={onClose}>
        <View className='kb-spacing-md-lr'>
          {filterList.map((item) => (
            <View className='kb-spacing-md-tb' key={item.key}>
              <View className='kb-size__base2 kb-text__left'>{item.label}</View>
              <KbListBox
                className='kb-package__base'
                selectedActive='ghost'
                list={defaultConfig?.[item.key]}
                onChange={(v) => handleChange(item.key, v)}
                selectted={searchData[item.key]}
                multi
              />
            </View>
          ))}
        </View>
        <View className='kb-choose-rental-filter__footer at-row'>
          <AtButton type='secondary' onClick={onClose}>
            取消
          </AtButton>
          <AtButton type='primary' onClick={onOk}>
            确定
          </AtButton>
        </View>
      </AtFloatLayout>
    </>
  );
};

export default TruckChooseRentalFilter;
