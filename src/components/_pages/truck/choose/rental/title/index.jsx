/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import TruckChooseRentalFilter from '../filter';

const TruckChooseRentalTitle = (props) => {
  return (
    <View className='at-row at-row__align--center kb-size__lg'>
      <Text>选择车型</Text>
      <TruckChooseRentalFilter {...props} />
    </View>
  );
};

TruckChooseRentalTitle.options = {
  addGlobalClass: true,
};

export default TruckChooseRentalTitle;
