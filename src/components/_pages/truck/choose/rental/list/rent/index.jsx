/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';

const RentalTruckRent = ({ data }) => {
  return (
    <View className='kb-border-t kb-spacing-md-t at-row at-row__align--center at-row__justify--between'>
      <Text>日租金</Text>
      <Text className='kb-color__bold'>
        <Text className='kb-size__xl'>￥{data?.rent}</Text>
      </Text>
    </View>
  );
};

RentalTruckRent.options = {
  addGlobalClass: true,
};

export default RentalTruckRent;
