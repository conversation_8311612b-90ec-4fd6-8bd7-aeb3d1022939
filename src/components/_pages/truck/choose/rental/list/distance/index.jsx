/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import classNames from 'classnames';
import './index.scss';

const RentalTruckDistance = ({ data, className }) => {
  return (
    <View
      className={classNames(
        'at-row at-row__align--center kb-spacing-md-tb kb-size__base2',
        className,
      )}
    >
      <Image className='location-img' src='https://cdn-img.kuaidihelp.com/truck/location.png' />
      <Text className='kb-spacing-sm-lr'>上海市长宁区金钟路968号</Text>
      <Text className='kb-color__brand'>距您10km</Text>
    </View>
  );
};

RentalTruckDistance.options = {
  addGlobalClass: true,
};

export default RentalTruckDistance;
