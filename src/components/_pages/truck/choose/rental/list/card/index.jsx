/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Image, Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { useMemo } from 'react';
import { orderNext } from '~/components/_pages/index/client/_utils/orderProcess';
import { randomCode } from '~base/utils/utils';
import RentalTruckDistance from '../distance';
import RentalTruckInfo from '../info';
import RentalTruckRent from '../rent';
import './index.scss';
import { createRentalTruckInfo } from './utils';

const RentalTruckCard = ({ className, data = {}, source, showDistance }) => {
  const isOrderList = source === 'orderList';
  const isMatchList = source === 'matchList';
  const isOrderDetail = source === 'orderDetail';
  const noOperate = isMatchList || isOrderList || isOrderDetail;

  const info = useMemo(
    () => createRentalTruckInfo({ data, isOrderList, isMatchList, isOrderDetail }),
    [data, isOrderList, isMatchList, isOrderDetail],
  );

  const handleClick = () => {
    if (noOperate) return;
    orderNext({
      url: 'order/create/rental',
      key: randomCode(),
      data: {
        rentalTruckInfo: data,
      },
    });
  };

  return (
    <View
      className={classNames('kb-rental-card kb-box kb-spacing-md', className)}
      hoverClass={noOperate ? '' : 'kb-hover'}
      onClick={handleClick}
    >
      <View className='at-row at-row__align--start kb-spacing-md-b'>
        <View
          className={classNames('kb-rental-card__left', {
            'kb-rental-card__left--disabled': false,
          })}
        >
          <Image
            className='kb-rental-card__img'
            src='https://cdn-img.kuaidihelp.com/truck/truck_1.png'
          />
        </View>
        <View className='kb-rental-card__right'>
          {info.map((item, index) => (
            <View
              key={item.key}
              className={classNames('at-row', {
                'kb-margin-md-t': index != 0,
              })}
            >
              <Text className='kb-rental-card__label'>{item.label}</Text>
              <Text className='kb-rental-card__value'>{item.value}</Text>
            </View>
          ))}
        </View>
      </View>
      {(isOrderList || isMatchList) && <RentalTruckInfo data={data} />}
      {!isOrderList && !isMatchList && (
        <>
          <RentalTruckRent data={data} />
          {showDistance && <RentalTruckDistance className='kb-spacing-none-b' />}
        </>
      )}
    </View>
  );
};

RentalTruckCard.options = {
  addGlobalClass: true,
};

export default RentalTruckCard;
