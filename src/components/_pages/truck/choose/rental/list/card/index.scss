/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-rental-card {
  color: $color-black-1;
  font-size: $font-size-base2;
  &__left {
    position: relative;
    overflow: hidden;
    border-radius: $border-radius-md;
    &--disabled {
      // filter: grayscale(10%);
      &::after {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 140px;
        height: 140px;
        color: $color-white;
        font-size: $font-size-lg;
        line-height: 140px;
        text-align: center;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: $border-radius-circle;
        transform: translate(-50%, -50%);
        content: '已租满';
      }
      &::before {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.5);
        content: '';
      }
    }
  }
  &__img {
    width: 240px;
    height: 240px;
  }
  &__right {
    flex: 1;
    flex-wrap: wrap;
    box-sizing: border-box;
    padding-left: $spacing-h-md;
    overflow: hidden;
  }
  &__label {
    width: 150px;
    color: $color-grey-1;
  }
  &__value {
    flex: 1;
    white-space: wrap;
    text-align: right;
    word-break: break-all;
  }
  .kb-spacing-none-b {
    padding-bottom: 0;
  }
}
