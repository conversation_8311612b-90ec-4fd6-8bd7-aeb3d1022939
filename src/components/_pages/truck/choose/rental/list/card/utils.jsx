/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import TruckCardType from '~/components/_pages/truck/list/item/card/type';

export const createRentalTruckInfo = ({ data, isOrderList, isMatchList, isOrderDetail }) => {
  return [
    {
      label: '品牌型号',
      key: 'brand',
      value: data.brand,
    },
    {
      label: '车厢类型',
      key: 'vehicle_type',
      value: <TruckCardType data={data} short />,
    },
    {
      label: '满载续航',
      key: 'full_load_endurance',
      value: data.full_load_endurance ? `${data.full_load_endurance}km` : '',
      hide: data.status < 4 || !isOrderList,
    },
    {
      label: '最大速度',
      key: 'max_speed',
      value: data.max_speed ? `${data.max_speed}m/s` : '',
      hide: data.status < 4 || !isOrderList,
    },
    // {
    //   label: '车辆颜色',
    //   key: 'color',
    //   value: data.color,
    //   hide: isOrderList || isMatchList || isOrderDetail,
    // },
    {
      label: '货箱尺寸',
      key: 'exxternal_dimensions',
      value: data.exxternal_dimensions,
      hide: data.status < 4,
    },
  ].filter((item) => !item.hide);
};
