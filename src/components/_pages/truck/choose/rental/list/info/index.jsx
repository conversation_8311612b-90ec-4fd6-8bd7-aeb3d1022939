/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Text, View } from '@tarojs/components';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { AtIcon } from 'taro-ui';
import './index.scss';

const RentalTruckInfo = ({ data, isReturn, isEarly }) => {
  const { lease_start_day, lease_hours, lease_end_day, stop_name } = data;

  const rentRange = useMemo(() => {
    const totalHours = Number(lease_hours);
    if (!totalHours || Number.isNaN(totalHours) || totalHours <= 0) return '';

    if (totalHours < 24) {
      return `${totalHours}小时`;
    }

    const days = Math.floor(totalHours / 24);
    const hours = totalHours % 24;

    if (hours === 0) {
      return `${days}天`;
    }

    return `${days}天${hours}小时`;
  }, [lease_hours]);

  return (
    <View className='rental-truck-info'>
      <View className='at-row at-row__align--center at-row__justify--between'>
        <View>
          起租日期
          <Text className='kb-color__brand kb-spacing-md-lr'>
            {dayjs(lease_start_day).format('YYYY-MM-DD HH:mm')}
          </Text>
        </View>
        <View>
          租期<Text className='kb-color__brand kb-spacing-sm-lr'>{rentRange}</Text>
        </View>
      </View>
      <View className='at-row at-row__align--center kb-spacing-md-tb'>
        <View>
          结束日期
          <Text className='kb-color__brand kb-spacing-md-lr'>
            {dayjs(lease_end_day).format('YYYY-MM-DD HH:mm')}
          </Text>
        </View>
      </View>
      <View className='at-row at-row__align--start'>
        <View>收车地址</View>
        <View className='kb-spacing-md-l'>
          <View>{stop_name}</View>
          {/* <View className='kb-size__base kb-color__greyer kb-spacing-sm-t'>
            <View>上海市长宁区新泾镇协和路1158号</View>
            <View>联系人：张三 13618923915</View>
          </View> */}
        </View>
      </View>
      {false && (
        <View className='at-row at-row__align--center kb-spacing-md-t'>
          <View className='kb-spacing-sm-r'>
            已租
            <Text className='kb-color__brand'> 2 </Text>天
          </View>
          <View className='kb-size__base kb-color__grey'>|</View>
          <View className='kb-spacing-sm-l'>
            提前
            <Text className='kb-color__brand'> 2 </Text>天还车
          </View>
        </View>
      )}
      {isReturn && isEarly && (
        <View className='at-row at-row__align--start kb-spacing-md-t kb-spacing-md-t'>
          <AtIcon
            prefixClass='kb-icon'
            value='info-circle'
            className='kb-size__sm kb-color__greyer'
          />
          <View className='kb-margin-sm-l kb-size__sm kb-color__greyer'>
            还车7天后，或车主未发起结算，押金员额退还
          </View>
        </View>
      )}
    </View>
  );
};

RentalTruckInfo.options = {
  addGlobalClass: true,
};

export default RentalTruckInfo;
