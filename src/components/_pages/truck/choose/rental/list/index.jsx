/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import { useMemo } from 'react';
import { rentalTruckListApi } from '~/services/rental';
import LongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import RentalTruckCard from './card';
// import RentalTruckDistance from './distance';

const TruckChooseRentalList = ({ search, vehicle_owner_id }) => {
  const { list, config } = useLongList(rentalTruckListApi);

  const active = useMemo(() => {
    const { car_type, rental_money = '', vehicle_type = '', goods_volume = '' } = search;
    const [rent_min = 0, rent_max = 0] = rental_money.split('-');
    return {
      type: car_type,
      vehicle_type,
      truckload: goods_volume,
      rent_min: rent_min || 0,
      rent_max: rent_max || 0,
      vehicle_owner_id,
    };
  }, [search, vehicle_owner_id]);

  return (
    <LongList data={config} active={active} enableMore noDataText='附近没有匹配的车辆'>
      {list.map((item) => (
        <View key={item.id} className='kb-spacing-md-lr'>
          {/* <RentalTruckDistance data={item} /> */}
          <RentalTruckCard className='kb-margin-md-b' key={item.id} data={item} />
        </View>
      ))}
    </LongList>
  );
};
export default TruckChooseRentalList;
