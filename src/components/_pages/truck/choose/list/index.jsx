/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro from '@tarojs/taro';
import { checkOrderIsRental } from '~/components/_pages/order/list/card/rental/bars/utils';
import { confirmTake } from '~/services/index';
import { getMatchRentOrderTruckListApi } from '~/services/rental';
import { getMatchOrderTruckListApi } from '~/services/truck';
import longListRefresherManager from '~base/components/long-list/refresher';
import TruckSelect from '../../list/item/select';
import { serverIndexOrderRefresherKey } from '../_utils';
import './index.scss';

const TruckChooseList = (props) => {
  const { onSelect, search, data } = props;
  const isRental = checkOrderIsRental(data);

  // 确认选择
  const handleConfirm = async (truckData, pathParams = {}) => {
    const { type, order_id } = data;
    const res = await confirmTake(
      {
        order_id,
        vehicle_no: truckData.vehicle_no,
        type,
        isRental,
        ...pathParams,
      },
      {
        toastError: !isRental,
      },
    );
    const isSuccess = `${res?.code}` === '0';
    if (isRental && !isSuccess) {
      if (res?.code == 36666) {
        Taro.kbModal({
          title: '温馨提示',
          content: res.msg,
          onConfirm: () => {
            handleConfirm(truckData, { confirm: 1 });
          },
        });
      } else {
        Taro.kbToast({
          text: res.msg,
        });
      }
      return;
    }
    if (isSuccess) {
      longListRefresherManager.trigger(serverIndexOrderRefresherKey);
      Taro.navigator({
        url: 'order/detail',
        target: 'self',
        options: {
          order_id,
        },
      });
    }
  };

  return (
    <TruckSelect
      search={search}
      onConfirm={handleConfirm}
      onSelect={onSelect}
      data={data}
      api={isRental ? getMatchRentOrderTruckListApi : getMatchOrderTruckListApi}
    />
  );
};

export default TruckChooseList;
