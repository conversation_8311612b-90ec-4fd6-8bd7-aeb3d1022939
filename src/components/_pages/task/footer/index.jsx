import { View } from "@tarojs/components"
import TaskMoreBar from "../moreBar";
import { dateCalendar } from "~base/utils/utils";
import './index.scss';

const TaskFooter = (props) => {
    const { data, isShare, onSuccess } = props;
    const { start_at, end_at, auth_code } = data;

    return (
        <View className="task-list__footer">
            <View>
                {
                    (start_at || end_at || auth_code) && (
                        <>
                            {
                                start_at && (
                                    <View className='footer-item'>
                                        <View className='footer-item__label'>任务开始</View>
                                        <View className='footer-item__date'>{dateCalendar(start_at, { timer: true })}</View>
                                    </View>
                                )
                            }
                            {
                                end_at && (
                                    <View className='footer-item'>
                                        <View className='footer-item__label'>任务结束</View>
                                        <View className='footer-item__date'>{dateCalendar(end_at, { timer: true })}</View>
                                    </View>
                                )
                            }
                            {
                                auth_code && !isShare && (
                                    <View className='footer-item'>
                                        <View className='footer-item__label'>授权码</View>
                                        <View className='footer-item__code'>{auth_code}</View>
                                    </View>
                                )
                            }
                        </>
                    )
                }
            </View>
            <TaskMoreBar data={data} isShare={isShare} onSuccess={onSuccess} />
        </View>
    )
}

export default TaskFooter;