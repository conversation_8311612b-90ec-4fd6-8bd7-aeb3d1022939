import debounce from 'lodash/debounce';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useRandomIDAndBounding } from '~base/hooks/observer';
import { rpxToPx } from '~base/utils/unit';

const moveViewMinHeight = 400; // 单位为rpx

export function useMoveArea({ onMoveChange }) {
  const [enableRefresh, setEnableRefresh] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const [active, setActive] = useState(false);
  const ref = useRef({
    preDisabled: disabled,
    changedY: 0,
    curY: 0,
    moveMin: 0,
    moveMax: 0,
    maxHeight: 0,
    startY: 0,
    moveY: 0,
  });
  const [posY, setPosY] = useState(0);

  const [id] = useRandomIDAndBounding(
    (res) => {
      if (!active) return;
      const { height } = res;
      const moveMin = height - rpxToPx(moveViewMinHeight);
      ref.current.moveMin = moveMin;
      ref.current.maxHeight = height;
      const defaultY = moveMin;
      ref.current.curY = defaultY;
      ref.current.changedY = defaultY;
      setPosY(defaultY); // 设置默认位置
    },
    [active],
  );

  // 移动位置便跟
  const onChange = (e) => {
    if (disabled) return;
    const { y } = e.detail;
    ref.current.changedY = y;
  };

  // 纵向移动
  const onVTouchMove = (e) => {
    if (disabled) return;
    const [{ clientY } = {}] = e.touches || e.changedTouches || [];
    const { startY } = ref.current;
    ref.current.moveY = startY - clientY;
  };

  const setPosYDebounce = useCallback(
    debounce(
      (p) => {
        setPosY(p);
      },
      100,
      { trailing: true, leading: false },
    ),
    [],
  );

  // 触摸结束
  const onTouchEnd = () => {
    if (disabled) return;
    const { curY, changedY, maxHeight, moveMin, moveMax, moveY } = ref.current;
    const threshold = 50; // 移动距离超过50生效
    const nextPosY = Math.abs(moveY) < threshold ? curY : moveY > 0 ? moveMax : moveMin;
    ref.current.curY = nextPosY;
    ref.current.changedY = nextPosY;
    setPosY(changedY);
    setPosYDebounce(nextPosY);
  };

  // 禁用：应当仅外部触发可用，onTouchStart 时不应该通过此方法更新disabled
  const onActiveChange = (a) => {
    const preDisabled = !a;
    setActive(a);
    setDisabled(preDisabled);
    setEnableRefresh(preDisabled);
    ref.current.preDisabled = preDisabled;
  };

  // 点击位置：根据位置，切换是否禁用
  const onTouchStart = (e) => {
    if (ref.current.preDisabled) return; // 外部彻底禁用，不做处理；
    const { moveMin, moveMax, curY } = ref.current;
    const [{ clientY } = {}] = e.touches || e.changedTouches || [];
    const isMax = curY <= moveMax;
    const isMin = curY >= moveMin;
    ref.current.startY = clientY;

    const notTop = clientY - curY > (isMax ? 200 : 50);
    const d = isMax ? notTop : false; // 最大位置时，点击非头部，禁止滑动，否则都可滑动；
    const r = isMin ? notTop : d; // 已滑到最小位置，非头部可刷新，否则禁止滑动时可刷新；

    if (d) {
      // 禁用前，先将滑块位置恢复
      onTouchEnd();
    }
    setEnableRefresh(r);
    setDisabled(d);
  };

  const isMoveMin = posY === ref.current.moveMin; // 拖动到最小位置
  const isMoveMax = posY === ref.current.moveMax; // 拖动到最大位置

  useEffect(() => {
    onMoveChange({
      isMoveMax,
      posY,
    });
  }, [isMoveMax]);

  return {
    posY,
    id,
    isMoveMin,
    isMoveMax,
    disabled,
    enableRefresh,
    onChange,
    onTouchEnd,
    onVTouchMove,
    onActiveChange,
    onTouchStart,
  };
}
