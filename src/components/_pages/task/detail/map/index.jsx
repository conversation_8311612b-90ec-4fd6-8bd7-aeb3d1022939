import { View } from "@tarojs/components";
import { useEffect, useMemo } from "react";
import LineMap from "~/components/_pages/line/map";
import { getOrderVehiclePath } from "~/services/order/detail";
import { useRequest } from "~base/utils/request/hooks";
import { formatOrderRoutes, formatOrderVehicleCurrentPath } from "./_utils";
import { checkTaskCanShowTracks } from "../../status/_utils";
import { getTaskVehiclePath } from "~/services/task/detail";

const TaskDetailMap = (props) => {
    const { className, data: orderData, onReady } = props;
    const { task_items, vehicle, id } = orderData || {};
    const { data, run } = useRequest(getTaskVehiclePath, { manual: true });

    const showMap = useMemo(() => data?.length > 0 && task_items?.length > 0, [data, task_items]);

    useEffect(() => {
        if (showMap) {
            onReady?.()
        }
    }, [showMap]);

    const points = useMemo(() => {
        if (!showMap) return [];
        return [formatOrderRoutes(task_items, data), formatOrderVehicleCurrentPath(vehicle)];
    }, [showMap]);

    useEffect(() => {
        if (id && checkTaskCanShowTracks(orderData)) {
            run({ id });
        }
    }, [id]);

    return (
        showMap
            ? (
                <View className={className}>
                    <LineMap points={points} />
                </View>
            )
            : null
    )
}

export default TaskDetailMap;