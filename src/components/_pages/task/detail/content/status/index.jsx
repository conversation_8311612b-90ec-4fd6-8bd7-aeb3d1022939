import { View } from "@tarojs/components";
import BatteryIcon from "~/components/_pages/truck/battery";
import { checkTaskTruckIsDone, getTaskStatusLabel } from "../../../status/_utils";
import TruckStatus from "~/components/_pages/truck/list/item/status";
import './index.scss';

const TaskDetailContentStatus = (props) => {
    const { data } = props;

    return (
        <View className="task-detail-status">
            <View className="detail-status">{getTaskStatusLabel(data)}</View>
            {
                !checkTaskTruckIsDone(data)
                    ? (
                        <View className="detail-status-info">
                            <BatteryIcon quantity={data?.realtime?.battery_power || '0'} />
                            <TruckStatus data={data?.realtime} />
                            <View>{data?.vehicle_no}</View>
                        </View>
                    )
                    : null
            }
        </View>
    )
}

export default TaskDetailContentStatus;