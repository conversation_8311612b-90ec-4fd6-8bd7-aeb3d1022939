import { View } from "@tarojs/components"
import './index.scss';
import TimeLineExtend from "~base/components/time-line/extend";
import { useFormatTracks } from "~/components/_pages/order/detail/content/tracks/_utils";

const TaskDetailContentTracks = (props) => {
    const { data } = props;
    const { formatted } = useFormatTracks(data);

    return (
        <View className="task-detail-tracks kb-box kb-spacing-md">
            <View className="task-detail-tracks__title">任务记录</View>
            <TimeLineExtend items={formatted.tracks} />
        </View>
    )
}

export default TaskDetailContentTracks;