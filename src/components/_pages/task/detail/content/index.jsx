import { View } from "@tarojs/components";
import LongList from "~base/components/long-list";
import { useLongList } from "~base/components/long-list/hooks";
import { formatResponseDefaultNonList } from "~base/components/long-list/create";
import classNames from "classnames";
import { useImperativeHandle, useMemo, useRef, useState } from "react";
import { serviceURL } from "~/services/_utils";
import { AtButton } from "taro-ui";
import { checkTaskTruckIsCancel, checkTaskTruckIsDone } from "../../status/_utils";
import './index.scss';
import TaskDetailContentTracks from "./tracks";
import TaskDetailContentStatus from "./status";
import TaskDetailContentStops from "./stops";
import TaskDetailNavigation from "./navigation";

const TaskDetailContent = (props) => {
    const { isMoveMax, params, authCode, actionRef, showMap, enableRefresh: enableRefreshProps } = props;
    const [showAuthCodeModalBtn, setShowAuthCodeModalBtn] = useState(false);
    const authCodeModalRef = useRef();
    const ref = useRef({ simpleData: null });

    const { config, data } = useLongList(serviceURL('/VehicleTask/getDispatchInfo'), {
        api: {
            data: { id: params?.id, auth_code: params?.auth_code },
        },
        isNonList: true
    });

    // 数据加载
    const handleLoad = ([data]) => props?.onLoad(data);

    const rootCls = classNames('task-detail-content', {
        'task-detail-content__bar': showMap,
        'task-detail-content__max': isMoveMax
    });

    useImperativeHandle(actionRef, () => ({
        loader: () => config.loader()
    }));

    // 非已取消的订单，都允许刷新
    const enableRefresh = useMemo(() => !checkTaskTruckIsCancel(data) && enableRefreshProps, [data, enableRefreshProps]);

    const isDone = checkTaskTruckIsDone(data);

    return (
        <>
            <TaskDetailNavigation data={data} />
            <View className={rootCls}>
                <LongList
                    data={config}
                    onLoad={handleLoad}
                    enableRefresh={enableRefresh}
                    noScrollview={showMap && !isMoveMax}
                >
                    <View className="task-detail-content__desc">
                        <TaskDetailContentStatus data={data} />
                        {!isDone && <TaskDetailContentStops data={data} />}
                        <TaskDetailContentTracks data={data} />
                    </View>
                </LongList>
            </View>
        </>
    )
}

export default TaskDetailContent;