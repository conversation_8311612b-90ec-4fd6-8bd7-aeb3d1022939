.task-detail-navigation {
    background-color: $color-white;
    display: flex;
    align-items: center;
    border-radius: $border-radius-arc;
    padding: $spacing-v-md $spacing-h-md;
    gap: $spacing-h-md;
    width: 80%;

    &__wrapper {
        padding: $spacing-v-md $spacing-h-md;
        display: flex;
        justify-content: flex-end;
    }

    .navigation {
        &-image {
            width: 100%;
            height: 100%;

            &__box {
                width: 90px;
                height: 90px;
                border-radius: $border-radius-circle;
                overflow: hidden;
                border: $border-lightest;
            }
        }

        &-info {
            flex-grow: 1;
        }

        &-icon {
            width: 70px;
            height: 70px;
            vertical-align: middle;
        }
    }
}