import { Image, RootPortal, Text, View } from "@tarojs/components";
import mapIcon from '@/assets/client/images/<EMAIL>';
import Taro from "@tarojs/taro";
import { formatNumeral } from "~base/utils/numeral";
import DateTimer from "~base/components/dateTimer";
import './index.scss';
import { useMemo } from "react";

const TaskDetailNavigation = (props) => {
    const { data } = props;
    const { current_stop_name, current_stop_id, distance, task_items } = data || {}

    const handleNavigation = () => {
        const cur = task_items?.find(item => `${item.id}` === `${current_stop_id}`);
        if (!cur) return;
        Taro.openLocation({
            name: current_stop_name,
            latitude: 1 * cur.lat,
            longitude: 1 * cur.lon
        });
    }

    return (
        current_stop_name && distance
            ? (
                <View className="task-detail-navigation__wrapper">
                    <View className="task-detail-navigation">
                        <View className="navigation-image__box">
                            <Image className="navigation-image" src="https://cdn-img.kuaidihelp.com/truck/truck_1.png" mode="widthFix" />
                        </View>
                        <View className="navigation-info">
                            <View>距离{current_stop_name}站<Text className="kb-color__brand">{formatNumeral(distance / 1000)}</Text>公里</View>
                            <View className="kb-size__base kb-spacing-sm-t">预计用时<Text className="kb-color__red"><DateTimer type='countdown' formatter='HH时mm分ss秒' start={2} /></Text></View>
                        </View>
                        <View hoverClass="kb-hover-opacity" onClick={handleNavigation}>
                            <Image className="navigation-icon" src={mapIcon} mode="widthFix" />
                        </View>
                    </View>
                </View>
            )
            : null
    )
}

export default TaskDetailNavigation;