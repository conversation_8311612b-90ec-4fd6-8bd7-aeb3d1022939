import { ScrollView, Swiper, SwiperItem, View } from "@tarojs/components"
import { getCurrentStopInfo } from "~/components/_pages/index/server/order/components/_utils/line";
import { createTaskItemsGroup, getScrollViewWidth } from "./_utils";
import classNames from "classnames";
import { AtIcon } from "taro-ui";
import { useCallback, useState } from "react";
import debounce from "lodash/debounce";
import './index.scss';
import Taro from "@tarojs/taro";

const TaskDetailContentStops = (props) => {
    const { data } = props;
    const groups = createTaskItemsGroup(data);

    const [showLeft, setShowLeft] = useState(false);
    const [showRight, setShowRight] = useState(true);

    const handleScroll = useCallback(
        debounce
            (
                async (e) => {
                    const tolerance = 10;
                    const { scrollLeft, scrollWidth } = e.detail
                    const { width: viewWidth = 0 } = await getScrollViewWidth();
                    if (!viewWidth) return;
                    let l = true, r = true;

                    // 最左侧
                    if (scrollLeft <= tolerance) {
                        l = false
                    }

                    // 最右侧
                    if (scrollLeft + viewWidth >= scrollWidth - tolerance) {
                        r = false;
                    }

                    setShowLeft(l);
                    setShowRight(r);
                },
                300,
                {
                    trailing: true,
                    leading: false
                }
            ),
        []
    );

    const leftCls = classNames("stops-bar stops-bar__left", {
        'stops-bar__hidden': !showLeft
    });

    const rightCls = classNames("stops-bar stops-bar__right", {
        'stops-bar__hidden': !showRight
    });

    return (
        <View className="task-detail-stops">
            <View className={leftCls} />
            <View className={rightCls} />
            {/* <View className="kb-size__base kb-color__grey kb-spacing-md-b">已行驶25分钟，速度{data?.realtime?.speed}m/s</View> */}
            <ScrollView scrollX className="stops-scrollview" onScroll={handleScroll}>
                <View className="stops-row">
                    {
                        groups.map((item, index) => (
                            <View key={item.id} className={classNames('stops-row__item', `stops-row__item-${index}`, {
                                'stops-row__item-active': item.active
                            })}>
                                <View className="item-head">
                                    <View className="item-head-inner">{1 + index}</View>
                                </View>
                                <View className="item-line">
                                    {
                                        item.isCurrent && (
                                            <View className="item-car">
                                                <AtIcon prefixClass="kb-icon" value='car-3' className="kb-color__brand kb-icon-size__lg" />
                                            </View>
                                        )
                                    }
                                    <View className="item-line-inner"></View>
                                </View>
                                <View className="item-info">
                                    <View className="item-info__overflow">{item.stop_name}</View>
                                    <View className="kb-size__base kb-spacing-sm-t">{getCurrentStopInfo(item.type)?.label}</View>
                                </View>
                            </View>
                        ))
                    }
                </View>
            </ScrollView>
        </View>
    )
}

export default TaskDetailContentStops;