.task-detail-stops {
    position: relative;
    padding: 0 40px;
    box-sizing: border-box;

    &,
    &__scrollview {
        width: 100%;
    }

    .stops {
        &-scrollview {
            width: 100%;
        }

        &-row {
            display: flex;
            align-items: center;
            text-align: center;
            justify-content: space-between;
            padding-top: $spacing-v-md;
            padding-bottom: $spacing-v-lg;

            &__item {
                display: flex;
                align-items: center;
                flex-direction: column;
                position: relative;
                width: 33.333333%;
                flex-shrink: 0;

                .item {
                    &-car {
                        position: absolute;
                        left: 50%;
                        top: -50%;
                        display: flex;
                        align-items: center;
                        transform: translateX(-50%);
                    }

                    &-head {
                        width: 45px;
                        height: 45px;
                        margin-bottom: $spacing-v-md;
                        position: relative;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        z-index: 1;
                        font-size: $font-size-base;

                        &-inner {
                            background-color: #DCDEE0;
                            border-radius: $border-radius-circle;
                            width: 100%;
                            height: 100%;
                            color: $color-white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }

                    &-line {
                        position: absolute;
                        z-index: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 100%;
                        height: 40px;
                        top: 0;
                        right: -50%;

                        &-inner {
                            height: $width-base;
                            display: inline-flex;
                            width: 100%;
                            background-color: #C8C9CC;
                        }
                    }

                    &-info {
                        width: 100%;

                        &__overflow {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                        }
                    }

                }

                &:last-child .item-line {
                    display: none;
                }

                &-0 {
                    .item-head {
                        &-inner {
                            font-size: 0;
                            width: 20px;
                            height: 20px;
                        }
                    }

                    &.stops-row__item-active {
                        .item-head {
                            position: relative;

                            &-inner {
                                box-shadow: 0 0 3px 8px rgba($color: $color-brand, $alpha: 0.15);
                            }
                        }
                    }
                }

                &-active {

                    .item-head,
                    .item-line {
                        &-inner {
                            background-color: $color-brand;
                        }
                    }
                }
            }
        }

        &-bar {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            border-radius: $border-radius-circle;
            background-color: #C8C9CC;
            color: $color-white;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 1;
            transition: opacity 0.3s ease-in-out;

            &::before {
                font-family: "kb-icon";
                content: "\e620";
                font-size: $icon-font-size-xs * 0.5;
            }

            &__hidden {
                opacity: 0;
            }

            &__left {
                left: 0;
                transform: rotate(180deg);
            }

            &__right {
                right: 0;
            }
        }
    }
}