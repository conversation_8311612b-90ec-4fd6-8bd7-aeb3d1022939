import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';

export function createTaskItemsGroup(data) {
  let { task_items, current_stop_id } = data || {};

  if (isArray(task_items)) {
    const activeIndex = task_items.findIndex((item) => `${item.id}` === `${current_stop_id}`);
    if (activeIndex >= 0) {
      task_items = task_items.map((item, index) => ({
        ...item,
        isCurrent: activeIndex - 1 === index,
        active: activeIndex > index,
      }));
    }
    return task_items;
  }
  return [];
}

// 获取宽度
let scrollViewBoundingCache = null;
export const getScrollViewWidth = () => {
    return new Promise((resolve) => {
        if (scrollViewBoundingCache) {
            resolve(scrollViewBoundingCache);
            return;
        }
        const query = Taro.createSelectorQuery();
        query.select('.kb-page >>> .stops-scrollview').boundingClientRect().exec(res => {
            const [item] = res;
            scrollViewBoundingCache = item;
            resolve({
                ...item
            });
        });
    })
}
