// status 0 初始化 1 待发车 2 行驶中 3 停靠中 4 已完成 5 已取消
export function getTaskStatusLabel(data) {
  const statusMap = {
    0: '待发车',
    1: '待发车',
    2: '行驶中',
    3: '停靠中',
    4: '已完成',
    5: '已取消',
  };

  return statusMap[`${data?.status}`];
}

// 检查是否可显示轨迹
export function checkTaskCanShowTracks(data) {
  const enableStatus = ['1', '2', '3'];
  return enableStatus.includes(`${data?.status}`);
}

// 任务车辆待发车
export function checkTaskTruckIsReady(data) {
  return `${data?.status}` === '1';
}

// 任务车辆行驶中
export function checkTaskTruckIsDriving(data) {
  return `${data?.status}` === '2';
}

// 任务车辆停靠中
export function checkTaskTruckIsStop(data) {
  return `${data?.status}` === '3';
}

// 任务车辆已完成
export function checkTaskTruckIsDone(data) {
  return `${data?.status}` === '4';
}

// 任务车辆已取消
export function checkTaskTruckIsCancel(data) {
  return `${data?.status}` === '5';
}
