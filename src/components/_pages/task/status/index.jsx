import { View } from "@tarojs/components";
import { getTaskStatusLabel } from "./_utils";
import classNames from "classnames";
import './index.scss';

// 车辆状态 0 初始化 1 待发车 2 行驶中 3 已完成 4 已取消
const TaskStatus = (props) => {
    const { data } = props;
    const cls = classNames('task-status', `task-status__${data?.status}`);

    return (
        <View className={cls}>
            {getTaskStatusLabel(data)}
        </View>
    )
}

export default TaskStatus;