import { View } from "@tarojs/components"
import { useCreateBars } from "./_utils";
import { AtButton } from "taro-ui";
import "./index.scss";

const TaskMoreBar = (props) => {
    const { moreBar: { showMore, bars }, onOpenMore, onOperate } = useCreateBars(props);
    const isRender = showMore || bars.length > 0;

    const handleClickBar = (item, e) => {
        e.stopPropagation();
        onOperate?.(item);
    }

    return (
        <>
            {
                isRender && (
                    <View className="task-more-bar">
                        {
                            showMore && (
                                <View className="kb-color__grey" hoverClass="kb-hover-opacity" onClick={onOpenMore}>更多</View>
                            )
                        }
                        {
                            bars.map(item => (
                                <View key={item.key} className="task-more-bar__item">
                                    <AtButton circle type='primary' size='small' onClick={(e) => handleClickBar(item, e)}>{item.label}</AtButton>
                                </View>
                            ))
                        }
                    </View>
                )
            }
        </>
    )
}

export default TaskMoreBar;