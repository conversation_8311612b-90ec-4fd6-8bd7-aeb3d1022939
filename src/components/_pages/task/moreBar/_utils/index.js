// 车辆状态 status 0 初始化 1 待发车 2 行驶中 3 停靠中 4 已完成 5 已取消

import Taro from '@tarojs/taro';
import { useMemo } from 'react';
import { truckCancelTask, truckDepart, truckOpenDoor } from '~/services/task';
import { randomCode } from '~base/utils/utils';
import {
  checkTaskTruckIsDriving,
  checkTaskTruckIsReady,
  checkTaskTruckIsStop,
} from '../../status/_utils';

const showMoreStatus = ['1', '2', '3'];

export function useCreateBars(props) {
  const { data, isShare, onSuccess } = props;
  const { status } = data || {};

  // 触发成功
  const triggerSuccess = (res) => {
    const isSuccess = `${res.code}` === '0';
    if (isSuccess) {
      onSuccess?.();
    }
    return res;
  };

  // 更多操作
  const moreBar = useMemo(() => {
    const showMore = !isShare && showMoreStatus.includes(`${status}`);
    const bars = [];

    const isStop = checkTaskTruckIsStop(data);

    // 停靠中
    if (isStop) {
      bars.push({
        label: '解锁车门',
        key: 'open',
      });
    }

    // 待发车、停靠中且处于分享状态
    if (checkTaskTruckIsReady(data) || (isShare && isStop)) {
      bars.push({
        label: '开始发车',
        key: 'start',
      });
    }

    return {
      showMore,
      bars,
    };
  }, [status, isShare]);

  // 操作
  const onOperate = (item) => {
    const { key } = item;
    switch (key) {
      case 'start': // 开始发车
        Taro.kbModal({
          title: '发车前，确保已完成货物装卸并做好安全检查',
          cancelText: '暂不发车',
          confirmText: '开始发车',
          onConfirm: async () => {
            return await truckDepart({
              id: data?.id,
              auth_code: isShare ? data?.auth_code : null,
            }).then(triggerSuccess);
          },
        });
        break;

      case 'open': // 解锁车门
        Taro.kbModal({
          title: '解锁后，拉起车门把手打开车门',
          cancelText: '取消',
          confirmText: '立即解锁',
          onConfirm: async () => {
            return await truckOpenDoor({
              id: data?.id,
              auth_code: isShare ? data?.auth_code : null,
            }).then(triggerSuccess);
          },
        });
        break;

      case 'cancelAndDispatch': // 取消任务并重新派车
        Taro.kbModal({
          title: '是否取消任务，重新派车',
          cancelText: '取消',
          onConfirm: async () => {
            const res = await truckCancelTask({ id: data?.id }).then(triggerSuccess);
            const isSuccess = `${res.code}` === '0';
            if (isSuccess) {
              Taro.navigator({
                url: '',
              });
            }
            Taro.navigator({
              url: 'truck/dispatch/depart',
              key: randomCode(),
              options: data,
              searchKeys: ['vehicle_no', 'vehicle_owner_id'],
            });
            return res;
          },
        });
        break;

      case 'cancel': // 取消任务
        Taro.kbModal({
          title: '是否取消任务',
          cancelText: '取消',
          onConfirm: async () => {
            return await truckCancelTask({ id: data?.id }).then(triggerSuccess);
          },
        });
        break;

      default:
        break;
    }
  };

  // 弹出更多操作
  const onOpenMore = (e) => {
    e.stopPropagation();
    let items = [
      { key: 'share', label: '分享订单', openType: 'share', info: data, page: 'task' },
      { key: 'cancel', label: '取消任务' },
      { key: 'cancelAndDispatch', label: '取消任务，重新派车' },
    ];

    if (checkTaskTruckIsDriving(data)) {
      items = items.slice(0, 1);
    }

    Taro.kbActionSheet({
      items,
      onClick: (_, item) => {
        onOperate(item);
      },
    });
  };

  return {
    moreBar,
    onOpenMore,
    onOperate,
  };
}
