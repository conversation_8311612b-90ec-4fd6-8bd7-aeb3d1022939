/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useDidShowCom } from '@base/hooks/page';
import request from '@base/utils/request';
import { frequencyLimit, reportAnalytics } from '@base/utils/utils';
import { Image } from '@tarojs/components';
import { useSelector } from '@tarojs/redux';
import Taro, { useEffect, useRef, useState } from '@tarojs/taro';
import isArray from 'lodash/isArray';
import { AtCurtain } from 'taro-ui';
import { adNavigator } from '../sdk';
import { getAdStorageKey, getAdTypeReq } from '../_utils';

/**
 * 微信官方广告组件位 - 插屏广告;
 */

const Index = (props) => {
  const { isOpen, adType = 'order.result' } = props;
  const queryData = getAdTypeReq(adType);
  const adStorageKey = getAdStorageKey(adType);
  const { isVip = false } = useSelector((state) => state.global);
  const [adData, setAdData] = useState(); //当前需要展示的广告;
  const [isOpened, setIsOpened] = useState(false);
  const WXInsertAdRef = useRef(); // 插屏广告
  const AdListRef = useRef(); //全部列表数据
  const refreshRef = useRef(false); //刷新标志
  const reportKey = 'exp_ad';

  useEffect(() => {
    isOpen && !isVip && loaderAd();
    return () => {
      //销毁微信插屏广告
      if (WXInsertAdRef.current && WXInsertAdRef.current.destroy) {
        WXInsertAdRef.current.destroy();
      }
    };
  }, [isOpen, isVip]);

  useDidShowCom(() => {
    if (refreshRef.current) {
      showOwnInsertAd('switch');
      refreshRef.current = false;
    }
  });

  const loaderAd = () => {
    //广告频率限制
    if (frequencyLimit('check', adStorageKey)) {
      console.log('限制用户一天只能看一次插屏广告');
      return;
    }
    request({
      url: '/g_order_core/v2/mina/User/getTablePlaqueBanner',
      data: queryData,
      toastLoading: false,
      onThen: ({ code, data }) => {
        const { list } = data || {};
        if (code == 0 && isArray(list) && list.length > 0) {
          AdListRef.current = data;
          AdListRef.current.oldList = [...list];
          showOwnInsertAd();
        } else {
          showWxInsertAd();
        }
        frequencyLimit('limit', adStorageKey);
      },
    });
  };

  const showOwnInsertAd = (action = 'init') => {
    //自有插屏广告
    /**
     * balance 均匀分配模式 / sequence 主次分配模式
     */
    let { list, oldList, type } = AdListRef.current || {};
    const { id } = adData || {};
    let cIndex = 0,
      len = list.length,
      current;
    switch (type) {
      case 'balance':
        cIndex = Math.floor(Math.random() * len);
        current = list[cIndex];
        AdListRef.current.list.splice(cIndex, 1);
        // 每轮后重置
        if (action == 'switch' && len <= 1) {
          AdListRef.current.list = [...oldList];
        }
        break;
      case 'sequence':
        cIndex = list.findIndex((item) => item.id == id) || 0;
        cIndex = action == 'init' ? 0 : cIndex * 1 + 1;
        if (cIndex > len - 1) cIndex = 0;
        current = list[cIndex];
        break;
    }
    setAdData(current);
    setIsOpened(true);
    reportAnalytics({
      key: reportKey,
      options: `展示广告-${current.title}`,
    });
  };

  const handleAdClick = () => {
    refreshRef.current = true;
    adNavigator({
      ...adData,
      report: {
        key: reportKey,
        options: `点击广告-${adData.title}`,
      },
    });
  };

  const handleCloseOwnAd = () => {
    setIsOpened(false);
  };

  const showWxInsertAd = () => {
    //微信插屏广告
    if (Taro.createInterstitialAd) {
      WXInsertAdRef.current = Taro.createInterstitialAd({
        adUnitId: 'adunit-0b6279a459673f34',
      });
      WXInsertAdRef.current.onLoad(() => {
        reportAnalytics({
          key: 'cpad_onload',
        });
        WXInsertAdRef.current.show().catch((err) => {
          console.log('cpad_err', err);
          reportAnalytics({
            key: 'cpad_errormsg',
            errormsg: err.errMsg,
          });
        });
      });
    }
  };

  return (
    <AtCurtain isOpened={isOpened} onClose={handleCloseOwnAd}>
      <Image
        showMenuByLongpress
        mode='widthFix'
        src={adData && adData.imgUrl}
        onClick={handleAdClick}
      />
    </AtCurtain>
  );
};
Index.defaultProps = {
  isOpen: false,
  adUnitIdIndex: '',
};
Index.options = {
  addGlobalClass: true,
};
export default Index;
