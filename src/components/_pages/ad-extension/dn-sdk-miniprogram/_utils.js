/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro from '@tarojs/taro';
import { SDK } from './index';
import { resolveLocationByAddress } from '@base/utils/gps';

let _wx_ad_sdk = null;
//微信广告skd
export default class WxAdSdk {
  constructor() {
    this.reportOpenIdLock = false;
    this.reportLocationLock = false;
  }

  init() {
    // 微信广告上报数据初始化
    if (process.env.MODE_ENV === 'wkd' && process.env.PLATFORM_ENV === 'weapp') {
      // 是否打开调试模式, 建议仅在调试期间开启
      SDK.setDebug(!!(process.env.DEBUG_ENV === 'OPEN'));
      if (_wx_ad_sdk) return;
      _wx_ad_sdk = new SDK({
        // 数据源ID，必填
        user_action_set_id: 1203327508,
        // 加密key，必填
        secret_key: '4a4c968d5142647d0232f4951178b8e4',
        // 微信小程序APPID，wx开头，必填
        appid: 'wxfaeec4255298e6c6',
        // 是否开启自动采集事件，选填，默认为true
        auto_track: false,
        // 是否开启自动采集设备属性，选填，默认为true
        auto_attr: false,
      });
      console.log('初始化广告sdk===>', _wx_ad_sdk);
    }
  }

  setOpenId(openId) {
    if (openId && _wx_ad_sdk && _wx_ad_sdk.setOpenId) {
      if (this.reportOpenIdLock) return;
      this.reportOpenIdLock = true;
      console.log('广告sdk==>上报openId');
      _wx_ad_sdk.setOpenId(openId);
    }
  }

  report(key, params) {
    if (_wx_ad_sdk && _wx_ad_sdk.track) {
      _wx_ad_sdk.track(key, params);
    }
  }

  reportLocation(opt) {
    const { longitude, latitude, address, city, label } = opt || {};
    console.log('广告sdk==>reportLocation', opt, address);

    const report = (longitude, latitude) => {
      if (this.reportLocationLock) return;
      this.reportLocationLock = true;
      console.log('广告sdk==>上报位置信息');
      this.report('GET_LOCATION', {
        longitude_user: longitude * 1, //用户位置，经度
        latitude_user: latitude * 1, // 用户位置，纬度
        locationformat_user: 'gcj-02', // 用户位置格式
        locationlabel_user: label, // 用户位置标签
      });
      this.report('PURCHASE', {
        app_type: '物流服务', // 小程序服务类目
      });
    };

    if (address) {
      resolveLocationByAddress({ address, city }).then(({ location }) => {
        const [_longitude, _latitude] = location.split(',');
        report(_longitude, _latitude);
      });
    } else {
      report(longitude, latitude);
    }
  }
}
