/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbButton from '~base/components/button';
import Taro, { Fragment } from '@tarojs/taro';
import { noop } from '~base/utils/utils';
import CabinetOpenShelfPackage from './components/shelfPackage';
import CabinetOpenConfirm from './components/confirm';
import CabinetOpenSuccess from './components/success';
import CabinetOpenIntercept from './components/intercept';
import CabinetOpenRejection from './components/rejection';
import { useCabinetOpen } from './_utils/useCabinetOpen';
import React from 'react';

/**
 * @description 所有开柜操作，都在此组件中完成
 * @param {string} type  当前操作类型 支持 input | storage | click 默认=click
 * @param {object} dakInfo 为了取 cabinet_info 字段，也可直接传 device_id 进来
 * @param {object} cabinet_info 同上
 * @param {string} device_id 同上
 * @param {function} handleOpen 替代开柜按钮上 handleOpen 方法
 * @param {object} actionRef 接收后会绑定开柜方法和关闭弹窗方法
 * @param {function} handleOperate 点击开柜动作会执行一次
 * @param {function} handleOpenSuccess 开柜成功后会执行一次
 */

const CabinetOpen = (props) => {
  const { buttonProps, button_label, actionRef, isDetail, data } = props;

  const isReject = data.rejection == 1;

  const {
    restProps,
    baseData,
    pageData,
    updatePageData,
    handleOpen,
    handleOpenCabinet,
    handleContinue,
    handleCloseSuccess,
    batchPay,
  } = useCabinetOpen(props);

  const {
    currentVisible,
    curPackageInfo,
    openSuccessInfo,
    shelfPackageInfo,
    interceptInfo,
    rejectionInfo,
  } = pageData;

  return (
    <Fragment>
      {!actionRef && (
        <KbButton
          type='primary'
          circle
          size='small'
          disabled={isReject}
          {...buttonProps}
          {...restProps}
          onClick={props.handleOpen || handleOpen}
        >
          {isReject ? '已申请退回' : button_label}
        </KbButton>
      )}
      {/* 二次确认弹窗,支付弹窗 */}
      {currentVisible == 'confirm' && (
        <CabinetOpenConfirm
          visible={currentVisible == 'confirm'}
          isDetail={isDetail}
          baseData={baseData}
          curPackageInfo={curPackageInfo}
          updatePageData={updatePageData}
          handleOpenCabinet={handleOpenCabinet}
        />
      )}
      {/* 开门成功 */}
      {currentVisible == 'success' && (
        <CabinetOpenSuccess
          successVisible={currentVisible == 'success'}
          openSuccessInfo={openSuccessInfo}
          handleContinue={handleContinue}
          handleCloseSuccess={handleCloseSuccess}
          handleOpenCabinet={handleOpenCabinet}
          updatePageData={updatePageData}
          baseData={baseData}
        />
      )}
      {/* 货架包裹 */}
      {currentVisible == 'shelf' && (
        <CabinetOpenShelfPackage
          visible={currentVisible == 'shelf'}
          shelfPackageInfo={shelfPackageInfo}
          updatePageData={updatePageData}
        />
      )}
      {/* 出库拦截 */}
      {currentVisible == 'intercept' && (
        <CabinetOpenIntercept
          visible={currentVisible == 'intercept'}
          interceptInfo={interceptInfo}
          updatePageData={updatePageData}
          handleOpenCabinet={handleOpenCabinet}
          batchPay={batchPay}
        />
      )}
      {/* 拒收退回 */}
      {currentVisible == 'rejection' && (
        <CabinetOpenRejection
          visible={currentVisible == 'rejection'}
          rejectionInfo={rejectionInfo}
          handleContinue={handleContinue}
          handleCloseSuccess={handleCloseSuccess}
          handleOpenCabinet={handleOpenCabinet}
          updatePageData={updatePageData}
        />
      )}
    </Fragment>
  );
};

CabinetOpen.defaultProps = {
  type: 'click',
  button_label: '开柜取件',
  handleOpen: null,
  actionRef: null,
  handleOperate: noop,
  updateList: noop,
  handleRemove: noop,
  handleOpenSuccess: noop,
};

CabinetOpen.options = {
  addGlobalClass: true,
};

export default React.memo(CabinetOpen);
