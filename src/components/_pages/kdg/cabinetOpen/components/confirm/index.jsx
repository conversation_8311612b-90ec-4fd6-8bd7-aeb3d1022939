/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { Fragment } from '@tarojs/taro';
import KbModal from '@base/components/modal';
import { Text, View } from '@tarojs/components';
import { padZero } from '../../../_utils';
import { useCabinetOpenConfirm } from './useCabinetOpenConfirm';

// 取件时的二次确认弹窗,已兼容支付功能

const CabinetOpenConfirm = (props) => {
  const { visible, baseData } = props;

  const { onConfirm, onCancel, isNeedPay, outTimeInfo, restPackageInfo } =
    useCabinetOpenConfirm(props);

  return (
    <KbModal
      centered
      top={false}
      closable={false}
      closeOnClickOverlay={false}
      isOpened={visible}
      cancelText='稍后取件'
      confirmText={isNeedPay ? '立即支付' : '确认开柜'}
      cancelButtonProps={{ type: 'secondary' }}
      onConfirm={onConfirm}
      onCancel={onCancel}
      onClose={onCancel}
      rootClass='kb-cabinetOpenModalWrap'
      className='kb-cabinetOpenModalContent'
      footerClass='kb-cabinetOpenModalFooter'
    >
      <View className='kb-cabinetOpenModal'>
        <View className='kb-color-orange kb-background__orange kb-tips'>
          <View className='kb-size__xxl'>确认您当前在快递柜前</View>
          <View className='kb-size__xxl'>避免包裹丢失</View>
        </View>
        <View className='kb-cabinetOpenModal_info kb-cabinetOpenModal_h350 at-row at-row__align--center at-row__justify--center at-row__direction--column'>
          <View className='kb-size__lg'>
            {restPackageInfo.enable_cross_pick ? '前往' : ''}
            {baseData.inn_name}
          </View>
          {restPackageInfo.grid_number && restPackageInfo.isStorage && (
            <View className='kb-size__48 kb-color__black'>
              {restPackageInfo.grid_desc} （
              <Text className='kb-size__72 kb-color__brand'>{restPackageInfo.grid_number}</Text>
              格口）
            </View>
          )}
          {restPackageInfo.number && restPackageInfo.grid_number == 1 ? (
            <View className='kb-size__48 kb-color__black'>
              <Text className='kb-size__72 kb-color__brand'>{restPackageInfo.number}</Text>格口
            </View>
          ) : (
            restPackageInfo.row && (
              <View className='kb-size__48 kb-color__black'>
                <Text className='kb-size__72 kb-color__brand'>{padZero(restPackageInfo.row)}</Text>
                号柜
                <Text className='kb-size__72 kb-color__brand'>{padZero(restPackageInfo.col)}</Text>
                格口
              </View>
            )
          )}
          {/* {!isDetail && (
            <View className='kb-size__base kb-color__grey'>
              {restPackageInfo.brand_cn} {restPackageInfo.waybill}
            </View>
          )} */}
          {restPackageInfo.multi > 1 && (
            <View className='kb-color__brand'>
              柜中有{restPackageInfo.multi}个包裹待取，切勿漏取
            </View>
          )}
          {isNeedPay && (
            <Fragment>
              <View className='kb-size__lg'>超时{outTimeInfo.outTime}</View>
              <View className='kb-size__lg'>需支付{outTimeInfo.fee}元</View>
            </Fragment>
          )}
        </View>
      </View>
    </KbModal>
  );
};

CabinetOpenConfirm.defaultProps = {
  handlePickup: () => {},
  handleOpenCabinet: () => {},
  updatePageData: () => {},
  isDetail: false,
  visible: false,
  baseData: {},
  curPackageInfo: {},
};

CabinetOpenConfirm.options = {
  addGlobalClass: true,
};

export default CabinetOpenConfirm;
