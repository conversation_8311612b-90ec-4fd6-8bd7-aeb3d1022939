/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbModal from '@base/components/modal';
import { Image, Text, View, Fragment, ScrollView } from '@tarojs/components';
import Taro, { useEffect, useState } from '@tarojs/taro';
import KbButton from '~base/components/button';
import KbCheckbox from '~base/components/checkbox';
import { connect } from '@tarojs/redux';
import { get } from '~/actions/brands';
import isUndefined from 'lodash/isUndefined';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import { padZero } from '../../../_utils';

// 取件成功弹窗

const CabinetOpenRejection = (props) => {
  const {
    visible,
    handleContinue,
    handleCloseSuccess,
    handleOpenCabinet,
    rejectionInfo,
    brands,
    dispatchGet,
  } = props;

  const {
    direction,
    number,
    grid_number,
    cabinet_code,
    grid_code,
    baseData,
    gridId: grid_id,
    batch_no,
    waybillList = [],
    waybill_no,
    status,
    message,
  } = rejectionInfo || {};

  const [step, setStep] = useState(1);
  const [checked, setChecked] = useState([]);

  const handleChoose = (item) => {
    const _checked = [...checked];
    const index = _checked.findIndex((v) => v.waybill_no == item.waybill_no);
    if (index > -1) {
      _checked.splice(index, 1);
    } else {
      _checked.push(item);
    }
    setChecked(_checked);
  };

  const handleCancel = () => {
    if (step == 1 && isUndefined(status)) {
      handleOpenCabinet({ ...baseData, type: 'click', second_open: true });
    } else if (step == 2) {
      handleCloseSuccess();
    } else {
      handleOpenCabinet({
        ...baseData,
        type: 'rejectReopen',
        grid_id: waybillList[0].ec_grid,
        waybill_no,
      });
    }
  };

  const handleConfirm = () => {
    if (step == 1) {
      handleContinue();
    } else if (step == 2) {
      handleOpenCabinet({
        ...baseData,
        type: 'getRejectionList',
        batch_no,
      });
      // setStep(3);
    } else if (step == 3) {
      handleCloseSuccess();
    }
  };

  const handleOpen = () => {
    const _checked = checked;
    if (waybillList.length == 1) {
      _checked.push(waybillList[0]);
    }
    if (!_checked.length) {
      Taro.kbToast({
        text: '请选择要退回的包裹',
      });
      return;
    }
    handleOpenCabinet({
      ...baseData,
      type: 'rejection',
      rejection_list: _checked,
      grid_id,
      batch_no,
    });
  };

  const getBrandName = (item) => {
    return item.brand == 'qita'
      ? '其他'
      : (brands[item.brand] && brands[item.brand].name) || item.brand;
  };

  useEffect(() => {
    if (step == 3 && status) {
      setStep(1);
    }
  }, [step, status, message]);

  useEffect(() => {
    if (Array.isArray(waybillList) && waybillList.length) {
      setStep(3);
    }
  }, [waybillList]);

  useEffect(() => {
    dispatchGet();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <KbModal
      centered
      top={false}
      closable={false}
      closeOnClickOverlay={false}
      isOpened={visible}
      cancelText={step == 1 ? (status ? '重开柜门' : '再次开柜') : step == 2 ? '取消' : ''}
      confirmText={step == 1 ? (status ? '投柜完成' : '继续取件') : step == 2 ? '继续退回' : ''}
      cancelButtonProps={{ type: 'secondary' }}
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      onClose={handleCloseSuccess}
      className='kb-cabinetOpenModalContent_rejection'
      rootClass='kb-cabinetOpenModalWrap'
      footerClass='kb-cabinetOpenModalFooter'
    >
      <View className='kb-cabinetOpenModal'>
        <View className='kb-close' hoverClass='kb-hover-opacity' onClick={handleCloseSuccess}>
          <AtIcon prefixClass='kb-icon' value='closed' className='kb-size__x kb-color__grey' />
        </View>
        {step == 1 ? (
          <Fragment>
            {direction ? (
              <View className='kb-directory at-row at-row__align--center'>
                {direction == 'left' && (
                  <Image
                    className='kb-directory__img kb-directory__img-revert kb-margin-md-r'
                    src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
                  />
                )}
                <View className='kb-size__lg'>显示屏{direction == 'left' ? '左侧' : '右侧'}</View>
                {direction == 'right' && (
                  <Image
                    className='kb-directory__img kb-margin-md-l'
                    src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_direction.png'
                  />
                )}
              </View>
            ) : (
              <View className='at-row at-row__justify--center'>
                <Image
                  className='avatar-img'
                  src='https://osscdn-kbad.kuaidihelp.com/admin/ad/2023/06/01/6478142d5caae/<EMAIL>'
                />
              </View>
            )}
            {rejectionInfo && (
              <View className='kb-color__black kb-spacing-md-tb'>
                {number && grid_number == 1 ? (
                  <Text className='kb-size__48'>
                    （格口
                    <Text className='kb-number'>{number}</Text> ）
                  </Text>
                ) : (
                  <View className='kb-size__48 at-row at-row__align--baseline at-row__justify--center'>
                    <Text className='kb-number'>{padZero(cabinet_code)}</Text>
                    号柜 <Text className='kb-number'>{padZero(grid_code)}</Text>
                    格口
                  </View>
                )}
              </View>
            )}
            <View className='kb-color__grey kb-size__sm kb-padding-40-b'>
              柜门已开，
              {isUndefined(status) ? '请取走包裹并关上柜门' : '请将包裹放回柜中，关上柜门'}
            </View>
            {isUndefined(status) && (
              <View
                className='kb-color__brand kb-padding-40-b'
                hoverClass='kb-hover-opacity'
                onClick={() => setStep(2)}
              >
                拒收退回 {'>'}
              </View>
            )}
          </Fragment>
        ) : step == 2 ? (
          <Fragment>
            <View className='kb-size__bold kb-color__brand kb-size__xl'>拒收退回说明</View>
            <View className='kb-margin-xl-t kb-text__left kb-margin-40-b'>
              确保您当前取出的包裹未拆开，可选择拒收退回，已拆包裹不允许拒收退回!
            </View>
          </Fragment>
        ) : step == 3 ? (
          waybillList && Array.isArray(waybillList) ? (
            <Fragment>
              <Image
                className='kb-rejection_img'
                src='https://cdn-img.kuaidihelp.com/yz/miniapp/icon_open.png'
              />
              {waybillList.length > 1 ? (
                <Fragment>
                  <View className='kb-size__bold kb-color__black kb-size__xl kb-text__left kb-spacing-xl-tb'>
                    一格口多包裹，请选择退回包裹
                  </View>
                  <ScrollView scrollY className='kb-rejection-list'>
                    {waybillList.map((item, index) => (
                      <View
                        key={item.waybill_no}
                        className={classNames('kb-spacing-md-tb kb-rejection-item', {
                          'kb-border-t': index > 0,
                        })}
                        hoverClass='kb-hover-opacity'
                        onClick={() => handleChoose(item)}
                      >
                        <View className='at-row at-row__align--center at-row__justify--between'>
                          <Text>{getBrandName(item)} </Text>
                          <Text>{item.waybill_no}</Text>
                          <KbCheckbox
                            checked={checked.findIndex((v) => v.waybill_no == item.waybill_no) > -1}
                            onChange={() => handleChoose(item)}
                          />
                        </View>
                      </View>
                    ))}
                  </ScrollView>
                </Fragment>
              ) : (
                <Fragment>
                  <View className='kb-size__bold kb-color__black kb-size__48'>确认退回</View>
                  <View className='kb-color__black kb-size__xl'>
                    <Text>{waybillList && waybillList[0] && waybillList[0].waybill_no}</Text>
                  </View>
                </Fragment>
              )}
            </Fragment>
          ) : null
        ) : null}
        {step == 3 && (
          <Fragment>
            <KbButton
              className='kb-button__middle kb-margin-xl-tb kb-w300'
              circle
              type='primary'
              onClick={handleOpen}
            >
              确认并打开柜门
            </KbButton>
          </Fragment>
        )}
      </View>
    </KbModal>
  );
};

CabinetOpenRejection.defaultProps = {
  handleOpenCabinet: () => {},
  handleContinue: () => {},
  handleCloseSuccess: () => {},
  rejectionInfo: {},
};

CabinetOpenRejection.options = {
  addGlobalClass: true,
};

export default connect(({ global }) => ({ brands: global.brands }), {
  dispatchGet: get,
})(CabinetOpenRejection);
