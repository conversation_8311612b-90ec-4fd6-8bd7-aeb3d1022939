/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useRef, useCallback, useEffect } from '@tarojs/taro';
import { useSelector } from '@tarojs/redux';
import MD5 from 'crypto-js/md5';
import isFunction from 'lodash/isFunction';
import isEmpty from 'lodash/isEmpty';
import WebSocket from '@base/utils/request/webSocket';
import { sleep } from '@base/utils/utils';
import { useOnUnload } from '~base/hooks/page';
import logger from '~base/utils/logger';

const WEBSOCKET_DEFAULT_OPTIONS = {
  url: 'wss://cabinet.kuaidihelp.com:19501',
  pathname: '/web',
  heartAction: '/v1/Cabinet/heartbeat',
};

/**
 * 配置 getMessage 处理消息方法。
 * 配置 wsOptions参数，动态更新。并支持用updateWebsocketOptions外部更新配置参数。
 *
 * 暴露发送消息方法。
 * 暴露更新ws配置方法。
 *
 */

const ws = {
  current: null,
  getMessage: () => {},
};
const loading = {
  current: null,
};

export function useCabinetWebsocket(props) {
  const { getMessage, wsOptions = {} } = props;

  // const ws = useRef();
  // const loading = useRef(); // loading状态
  const options = useRef(WEBSOCKET_DEFAULT_OPTIONS); // 配置信息

  const { loginData = {} } = useSelector((state) => state.global);
  const { userInfo } = loginData || {};
  const { sessionid, openid, nickname, mobile } = userInfo || {};

  // 手动更新websocket配置数据
  const updateWebsocketOptions = async (params = {}, then = () => {}) => {
    options.current = {
      ...options.current,
      ...params,
    };

    if (then) {
      await sleep(500);
      then();
    }
  };

  // loading控制
  const showLoading = (text) => {
    loading.current = Taro.kbToast({
      status: 'loading',
      text,
    });
  };

  const hideLoading = () => {
    if (loading.current) {
      loading.current.close();
      loading.current = null;
    }
  };

  // 连接
  const connect = useCallback(
    (deviceId) => {
      return new Promise(async (resolve, reject) => {
        const options_ = options.current || {};
        const device_id = deviceId || options_.device_id;
        // console.info('device_id======>66', device_id);
        if (!device_id) {
          Taro.kbToast({ text: '快递柜类型缺失' });
          new Error('device_id缺失');
          reject();
          return;
        }

        console.info('83======>', ws.current);
        if (ws.current) {
          resolve();
          return;
        }
        ws.current = new WebSocket({
          url: options_.url,
          pathname: options_.pathname,
          heart: {
            action: options_.heartAction,
            data: { timestamp: Date.now() / 1000, id: `${MD5(openid)}`, sessionid },
          },
          onOpen: () => {
            showLoading('正在连接 ...');
            // console.info('正在连接======>87');
            resolve();
          },
          onClose: () => {
            hideLoading();
          },
          onError: (err) => {
            logger.info('2025-03-30', 'onError', err);
            Taro.kbToast({
              text: err && err.errMsg ? err.errMsg : '连接失败',
            });
            reject();
          },
          onMessage: (res) => {
            getMessageHandle(res);
          },
          onStatusChange: (status) => {
            console.info('107=======>', status);
            // hideLoading();
          },
        });
      });
    },
    [options, openid, sessionid],
  );

  // 发送消息
  const sendMessage = useCallback(
    async (params) => {
      const { type, data = {} } = params;
      const device_id = data.cabinet_id || data.device_id || options.current.device_id;
      // console.info('123======>sendMessage', device_id);
      const msg = {
        action: 'forward',
        data: {
          id: `${MD5(openid)}`,
          device_id: device_id,
          type,
          form: 'customer',
          courier_id: `${MD5(openid)}`,
          timestamp: Date.now() / 1000,
          data: {
            sid: `${MD5(openid)}`,
            courier_id: `${MD5(openid)}`,
            ecId: device_id,
            channel: 'web',
            phone: mobile,
            name: nickname,
            ...data, // 交互发送信息
          },
        },
      };
      try {
        await connect(device_id);
        if (ws.current) {
          showLoading('请稍后');
          console.info('133======>发送的数据', msg);
          // 更新一次message方法
          ws.current.getMessage = getMessage;
          ws.current.sendMessage(msg);
        } else {
          ws.current = null;
          hideLoading();
        }
      } catch (err) {
        logger.info('2025-03-30', 'sendMessage_catch', {
          device_id,
          openid,
          pickup_code: data.pickup_code,
        });
        ws.current = null;
        hideLoading();
      }
    },
    [connect, mobile, nickname, openid, options, getMessage],
  );

  // 接收消息
  const getMessageHandle = (res) => {
    console.info('getMessageHandle=====>143', res, loading.current);
    if (!res) return;
    const { data: latestMessageData = {}, code, msg } = res;
    const { type = '', data, action } = latestMessageData;

    hideLoading();
    // 心跳包无需处理
    if (action == options.current.heartAction) {
      return;
    }

    if (code != 0) {
      Taro.kbToast({ text: msg || '操作失败' });
      return;
    }

    if (isFunction(ws.current.getMessage)) {
      ws.current.getMessage(type, data, msg);
    }
  };

  // 更新ws配置信息
  useEffect(() => {
    if (!isEmpty(wsOptions)) {
      // console.info('更新wsOptions====>', wsOptions);
      updateWebsocketOptions(wsOptions);
    }
  }, [...wsOptions]);

  // useEffect(() => {
  //   //退出后断开连接
  //   return () => {
  //     // console.info('退出后断开链接', ws.current);
  //     // ws.current = null;
  //     if (ws.current) {
  //       loading.current.close();
  //       loading.current = null;
  //       console.info('close 退出后需要断开连接');
  //       ws.current.destroy({ code: 1000, reason: 'destroy' });
  //     }
  //   };
  // }, []);

  useOnUnload(() => {
    if (ws.current) {
      hideLoading();
      ws.current.destroy({ code: 1000, reason: 'destroy' });
      ws.current = null;
    }
  });

  return {
    sendMessage,
    updateWebsocketOptions,
  };
}
