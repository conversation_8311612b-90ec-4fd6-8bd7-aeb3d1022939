/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';

export const CABINET_GRID_LIST = [
  {
    title: '微格口',
    short: '微',
    key: 'miniature',
    desc: '适合存放文件类、手机等物品',
    desc_short:"适合存放手机类、小物品等",
    index: '1',
  },
  {
    title: '小格口',
    short: '小',
    key: 'small',
    desc: '适合存放春夏季服装类、背包等物品',
    desc_short:"适合存放文件类、夏装等",
    index: '2',
  },
  {
    title: '中格口',
    short: '中',
    key: 'medium',
    desc: '适合存放厚外套等物品',
    desc_short:"适合存放厚外套、鞋子等",
    index: '3',
  },
  {
    title: '大格口',
    short: '大',
    key: 'big',
    desc: '适合鞋子、箱子等物品',
    desc_short:"适合鞋子、箱子等",
    index: '4',
  },
  {
    title: '超大格口',
    short: '超大',
    key: '5',
    desc: '适合羽绒服、迷你行李箱等物品',
    desc_short:"适合羽绒服迷你行李箱等",
    index: '5',
  },
  {
    title: '最大格口',
    short: '最大',
    key: '6',
    desc: '适合薄被子、小型纸箱等物品',
    desc_short:"适合薄被子小型纸箱等",
    index: '6',
  },
];

const formatString = (str) => (parseFloat(str) < 10 ? `0${str}` : str);

export const formatGridInfo = (data) => {
  const { cabinet_code, grid_code, size } = data || {};
  const obj = CABINET_GRID_LIST.find((item) => item.index == size);
  if (obj) {
    return {
      ...(data || {}),
      size: obj.key,
      num: `${formatString(cabinet_code)}${formatString(grid_code)} (${obj.short})`,
    };
  }
  return {};
};

export const cabinetSyncOrderPlace = ({ order_id } = {}) => {
  request({
    url: '/api/weixin/mini/minpost/order/cabinetSyncOrderPlace',
    data: {
      order_id,
    },
    toastLoading: false,
    toastError: true,
  });
};
