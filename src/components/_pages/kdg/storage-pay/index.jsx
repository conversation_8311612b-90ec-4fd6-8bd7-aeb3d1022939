/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbPage from '@base/components/page';
import { Text, View } from '@tarojs/components';

import './index.scss';
import { AtButton } from 'taro-ui';
import { useKdgStoragePay } from './useKdgStoragePay';
import { formatSecondDate } from '../../order/detail-cabinet/_utils/utils';
import { UNIT_TYPE } from '../storage/_utils/useKdgStorage';

const KdgStoragePay = (props) => {
  const { curPrice, curStorageTime, step, unit, onPay } = useKdgStoragePay(props);

  return (
    <KbPage {...props}>
      <View className='kbStoPay'>
        <View className='kbStoPay__item'>
          <View className='kbStoPay__item__price'>
            <Text className='kbStoPay__item__price__icon'>¥</Text>
            <Text>{curPrice}</Text>
          </View>
          <View className='kbStoPay__item__time'>存储时间{formatSecondDate(curStorageTime)}</View>
          <View className='kbStoPay__item__desc'>超时后，需补支持超时费</View>
          <View className='kbStoPay__item__overTime'>
            按{step}元/{UNIT_TYPE[unit]}收费，上不封顶
          </View>
          <AtButton type='primary' circle onClick={onPay}>
            支付并开柜
          </AtButton>
        </View>
      </View>
    </KbPage>
  );
};

export default KdgStoragePay;
