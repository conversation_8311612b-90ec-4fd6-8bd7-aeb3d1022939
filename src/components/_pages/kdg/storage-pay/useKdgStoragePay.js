/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Taro, { useMemo } from '@tarojs/taro';
import { payCabinetOrder } from '../../order/detail-cabinet/_utils/utils';

export function useKdgStoragePay(props) {
  const { curPrice, fee, curStorageTime, order_id, dakInfo, device_id, size } = props;
  const { step, unit } = useMemo(() => {
    const { step, unit } = fee ? JSON.parse(fee) : {};
    return {
      step,
      unit,
    };
  }, [fee]);

  const onPay = async () => {
    await payCabinetOrder(order_id);
    Taro.navigator({
      url: 'kdg/storage/result',
      target: 'self',
      options: {
        dakInfo,
        device_id,
        order_id,
        size,
      },
    });
  };

  return {
    curPrice,
    curStorageTime,
    step,
    unit,
    onPay,
  };
}
