import {
  updateBadgeMap,
  updateSelectedTabIndex,
  updateTabBarMap,
  updateTabBarShow,
} from '@/actions/tabs';
import { checkIsSecondaryTabPage } from '@base/utils/navigator';
import { getPage } from '@base/utils/utils';
import { useDispatch, useSelector } from '@tarojs/redux';
import Taro, { useEffect, useRef } from '@tarojs/taro';
import { tabItemTapCall } from '../../_utils';

const TaroSwitchTab = Taro.switchTab; // 改写switchTab

// 选中tabBar
function selectTabsBar(dispatch, path, tabs) {
  const index = tabs.findIndex((item) => item.pagePath === path);
  if (index < 0) return;
  dispatch(updateSelectedTabIndex(index));
}

let badgeMapCache = null;
let tabBarMapCache = null;

/**
 *
 * @description 修补tabBar相关方法
 */
function patchTabsFun(dispatch, tabs) {
  // 切换红点与徽标
  function switchTabBarBadge(opts, resolve, status) {
    let badgeMap = { ...badgeMapCache };
    const { index: key } = opts;
    if (status === 'set') {
      badgeMap = {
        ...badgeMap,
        [key]: opts,
      };
    } else {
      delete badgeMap[key];
    }
    badgeMapCache = badgeMap;
    dispatch(updateBadgeMap(badgeMap));
    resolve();
  }

  // 切换某个tabBar状态
  function switchTabBarItem(opts) {
    const { index = -1, status = 'show' } = opts || {};
    let tabBarMap = { ...tabBarMapCache };
    tabBarMap[index] = status;
    tabBarMapCache = tabBarMap;
    dispatch(updateTabBarMap(tabBarMap));
  }

  /**
   * tab显示与隐藏
   */

  // 隐藏tabBar
  Taro.hideTabBar = (index = -1) =>
    new Promise((resolve) => {
      if (index >= 0) {
        switchTabBarItem({ index, status: 'hide' });
      } else {
        dispatch(updateTabBarShow(false));
      }
      resolve();
    });
  // 显示tabBar
  Taro.showTabBar = (index = -1) =>
    new Promise((resolve) => {
      if (index >= 0) {
        switchTabBarItem({ index, status: 'show' });
      } else {
        dispatch(updateTabBarShow(true));
      }
      resolve();
    });

  /**
   * 红点、文本设置与移除
   */

  // 显示红点
  Taro.showTabBarRedDot = (opts) =>
    new Promise((resolve) => switchTabBarBadge(opts, resolve, 'set'));
  // 隐藏红点
  Taro.hideTabBarRedDot = (opts) =>
    new Promise((resolve) => switchTabBarBadge(opts, resolve, 'remove'));
  // 设置徽章
  Taro.setTabBarBadge = (opts) => new Promise((resolve) => switchTabBarBadge(opts, resolve, 'set'));
  // 移除徽章
  Taro.removeTabBarBadge = (opts) =>
    new Promise((resolve) => switchTabBarBadge(opts, resolve, 'remove'));

  // 重置tab切换方法，以已修正tabBar选中
  Taro.switchTab = (opts) => {
    const { url } = opts;
    selectTabsBar(dispatch, url, tabs);
    return TaroSwitchTab(opts);
  };
}

export function usePatch(tabs) {
  const { selectedTabIndex: current } = useSelector((state) => state.global);
  const ref = useRef({ current });
  const dispatch = useDispatch();
  patchTabsFun(dispatch, tabs);

  const switchTab = (index) => {
    if (index === ref.current.current) return;
    const { pagePath: url, text } = tabs[index];
    tabItemTapCall({ options: text });

    if (checkIsSecondaryTabPage(url)) {
      // tab二级内页跳转
      Taro.navigator({ url: url.replace('/index', '/target/index'), force: true });
      return;
    }
    Taro.switchTab({
      url,
    });
  };

  useEffect(() => {
    const currentPage = getPage();
    const { path } = currentPage.$router || {};
    selectTabsBar(dispatch, path, tabs);
    // Taro.setTabBarBadge({
    //   index: 2,
    //   text: '领红包',
    //   position: 'left',
    //   animation: 'shakeY',
    // });
  }, []);

  useEffect(() => {
    ref.current.current = current;
  }, [current]);

  return {
    ref,
    current,
    switchTab,
  };
}
