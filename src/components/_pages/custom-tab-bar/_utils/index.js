import { useDidShowCom } from '@base/hooks/page';
import { checkIsTabPage } from '@base/utils/navigator';
import { getPage, getStorage, setStorage } from '@base/utils/utils';
import { useSelector } from '@tarojs/redux';
import Taro, { useEffect, useMemo, useState } from '@tarojs/taro';
import { getAdConfig } from '../../ad-extension/_utils';
import { usePatchCustomTabItem } from '../../userTypeLayout/_utils';

export function getTabBarConfig() {
  const { config: { tabBar = {} } = {} } = Taro.getApp();
  return tabBar;
}

const isCustomRemoteTabs = false;

let remoteTabs = [];
export const getRemoteTabs = () => {
  return new Promise((resolve) => {
    if (isCustomRemoteTabs) {
      if (remoteTabs && remoteTabs.length > 0) {
        resolve(remoteTabs);
        return;
      }
      getAdConfig(
        {
          fourAd: 1,
        },
        true,
      ).then((data) => {
        let list = [];
        if (data && data.length > 0) {
          list = data.map((item) => {
            const { adUrl, imgUrl, imgUrl1, title } = item || {};
            return {
              pagePath: adUrl,
              iconPath: imgUrl,
              selectedIconPath: imgUrl1,
              text: title,
            };
          });
          remoteTabs = list;
        }
        resolve(list);
      });
    } else {
      resolve([]);
    }
  });
};

let remoteTabsList = [];
export function useGetTabBarConfig() {
  const { list = [], selectedColor, color } = getTabBarConfig();
  const [customList, setCustomList] = useState([]);
  const patchTabItem = usePatchCustomTabItem();
  const storageTabsKey = 'storageTabsKey';

  const getStorageAsync = async () => {
    try {
      const res = await getStorage({ key: storageTabsKey });
      return res.data || {};
    } catch (error) {
      return {};
    }
  };

  useEffect(() => {
    if (remoteTabsList && remoteTabsList.length > 0) {
      setCustomList([...remoteTabsList]);
      return;
    }
    if (isCustomRemoteTabs) {
      getStorageAsync().then((res) => {
        const { data = [] } = res || {};
        if (data && data.length > 0) {
          remoteTabsList = data;
          setCustomList([...data]);
        }
        getRemoteTabs().then((res) => {
          if (res && res.length > 0) {
            res = res.map((item, index) => {
              if (!item || !item.pagePath) {
                return list[index];
              }
              return item;
            });
            remoteTabsList = res;
            setCustomList([...res]);
            setStorage({
              key: storageTabsKey,
              data: res,
            });
          }
        });
      });
    }
  }, [list]);

  const { selectedTabIndex } = useSelector((state) => state.global);

  const tabs = useMemo(() => {
    const l =
      customList && customList.length > 0
        ? customList
        : remoteTabsList && remoteTabsList.length > 0
          ? remoteTabsList
          : list;

    const lPatched = l.map((item) => {
      const { showText = true, pagePath, className, text, ...restItem } = item || {};
      return {
        ...restItem,
        showText,
        pagePath: `/${pagePath}`,
        className: `kb-custom-tab__item kb-custom-tab__item--${className} tab-size-${text && showText ? 'normal' : 'full'
          }`,
        ...patchTabItem(item),
      };
    });

    return lPatched;
  }, [list, customList, selectedTabIndex, patchTabItem]);

  return [tabs, selectedColor, color];
}

export function useIsTabPage() {
  const {
    $router: { path },
  } = getPage();
  const isTab = useMemo(() => checkIsTabPage(path), [path]);
  return isTab;
}

/**
 *
 * @description 兼容自定义tab跳转后返回无法恢复的问题
 * @returns
 */
export function useTabBarShow() {
  const { showTabBar } = useSelector((state) => state.global);
  const isTab = useIsTabPage();

  const isCustomTabShow = useMemo(() => {
    const { custom } = getTabBarConfig();
    return custom && isTab && showTabBar;
  }, [showTabBar, isTab]);

  return { isTab, isCustomTabShow };
}
