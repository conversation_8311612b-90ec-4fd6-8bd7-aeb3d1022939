/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getDocumentById } from '@/utils/document';
import { View } from '@tarojs/components';
import { connect } from '@tarojs/redux';
import Taro, { useCallback, useEffect, useState } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import './index.scss';

const agreeMaps = {
  serviceAgreement: getDocumentById(0),
  bindMobileAgreement: getDocumentById(1),
  servicePrivacy: getDocumentById(2),
  rentalAgreement: getDocumentById(4),
};

const Index = (props) => {
  const { agreeType, actionRef, style } = props;
  const [viewUrl, setViewUrl] = useState('');
  // 阻止冒泡
  const onCatch = (e) => {
    e.stopPropagation();
  };
  const signAgreement = () => {};

  const handleShowAgreement = useCallback(() => {
    Taro.navigator({
      url: viewUrl,
      target: 'webview',
      force: true,
    });
  }, [agreeType, viewUrl]);

  useEffect(() => {
    if (!actionRef || !Object.keys(actionRef).includes('current')) return;
    actionRef.current = { signAgreement };
  }, [actionRef]);

  useEffect(() => {
    const { url = '' } = agreeMaps[agreeType] || {};
    setViewUrl(url);
  }, [agreeType]);

  return (
    <View onClick={onCatch} hoverStopPropagation className='kb-agreement'>
      <AtButton className='kb-button__link' size='small' onClick={handleShowAgreement}>
        <View style={style}>《{agreeMaps[agreeType].name}》</View>
      </AtButton>
    </View>
  );
};

Index.options = {
  addGlobalClass: true,
};

Index.defaultProps = {
  agreeType: '',
};

export default connect(({ global }) => ({
  loginData: global.loginData,
}))(Index);
