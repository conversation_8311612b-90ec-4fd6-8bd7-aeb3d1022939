/* eslint-disable import/first */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react-hooks/exhaustive-deps */
import { noop } from '@base/utils/utils';
import Taro, { useMemo } from '@tarojs/taro';
import { useCabinetWebsocket } from '../scan/useWebsocket';

export function useCabinetOpen(props) {
  const { data = {}, updateList = noop } = props;

  // 格式化当前驿站数据，统一驿站名称，取件码，格口，柜子等信息。
  const baseData = useMemo(() => {
    return {
      ...data,
    };
  }, [data]);

  // message方法回调
  const getMessage = (type) => {
    switch (type) {
      case 'deliver_open':
        console.log('柜门已开==>');
        Taro.kbToast({ text: '柜门已开' });
        updateList();
        break;

      default:
        console.info('未知操作类型');
        // Taro.kbToast({ text: `${type}-${resultMsg}未知操作类型` });
        break;
    }
  };

  const { sendMessage } = useCabinetWebsocket({
    getMessage,
    wsOptions: baseData,
  });

  const handleOpen = (data = baseData) => {
    Taro.kbToast({
      status: 'loading',
      text: '正在开柜...',
    });

    sendMessage({
      type: 'deliver_open',
      data: {
        ...data,
      },
    });
  };

  return {
    baseData,
    handleOpen,
  };
}
