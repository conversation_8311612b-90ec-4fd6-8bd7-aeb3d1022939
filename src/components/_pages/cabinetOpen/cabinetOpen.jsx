/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { AtButton } from 'taro-ui';
import './cabinetOpen.scss';
import { useCabinetOpen } from './useCabinetOpen';

const CabinetOpen = (props) => {
  const { handleOpen } = useCabinetOpen(props);

  return (
    <AtButton className='kb-cabinet-open' type='primary' onClick={() => handleOpen()}>
      开柜取件
    </AtButton>
  );
};

CabinetOpen.options = {
  addGlobalClass: true,
};

export default CabinetOpen;
