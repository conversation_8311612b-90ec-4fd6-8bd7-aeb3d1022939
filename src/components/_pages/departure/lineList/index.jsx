/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Input, View } from '@tarojs/components';
import { useEffect, useState } from 'react';
import { AtSwipeAction } from 'taro-ui';
import { second2Minutes } from '~base/utils/utils';
import './index.scss';

const LineList = (props) => {
  const { data, onChange } = props;
  const [list, setList] = useState(data);
  const [changeData, setChangeData] = useState(null);

  useEffect(() => {
    setList(data);
  }, [data]);

  const handleChange = (id, e) => {
    setChangeData({ id, value: e.detail.value });
  };

  const handleBlur = () => {
    if (!changeData) return;
    const _data = [...list];
    const index = _data.findIndex((item) => item.id == changeData.id);
    _data[index].stop_time = changeData.value * 60;
    setList(_data);
    onChange(changeData);
  };

  return (
    <View className='kb-lineList'>
      {list.map((item) => (
        <View key={item.id} className='kb-lineList-item'>
          <AtSwipeAction
            disabled
            className='kb-lineList-swiperAction'
            options={[
              {
                text: '取消',
              },
              {
                text: '确认',
                style: {
                  backgroundColor: '#19b699',
                },
              },
            ]}
          >
            <View className='at-row'>
              <View className='kb-lineList-item__left at-row  at-row__align--center at-row__justify--between'>
                <View className='kb-size__base'>{item.name}</View>
                <View className='kb-num'>{item.count}</View>
              </View>
              <View className='kb-lineList-item__right at-row at-row__align--center'>
                <Input
                  type='number'
                  className='kb-time-input'
                  defaultValue={second2Minutes(item.stop_time)}
                  onInput={(v) => handleChange(item.id, v)}
                  onBlur={() => handleBlur()}
                />
                <View className='kb-spacing-md-r'>分钟</View>
              </View>
            </View>
          </AtSwipeAction>
        </View>
      ))}
    </View>
  );
};

LineList.options = {
  addGlobalClass: true,
};

export default LineList;
