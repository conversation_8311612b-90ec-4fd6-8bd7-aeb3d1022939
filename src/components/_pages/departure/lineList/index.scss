/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-lineList {
  padding: 0 $spacing-h-md;
  &-item {
    width: 100%;
    height: 100px;
    margin-bottom: $spacing-h-md;
    &__left,
    &__right {
      height: 100px;
      font-size: $font-size-base;
      background-color: $color-white;
      border-radius: $border-radius-md;
    }
    &__left {
      position: relative;
      flex: 1;
      margin-right: $spacing-h-md;
      padding: 0 $spacing-h-md;
      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 6px;
        height: 100%;
        background-color: #19b498;
        border-top-left-radius: $border-radius-base;
        border-bottom-left-radius: $border-radius-base;
        content: '';
      }
    }
    &__right {
      width: 240px;
    }
    .kb-num {
      width: 80px;
      height: 42px;
      color: #19b498;
      line-height: 42px;
      text-align: center;
      background: #e6f8f5;
      border-radius: $border-radius-md;
    }
  }
  &-swiperAction {
    .at-swipe-action__content {
      background-color: unset;
    }
  }
}

.kb-time-input {
  flex: 1;
  text-align: center;
  &::after {
    display: none;
  }
}
