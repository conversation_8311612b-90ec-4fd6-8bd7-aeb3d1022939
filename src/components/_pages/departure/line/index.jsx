/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ScrollView, Text, View } from '@tarojs/components';
import classNames from 'classnames';
import { AtIcon } from 'taro-ui';
import { second2Minutes } from '~base/utils/utils';
import './index.scss';

const Line = (props) => {
  const { data = [], title = '默认线路', onClick, className } = props;
  const enableClick = !!onClick;
  const handleClick = () => onClick?.();

  if (!data.length) return null;

  return (
    <View
      className={classNames('kb-line', className)}
      hoverClass={enableClick ? 'kb-hover' : 'none'}
      onClick={handleClick}
    >
      <View className='at-row at-row__align--center kb-size__lg kb-border-b kb-spacing-lg-b'>
        <AtIcon prefixClass='kb-icon' value='line' className='kb-size__base kb-color__brand' />
        <Text className='kb-spacing-sm-l'>{title}</Text>
      </View>
      <View className='at-row at-row__align--center at-row__justify--end kb-color__brand kb-size__sm kb-margin-sm-tb'>
        <Text>滑动查看</Text>
        <AtIcon
          prefixClass='kb-icon'
          value='triangle-right'
          className='kb-size__sm kb-color__brand'
        />
      </View>
      <ScrollView scrollX className='kb-line-scrollView'>
        <View className='at-row at-row__align--start kb-line-scrollView__wrapper'>
          {data.map((item, index) => {
            return (
              <View key={item.station_id} className='kb-line__item'>
                <View
                  className={classNames(
                    'kb-line__item__content at-row at-row__align--center at-row__justify--center at-row__direction--column',
                    {
                      past: item.active,
                    },
                  )}
                >
                  {item.active && (
                    <View className='kb-line__car at-row at-row__justify--center'>
                      <AtIcon prefixClass='kb-icon' value='car' className='kb-line__car-icon' />
                    </View>
                  )}
                  <View className='kb-line__name'>{item.station_name}</View>
                  <View
                    className={classNames('kb-line__time', {
                      point: index == 0 || index == data.length - 1,
                    })}
                  >
                    {index == 0
                      ? '始发'
                      : index == data.length - 1
                      ? '终点'
                      : `${second2Minutes(item.stop_time)}分钟`}
                  </View>
                </View>
              </View>
            );
          })}
        </View>
      </ScrollView>
    </View>
  );
};

Line.options = {
  addGlobalClass: true,
};

export default Line;
