/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.kb-line {
  box-sizing: border-box;
  padding: $spacing-h-lg $spacing-h-md;
  background-color: $color-white;
  border-radius: $border-radius-md;
  &-scrollView {
    width: 100%;
    &__wrapper {
      position: relative;
      padding-top: 50px;
    }
  }
  &__item {
    position: relative;
    width: 148px;
    margin-right: 60px;
    font-size: $font-size-sm;
    &:last-child {
      margin-right: 0 !important;
    }
    &::before,
    &::after {
      position: absolute;
      top: 0px;
      left: 0;
      z-index: 1;
      width: 200px;
      height: 4px;
      background-color: #baebe3;
      content: '';
    }
    &::after {
      right: 0;
      left: unset;
    }
    &__content {
      width: 148px;
      &::after {
        position: absolute;
        top: -8px;
        left: 50%;
        z-index: 2;
        width: 16px;
        height: 16px;
        background-color: $color-white;
        border: 4px solid #1abea3;
        border-radius: $border-radius-circle;
        transform: translateX(-50%);
        content: '';
      }
    }
    .past {
      &::after {
        background-color: #1abea3;
      }
    }
  }
  &__name {
    height: 80px;
    padding-top: $spacing-h-lg;
    text-align: center;
  }
  &__time {
    width: 90px;
    height: 30px;
    color: #1abea3;
    font-size: $font-size-xs;
    line-height: 30px;
    text-align: center;
    background-color: $color-white;
    border: $width-base solid #1abea3;
    border-radius: 18px;
    &.point {
      color: $color-white;
      background: #1abea3;
    }
  }
  &__car {
    position: absolute;
    top: -36px;
    left: 0;
    width: 148px;
    text-align: center;
    &-icon {
      // margin-left: 8px;
      color: $color-brand;
      font-size: $font-size-sm !important;
    }
  }
}
