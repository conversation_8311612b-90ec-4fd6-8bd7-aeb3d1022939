import Taro from "@tarojs/taro";
import KbPage from "~base/components/page";
import Kb<PERSON>ongList from '@base/components/long-list';
import { useLongList } from "~base/components/long-list/hooks";
import { View } from "@tarojs/components";
import { AtIcon } from "taro-ui";
import KbSwipeAction from '@base/components/swipe-action';
import ContactsEditBar from "~/components/_pages/contacts/edit/bar";
import KbSearch from '~base/components/search';
import { Component, useRef, useState } from "react";
import Popconfirm from "~base/components/popconfirm";
import { contactsListApi, removeContacts } from "~/services/contacts";
import PageLayout from "~base/components/page/layout";
import ContactsEditModal from "../edit/modal";

// 操作配置
const swipeActionOptions = [
    {
        key: 'delete',
        text: '删除',
        style: {
            backgroundColor: '#FF5A7A',
        },
    },
];

const ContactsPage = () => {
    const [openedId, setOpenedId] = useState('');
    const searchRef = useRef();
    const { list, config } = useLongList(contactsListApi);
    // 搜索
    const handleSearch = v => {
        config.loader({ keywords: v });
    }

    // 删除
    const handleDelete = async (item) => {
        const res = await removeContacts({ id: item.id });
        const isSuccess = `${res.code}` === '0';
        if (isSuccess) {
            config.loader();
        }
        return isSuccess;
    }

    // 左滑
    const handleSwipeOpened = (id = '') => {
        setOpenedId(id);
    }
    // 左滑点击
    const handleSwipeClick = (item) => handleDelete(item);

    const hasList = list.length > 0;

    return (
        <PageLayout
            renderHeader={
                <View className="kb-spacing-md">
                    <KbSearch actionRef={searchRef} trigger='click' onSearch={handleSearch} theme='white' ghostSearchButton placeholder='请输入姓名和电话查询' />
                </View>
            }
            renderFooter={
                <View className="kb-spacing-md kb-background__white">
                    <ContactsEditBar onSuccess={config.loader} />
                </View>
            }
        >
            <KbLongList data={config} enableMore>
                <View className="kb-list">
                    {
                        list.map(item => (
                            <View key={item.id} className="kb-list__item--wrapper kb-box">
                                <KbSwipeAction
                                    autoClose
                                    isOpened={openedId === item.id}
                                    onOpened={() => handleSwipeOpened(item.id)}
                                    onClosed={() => handleSwipeOpened()}
                                    onClick={() => handleSwipeClick(item)}
                                    rightButtons={swipeActionOptions}
                                >
                                    <View className="kb-list__item">
                                        <View className="item-content">
                                            <View>{item.name}</View>
                                            <View>{item.phone}</View>
                                        </View>
                                        <View className="item-extra">
                                            <View className="at-row at-row__align--center">
                                                <ContactsEditBar data={item} onSuccess={config.loader} />
                                            </View>
                                        </View>
                                    </View>
                                </KbSwipeAction>
                            </View>
                        ))
                    }
                </View>
            </KbLongList>
            <ContactsEditModal />
        </PageLayout>
    )
}

export default ContactsPage;