.contacts-edit-modal {
    z-index: 1011;

    .kb-form {
        &__title {
            font-weight: 400;
            font-size: $font-size-lg;
            padding-top: $spacing-v-xxl;
            padding-bottom: $spacing-v-md;
        }

        &__item {
            min-height: 80px;

            .item {
                &-title {
                    padding: $spacing-v-xs 0;
                    position: relative;
                    padding-right: $spacing-h-md;
                    margin-right: $spacing-h-md;

                    &::after {
                        display: block;
                        position: absolute;
                        content: '';
                        border-right: $border-grey-3-1;
                        height: 20px;
                        width: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        right: 0;
                    }

                    &::before {
                        content: '*';
                        color: $color-red;
                    }
                }

                &-content {
                    .at-input {
                        padding: $spacing-v-xs 0;
                    }
                }
            }

            border-radius: 12px;
            background-color: $color-grey-5-1;

            &--tips {
                font-size: $font-size-base;
                padding-top: $spacing-v-md;
                padding-bottom: $spacing-v-xxl;
                color: $color-grey-1-1;
            }
        }

        &__btn {
            padding: $spacing-v-md $spacing-h-md;
            padding-top: $spacing-v-lg * 5;
        }
    }
}