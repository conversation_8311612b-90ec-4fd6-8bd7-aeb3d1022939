import { View } from "@tarojs/components";
import { AtButton, AtFloatLayout, AtInput } from "taro-ui";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { ContactsEditModalName } from "./_utils";
import rules from "~base/utils/rules";
import { serviceURL } from "~/services/_utils";
import './index.scss';

const ContactsEditModal = () => {

    const { open, onOpenChange, form } = useActionContext(ContactsEditModalName, {
        form: {
            id: { required: false },
            name: {
                tag: '联系人姓名'
            },
            phone: {
                reg: rules.phone,
                tag: '联系人手机号'
            }
        },
        api: {
            url: serviceURL('/StopContact/save'),
            toastLoading: false,
            toastSuccess: true,
            toastError: true,
        }
    });

    // 关闭
    const handleClose = () => onOpenChange(false);

    // 输入
    const handleChange = (name, value) => {
        form?.setFieldsValue({ [name]: value });
    }

    // 确认
    const handleConfirm = async () => {
        await form.onFinish();
    }

    const isEdit = !!form?.data?.id;
    const title = `${isEdit ? '编辑' : '新增'}联系人`;

    return (
        <AtFloatLayout
            isOpened={open}
            onClose={handleClose}
            className="contacts-edit-modal"
        >
            {
                open && (
                    <View className="kb-form">
                        <View className="kb-form__title">{title}</View>
                        <View className="kb-spacing-md-lr">
                            <View className="kb-form__item">
                                <View className="item-title">姓名</View>
                                <View className="item-content">
                                    <AtInput clear value={form?.data?.name} cursorSpacing={150} maxLength={12} onChange={(v) => handleChange('name', v)} placeholder="请输入" />
                                </View>
                            </View>
                            <View className="kb-form__item--tips">
                                姓名限制1~12个字符以内，一个汉字为一个字符
                            </View>
                            <View className="kb-form__item">
                                <View className="item-title">手机号</View>
                                <View className="item-content">
                                    <AtInput clear value={form?.data?.phone} cursorSpacing={150} maxLength={11} type='mobile' onChange={(v) => handleChange('phone', v)} placeholder="请输入" />
                                </View>
                            </View>
                        </View>
                        <View className="kb-form__btn at-row at-row__align--center">
                            <View className="at-col">
                                <AtButton circle onClick={handleClose}>取消</AtButton>
                            </View>
                            <View className="at-col kb-spacing-md-l">
                                <AtButton circle type='secondary' onClick={handleConfirm} loading={form?.loading}>提交</AtButton>
                            </View>
                        </View>
                    </View>
                )
            }
        </AtFloatLayout>
    )
}

export default ContactsEditModal;