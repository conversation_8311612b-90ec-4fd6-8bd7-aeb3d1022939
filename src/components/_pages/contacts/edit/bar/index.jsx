import { View } from "@tarojs/components";
import { AtButton, AtIcon } from "taro-ui";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { ContactsEditModalName } from "../modal/_utils";

/**
 * 
 * @param {{
 *   data?:any;
 *   onSuccess?:any;
 * }} props 
 */
const ContactsEditBar = (props) => {
    const { data, onSuccess, size } = props;
    const isEdit = !!data;

    const { onOpenChange, form } = useActionContext(ContactsEditModalName, {
        onSuccess
    });

    const handleClick = () => {
        form?.setFieldsValue?.({ id: data?.id, name: data?.name, phone: data?.phone });
        onOpenChange(true)
    }

    return (
        isEdit ?
            (
                <View hoverStopPropagation hoverClass="kb-hover-opacity" onClick={handleClick} className="kb-contacts-edit-bar">
                    <AtIcon prefixClass="kb-icon" value='edit' className="kb-icon-size__base kb-color__grey-1-1" />
                </View>
            )
            : (
                <AtButton type='primary' circle size={size} onClick={handleClick}>新增联系人</AtButton>
            )
    )
}


export default ContactsEditBar;