import { Text, View } from "@tarojs/components";
import FormSelectorField from "~base/components/form/selector";
import { useMemo, useState } from "react";
import { getContactsListForSelector } from "~/services/contacts";
import { AtButton } from "taro-ui";
import ContactsEditBar from "../bar";
import { now } from "~base/utils/utils";

/**
 * 
 * @param {{
 *   value?:{value:string;label:string;};
 *   onChange?:(value?:{value:string;label:string;})=>void;
 *   label?:string;
 *   className?:string;
 *   title?:string;
 *   hoverClass?:'kb-hover'|'none'|'kb-hover-opacity';
 *   readOnly?:boolean;
 *   placeholder?:string;
 * }} props 
 * @returns 
 */
const ContactsSelector = (props) => {
    const { readOnly, hoverClass, value, onChange, placeholder = '请选择接车联系人', label = '联系人', title = `请选择${label}`, className, actionSheetProps } = props;

    const [active, setActive] = useState(true);

    // 更新列表
    const handleSuccess = () => setActive({ ts: now() });

    const actionSheetPropsMerged = useMemo(() => ({
        enableMore: true,
        align: 'start',
        title,
        closable: true,
        showSearch: true,
        className: 'depart-point-action-sheet',
        renderEmptyFooter: (
            <View className="footer-inner">
                <ContactsEditBar size='small' onSuccess={handleSuccess} />
            </View>
        ),
        renderFooter: (
            <View className="kb-spacing-md-tb kb-spacing-xxl-lr">
                <ContactsEditBar onSuccess={handleSuccess} />
            </View>
        ),
        optionRender: (text) => (
            <>
                <Text className='kb-icon kb-icon-location kb-color__brand kb-icon-size__base kb-margin-md-r' />
                <Text className='kb-sheet__button-text'>{text}</Text>
            </>
        ),
        ...actionSheetProps
    }), [actionSheetProps]);

    return (
        <FormSelectorField
            readOnly={readOnly}
            hoverClass={hoverClass}
            value={value}
            onChange={onChange}
            label={label}
            placeholder={placeholder}
            className={className}
            request={getContactsListForSelector}
            active={active}
            actionSheetProps={actionSheetPropsMerged}
        />
    )
}

export default ContactsSelector;