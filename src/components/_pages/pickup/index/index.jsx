/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import KbExternalAd from '@/components/_pages/ad-extension/ad';
import { View } from '@tarojs/components';
import Taro, { Fragment, useRef, useState, useEffect } from '@tarojs/taro';
import { AtButton } from 'taro-ui';
import KbKeyboard from '../keyboard';
import './index.scss';
import CabinetPickupCodeInput from '../input';
import KbCode from '../code';
import CabinetOpen from '../../kdg/cabinetOpen';

const Index = ({ data }) => {
  const { cabinet_info: { shelves_check = '0', device_id } = {} } = data || {};

  const isCheckShelves = !!(shelves_check == '1'); // 是否为货架包裹

  const MAX_CODE_LENGTH = isCheckShelves ? 10 : 6;

  const [visible, setVisible] = useState(true);
  const [value, setValue] = useState('');
  const [code, setCode] = useState('');
  const cabinetPickupRef = useRef({});

  const handleInput = (key) => {
    if (key == 'delete') {
      const v = value.length == 0 ? '' : value.slice(0, value.length - 1);
      setValue(v);
    } else if (value.length < MAX_CODE_LENGTH) {
      setValue((pre) => {
        return pre + key;
      });
    }
  };

  const onSearch = () => {
    setVisible(false);
    setCode(value);
    cabinetPickupRef.current.handleOpen({ pickup_code: value, device_id });
  };

  const handleCopy = () => {
    Taro.getClipboardData().then(({ data: word }) => {
      if (word) {
        // isCheckShelves的时候允许数字和-，其他时候只允许数字
        const isValid = isCheckShelves ? /^[0-9-]{1,10}$/.test(word) : /^\d{1,6}$/.test(word);

        if (isValid) {
          setValue(word);
          Taro.kbToast({
            text: '已粘贴',
          });
        } else {
          Taro.kbToast({
            text: '内容不符合取件码规则',
          });
        }
      } else {
        Taro.kbToast({
          text: '粘贴板上没有内容哦',
        });
      }
    });
  };

  useEffect(() => {
    if (isCheckShelves) return;
    if (value.length == 6) {
      setVisible(false);
      setCode(value);
      cabinetPickupRef.current.handleOpen({
        pickup_code: value,
        device_id,
      });
    }
  }, [value, isCheckShelves]);

  return (
    <Fragment>
      <View className='kb-spacing-md-lr'>
        <View className='kb-pickupCode'>
          <View className='kb-size__lg'>请输入取件码</View>
          {!isCheckShelves && (
            <View
              className='at-row at-row__justify--end kb-color__brand kb-margin-md-b'
              hoverClass='kb-hover-opacity'
              onClick={handleCopy}
            >
              粘贴
            </View>
          )}
          {isCheckShelves ? (
            <Fragment>
              <CabinetPickupCodeInput
                visible={visible}
                value={value}
                handleOpenKeyBoard={setVisible.bind(null, true)}
                handleCopy={handleCopy}
              />
              <View style={{ margin: '0 100rpx' }}>
                <AtButton type='primary' disabled={value.length <= 4} onClick={onSearch}>
                  开柜
                </AtButton>
              </View>
            </Fragment>
          ) : (
            <KbCode value={value} handleOpenKeyBoard={() => setVisible(true)} />
          )}
        </View>
      </View>
      <View className='kb-margin-md'>
        <KbExternalAd adUnitIdIndex='pickupCode' wrapper />
      </View>
      <KbKeyboard
        visible={visible}
        setVisible={(v) => setVisible(v)}
        setValue={handleInput}
        isCheckShelves={isCheckShelves}
      />
      <CabinetOpen
        device_id={device_id}
        data={{ ...data, pickupCode: code }}
        actionRef={cabinetPickupRef}
        handleOperate={() => setValue('')}
        type='input'
      />
    </Fragment>
  );
};

Index.options = {
  addGlobalClass: true,
};

export default Index;
