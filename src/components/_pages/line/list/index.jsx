import { Image, Text, View } from "@tarojs/components"
import { AtButton } from "taro-ui"
import KbLongList from '@base/components/long-list';
import { useLongList } from "~base/components/long-list/hooks";
import { serviceURL } from "~/services/_utils";
import { checkIsEndPoint, checkIsStartPoint } from "../../index/server/order/components/_utils/line";
import mapIcon from '@/assets/client/images/<EMAIL>';
import LineDetailModal from "../detail/modal";
import { useRef } from "react";
import { lineListRefreshKey } from "../_utils";
import './index.scss';
import Taro from "@tarojs/taro";
import classNames from "classnames";
import { randomCode } from "~base/utils/utils";

const LineList = (props) => {
    const { enableDispatch, onDispatch, active } = props;
    const lineDetailModalRef = useRef();
    const { list, config } = useLongList(serviceURL('/UserRoute/list'), { refresherKey: lineListRefreshKey });

    // 查看详情
    const handleClickDetail = (item) => {
        lineDetailModalRef.current.open(item);
    }

    // 操作成功
    const handleSuccess = () => config.triggerRefresher();

    // 操作
    const handleRecord = () => config.recordRefresher();

    // 添加路线
    const handleAdd = () => {
        handleRecord();
        Taro.navigator({
            url: 'line/edit'
        });
    }

    const hasList = list.length > 0;
    const wrapperCls = classNames('line-list__wrapper', {
        'line-list__wrapper-footer': true,
    });

    return (
        <>
            <View className={wrapperCls}>
                <KbLongList data={config} enableMore active={active} className="line-list">
                    <View className="kb-list">
                        {
                            list.map(item => {
                                const { stop_name: start_point_name } = item.route_item?.find(r => checkIsStartPoint(r.type)) || {};
                                const { stop_name: end_point_name } = item.route_item?.find(r => checkIsEndPoint(r.type)) || {};

                                return (
                                    <View key={item.id} className="kb-list__item--wrapper kb-box" hoverClass="kb-hover" onClick={() => handleClickDetail(item)}>
                                        <View className="kb-list__item kb-list__item-clickable">
                                            <View className="item-content">
                                                <View><Image className="line-list__item-icon" src={mapIcon} mode="widthFix" /><Text className="line-list__item-text">{item.route_name}</Text></View>
                                                <View className="kb-size__base kb-color__grey kb-spacing-sm-t">发车点：{start_point_name}</View>
                                                {end_point_name && <View className="kb-size__base kb-color__grey kb-spacing-sm-t">返程点：{end_point_name}</View>}
                                            </View>
                                        </View>
                                    </View>
                                )
                            })
                        }
                    </View>
                </KbLongList>
                <View className="line-list__footer">
                    <AtButton type='primary' circle onClick={handleAdd}>添加路线</AtButton>
                </View>
            </View>
            <LineDetailModal enableDispatch={enableDispatch} actionRef={lineDetailModalRef} onRecord={handleRecord} onSuccess={handleSuccess} onDispatch={onDispatch} />
        </>
    )
}

export default LineList;