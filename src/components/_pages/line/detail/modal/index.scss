.line-detail-modal {
    &-timeline-color {}

    &__header {
        border-bottom: $border-lightest;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $spacing-v-md $spacing-h-md;

        &--btns {
            display: flex;
            align-items: center;

            .btns-item {
                margin-left: $spacing-h-lg;

                .kb-icon,
                .at-icon {
                    font-size: $icon-font-size-sm !important;
                }

                &:first-child {
                    margin-left: 0;
                }

                &__edit,
                &__del {
                    width: 50px;
                    height: 50px;
                    border-radius: $border-radius-circle;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $color-white;
                }

                &__edit {
                    background-color: $color-brand;
                }

                &__del {
                    background-color: #E34D59;
                }

                &__close {
                    color: $color-grey-1-1;
                    margin-left: $spacing-h-xxl;
                }
            }
        }
    }

    &__content {
        padding: $spacing-v-md $spacing-h-md;

        &--scrollview {
            max-height: 320px;
        }

        &--btns {
            padding-top: $spacing-v-lg;
        }
    }

    .time-line {
        &__item-content {
            flex-grow: 1;
        }
    }

}