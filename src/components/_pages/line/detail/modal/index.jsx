import { RootPortal, <PERSON><PERSON>View, View } from "@tarojs/components"
import isArray from "lodash/isArray";
import { useImperativeHandle, useMemo, useState } from "react";
import { AtButton, AtFloatLayout, AtIcon } from "taro-ui";
import TimeLineExtend from "~base/components/time-line/extend";
import { checkIsEndPoint, checkIsStartPoint, getCurrentStopInfo } from "~/components/_pages/index/server/order/components/_utils/line";
import './index.scss';
import Taro from "@tarojs/taro";
import { deleteLine } from "~/services/line";
import longListRefresherManager from "~base/components/long-list/refresher";
import { lineListRefreshKey } from "../../_utils";
import { randomCode } from "~base/utils/utils";

const LineDetailModal = (props) => {
    const { enableDispatch = true, actionRef, onDispatch, onSuccess, onRecord } = props;
    const [title, setTitle] = useState('');
    const [isOpened, setIsOpened] = useState(false);
    const [data, setData] = useState();

    // 关闭
    const handleClose = () => {
        setIsOpened(false);
    };

    const list = useMemo(() => {
        const { route_item } = data || {}
        if (!isArray(route_item)) return [];
        return route_item.map(({ id, type, contact_name, contact_phone, stop_name }, index) => {
            const stopInfo = getCurrentStopInfo(type);
            const isStart = checkIsStartPoint(type);
            const isEnd = checkIsEndPoint(type);

            return {
                key: id,
                isCurrent: index === 0,
                isActive: index === 0,
                className: `line-detail-modal-timeline-color__${type}`,
                content: (
                    <View className='at-row at-row__align--start at-row__justify--between'>
                        <View className="kb-color__grey">
                            <View>
                                {stop_name}
                            </View>
                            {
                                !isStart && !isEnd && <View className="kb-size__base kb-spacing-sm-t">{contact_name} {contact_phone}</View>
                            }
                        </View>
                        <View className="kb-color__black">{stopInfo?.aka || stopInfo?.label}</View>
                    </View>
                )
            }
        });
    }, [data]);

    // 编辑
    const handleEdit = () => {
        onRecord?.();
        handleClose();
        Taro.navigator({
            url: 'line/edit',
            key: randomCode(),
            options: data
        });
    }

    // 删除
    const handleDelete = () => {
        Taro.kbModal({
            title: '是否删除路线？',
            cancelText: '取消',
            onConfirm: () => {
                onRecord?.();
                deleteLine({ id: data?.id }).then(res => {
                    const isSuccess = `${res.code}` === '0';
                    if (isSuccess) {
                        onSuccess?.();
                        handleClose();
                    }
                    return res;
                })
            }
        })
    }

    // 派车
    const handleDispatch = () => {
        onDispatch?.(data);
        handleClose();
    }

    useImperativeHandle(actionRef, () => ({
        open: (d) => {
            setData(d);
            setIsOpened(true);
        }
    }));

    return (
        <RootPortal>
            <AtFloatLayout isOpened={isOpened} onClose={handleClose} className="line-detail-modal">
                <View className="line-detail-modal__header">
                    <View className="line-detail-modal__header--text">{data?.route_name}</View>
                    <View className="line-detail-modal__header--btns">
                        <View className="btns-item btns-item__edit" hoverClass="kb-hover-opacity" onClick={handleEdit}>
                            <AtIcon prefixClass="kb-icon" value="edit" />
                        </View>
                        <View className="btns-item btns-item__del" hoverClass="kb-hover-opacity" onClick={handleDelete}>
                            <AtIcon prefixClass="kb-icon" value='delete' />
                        </View>
                        <View className="btns-item btns-item__close" hoverClass="kb-hover-opacity" onClick={handleClose}>
                            <AtIcon value='close' />
                        </View>
                    </View>
                </View>
                <View className="line-detail-modal__content">
                    <ScrollView className="line-detail-modal__content--scrollview" scrollY>
                        <TimeLineExtend lineColorPure labelWidthAuto centered={false} items={list} size='normal' />
                    </ScrollView>
                    {
                        enableDispatch && (
                            <View className="line-detail-modal__content--btns">
                                <AtButton type="primary" circle onClick={handleDispatch}>去派车</AtButton>
                            </View>
                        )
                    }
                </View>
            </AtFloatLayout>
        </RootPortal>
    )
}

export default LineDetailModal;