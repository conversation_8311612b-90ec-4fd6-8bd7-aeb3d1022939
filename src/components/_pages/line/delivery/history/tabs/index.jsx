import { AtTabs, AtTabsPane } from "taro-ui"
import { useCreateTabs } from "./_utils";
import { ScrollView, View } from "@tarojs/components";
import LineDeliveryHistoryList from "../list";
import pick from "lodash/pick";
import './index.scss';

const LineDeliveryHistoryTabs = (props) => {
    const { params } = props;
    const { tabs, current, onSwitch } = useCreateTabs();

    return (
        <AtTabs tabList={tabs} current={current} onClick={onSwitch}>
            {
                tabs.map((item, index) => {
                    return (
                        <AtTabsPane key={item.key} current={current} index={index}>
                            <LineDeliveryHistoryList type={item.key} active={index === current && pick(params, ['vehicle_owner_id'])} />
                        </AtTabsPane>
                    )
                })
            }
        </AtTabs>
    )
}

export default LineDeliveryHistoryTabs;