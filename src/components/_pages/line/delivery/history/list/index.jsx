import { View } from "@tarojs/components";
import { checkIsStartPoint, getCurrentStopInfo } from "~/components/_pages/index/server/order/components/_utils/line";
import { serviceURL } from "~/services/_utils";
import LongList from "~base/components/long-list"
import { useLongList } from "~base/components/long-list/hooks";
import './index.scss';
import { AtIcon } from "taro-ui";
import classNames from "classnames";
import { switchCollectLineHistory } from "~/services/line/delivery/history";
import { checkIsCollectLine } from "../../../_utils";
import Taro from "@tarojs/taro";

const LineDeliveryHistoryList = (props) => {
    const { type, active } = props;

    const { config, list } = useLongList(serviceURL('/CargoHistory/getList'), {
        api: {
            data: {
                is_collect: type
            }
        }
    });

    // 点击
    const handleClickItem = (item) => {
        Taro.navigator({
            post: {
                type: 'selectLineHistory',
                data: item
            }
        });
    }

    // 切换收藏
    const handleSwitchCollect = (item, e) => {
        e.stopPropagation();
        switchCollectLineHistory({
            id: item.id,
            is_collect: checkIsCollectLine(item) ? '0' : '1'
        }).then(res => {
            const isSuccess = `${res.code}` === '0';
            if (isSuccess) {
                config.loader();
            }
        });
    }

    return (
        <LongList
            active={active}
            data={config}
            className="line-delivery-history-list"
        >
            <View className="kb-list">
                {
                    list.map(item => (
                        <View key={item.id} className="kb-list__item--wrapper kb-box" hoverClass="kb-hover" onClick={() => handleClickItem(item)}>
                            <View className="kb-list__item">
                                <View className="item-content">
                                    <View
                                        className={
                                            classNames("list-collect-icon", {
                                                "list-collect-icon__active": checkIsCollectLine(item)
                                            })
                                        }
                                        hoverClass="kb-hover-opacity"
                                        onClick={(e) => handleSwitchCollect(item, e)}
                                        hoverStopPropagation
                                    >
                                        <AtIcon prefixClass="kb-icon" value='start' />
                                    </View>
                                    {
                                        item.dock_point_json.map(route => (
                                            <View key={route.stop_id} className="at-row list-route__item">
                                                <View className="kb-color__grey">
                                                    {getCurrentStopInfo(route.type)?.label}
                                                </View>
                                                <View className="list-route__item--info">
                                                    <View>{route.stop_name}</View>
                                                    <View className="kb-color__grey kb-size__base kb-spacing-sm-t">联系人：{route.name}/{route.phone}</View>
                                                </View>
                                            </View>
                                        ))
                                    }
                                </View>
                            </View>
                        </View>
                    ))
                }
            </View>
        </LongList>
    )
}

export default LineDeliveryHistoryList;