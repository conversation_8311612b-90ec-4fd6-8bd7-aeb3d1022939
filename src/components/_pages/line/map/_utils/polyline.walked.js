import cloneDeep from 'lodash/cloneDeep';

/**
 *
 * @description 翻转point顺序
 * @param {*} points
 * @returns
 */
export function reversePointsOrder(points) {
  const reversePoints = cloneDeep(points);
  reversePoints.reverse();
  reversePoints.forEach((item) => {
    return {
      ...item,
      points: item.points?.reverse?.(),
    };
  });
  return reversePoints;
}

function pointsIndexByContent(content, points) {
  const index = points.findIndex((item) => item.content === content);
  return index;
}

/**
 * 计算两个经纬度坐标点之间的球面距离（Haversine公式）
 * @param {Object} p1 - 第一个点，包含latitude和longitude属性
 * @param {Object} p2 - 第二个点，包含latitude和longitude属性
 * @returns {number} 距离，单位：米
 */
export function getDistance(p1, p2) {
  // 地球半径（米）
  const R = 6371000;
  // 将经纬度转换为弧度
  const φ1 = (p1.latitude * Math.PI) / 180;
  const φ2 = (p2.latitude * Math.PI) / 180;
  const Δφ = ((p2.latitude - p1.latitude) * Math.PI) / 180;
  const Δλ = ((p2.longitude - p1.longitude) * Math.PI) / 180;

  // Haversine公式
  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}

/**
 *
 * @description 获取经过的线段
 * @param {*} currentPoint
 * @param {*} pointsListParams
 * @returns
 */
export function getPassedSegments(currentPoint, pointsListParams, maxAllowedDistance = 50) {
  if (!currentPoint) return {};

  let minDistance = Infinity;
  let closest = { segIndex: -1, pointIndex: -1 };
  let pointsList = pointsListParams;

  let preIndex = pointsIndexByContent(currentPoint.pre_content, pointsList);
  let nextIndex = pointsIndexByContent(currentPoint.next_content, pointsList);
  const isBack = nextIndex >= 0 && preIndex > nextIndex;
  const enableDrawWalked = nextIndex >= 0; // 只有当下一个点位明确时，才显示路线图；
  if (isBack) {
    // 翻转
    pointsList = pointsList.slice(nextIndex, preIndex + 1);
    pointsList = reversePointsOrder(pointsList);
  } else if (nextIndex > 0) {
    pointsList = pointsList.slice(preIndex, nextIndex);
  }

  // 1. 找出离 currentPoint 最近的路径点
  for (let segIndex = 0; segIndex < pointsList.length; segIndex++) {
    const segment = pointsList[segIndex];
    for (let pointIndex = 0; pointIndex < segment.points.length; pointIndex++) {
      const point = segment.points[pointIndex];
      const distance = getDistance(currentPoint, point);
      if (distance < minDistance) {
        minDistance = distance;
        closest = { segIndex, pointIndex };
      }
    }
  }

  // 2. 判断是否在允许距离内
  if (minDistance > maxAllowedDistance) {
    return {};
  }

  // 3. 构造已走过路径段
  const result = [];

  for (let segIndex = 0; segIndex <= closest.segIndex; segIndex++) {
    const segment = pointsList[segIndex];
    const newSeg = { ...segment, points: [] };

    const endIndex = segIndex === closest.segIndex ? closest.pointIndex : segment.points.length - 1;

    for (let i = 0; i <= endIndex; i++) {
      newSeg.points.push(segment.points[i]);
    }

    if (newSeg.points.length > 0) {
      result.push(newSeg);
    }
  }

  return {
    isBack,
    enableDrawWalked,
    points: result,
  };
}
