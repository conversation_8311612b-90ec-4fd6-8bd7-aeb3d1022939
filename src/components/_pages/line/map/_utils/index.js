import Taro from '@tarojs/taro';
import debounce from 'lodash/debounce';
import isArray from 'lodash/isArray';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useEffectExtend } from '~base/hooks/state';
import { randomCode } from '~base/utils/utils';
import { createMarkers, createTaskStopMarkers } from './markers';
import { createPolyline, taskNavigationPolyLineProps, taskPolyLineProps } from './polyline';
import { createCarMarkers } from './polyline.car';
import { getPassedSegments } from './polyline.walked';

const centerDefault = {
  longitude: 116.404,
  latitude: 39.915,
};

/**
 *
 * @description 补充id
 * @param {*} points
 * @returns
 */
function patchPointsWithId(points) {
  if (!isArray(points)) return [];
  const hasMorePoints = points.some((item) => isArray(item)); // 多组points
  const pointsGroup = hasMorePoints ? points : [points];
  let start = 0;
  return pointsGroup
    .filter((item) => isArray(item))
    .map((item, index) => {
      start += pointsGroup[index - 1]?.length || 0;
      return item
        .filter((iitem) => iitem?.latitude > 0 && iitem?.longitude > 0)
        .map(
          ({ latitude, longitude, data, content, points, pre_content, next_content }, iindex) => ({
            latitude,
            longitude,
            points,
            data,
            content,
            pre_content,
            next_content,
            key: randomCode(),
            id: iindex + start,
          }),
        );
    });
}

/**
 *
 * @param {{
 *   type?:'line'|'task';
 *   points:{
 *       content?:string;
 *       data?:any;
 *       longitude:number;
 *       latitude:number;
 *   }[];
 *   onCalloutTap?:(e:any)=>void;
 * }} props
 * @returns
 */
export function useCreateMap(props) {
  const { type, points: propsPoints } = props;
  const ref = useRef({
    points: [],
    minPointsForClose: 3,
    mapCtx: null,
    mapReady: false,
    markers: [],
    clustersMap: {},
    includeLocked: false, // 缩放展示所有点，仅首次生效，避免编辑时，频繁缩放
  });

  const [mapReady, setMapReady] = useState(false);
  // 格式化点位
  const [points, morePoints] = useMemo(() => patchPointsWithId(propsPoints), [propsPoints]);

  // 标记点
  const [markers, setMarkers] = useState([]);

  // 连线
  const [polyline, setPolyline] = useState([]);

  // 地图中心
  const [center, setCenter] = useState(centerDefault);

  // 地图ID
  const lineMapID = 'lineMap';

  // 缓存 markers
  useEffect(() => {
    ref.current.markers = markers;
  }, [markers]);

  // 初始化
  useEffect(() => {
    const mapCtx = Taro.createMapContext(lineMapID);
    mapCtx.initMarkerCluster({
      enableDefaultStyle: true,
      zoomOnClick: true,
      gridSize: 60,
    });
    ref.current.mapCtx = mapCtx;
  }, [lineMapID]);

  // 缩放地图以显示所有点
  const includePointsDebounce = useCallback(
    debounce(
      (p) => {
        if (p?.length > 0 && !ref.current.includeLocked) {
          // 缩放显示所有点
          ref.current.mapCtx?.includePoints?.({
            points: p,
            padding: [50, 50, 50, 50],
          });
          ref.current.includeLocked = true;
        }
      },
      500,
      {
        trailing: true,
        leading: false,
      },
    ),
    [ref.current.mapCtx],
  );

  // 地图准备就绪
  const onInitComplete = useCallback(
    debounce(
      () => {
        if (!ref.current.mapReady) {
          ref.current.mapReady = true;
          setMapReady(true);
        }
      },
      300,
      { leading: true, trailing: false },
    ),
    [],
  );

  useEffect(() => {
    onInitComplete();
  }, []);

  // 点击地图
  const onTap = (e) => props.onTap?.(e);

  // 点击标记点
  const onMarkerTap = (e) => {
    const markerItem = ref.current.markers.find((item) => item.id === e.detail?.markerId);
    props.onMarkerOrCalloutTap?.({
      type: e.type,
      detail: markerItem,
    });
  };

  // 点击气泡
  const onCalloutTap = (e) => onMarkerTap(e);

  useEffectExtend(async () => {
    let needIncludePoints = points;
    // 设置中心点
    setCenter(points?.[0] || morePoints?.[0] || centerDefault);
    if (!mapReady) return;

    // 任务详情
    const currentPoint = morePoints?.[0];
    const {
      isBack = false,
      points: walkedPoints = [],
      enableDrawWalked = walkedPoints.length > 0,
    } = morePoints?.length > 1 // 真实的导航路径
      ? { points: morePoints }
      : getPassedSegments(currentPoint, points);

    // 车辆
    const carMarkers = await createCarMarkers(currentPoint, walkedPoints, points, isBack);

    // 停靠点
    const stopMarkers = await createMarkers(points);

    // 集合
    const allMarkers = stopMarkers.concat(carMarkers);

    // 生成markers
    setMarkers(allMarkers);

    // 生成连线
    const lines = createPolyline(points, taskPolyLineProps, false); // 停靠点线路
    const moreLines = enableDrawWalked
      ? createPolyline(walkedPoints, taskNavigationPolyLineProps, false)
      : []; // 车辆行驶线路
    const allLines = lines.concat(moreLines);
    setPolyline(allLines);

    needIncludePoints = points;

    includePointsDebounce(needIncludePoints);
  }, [points, morePoints, type, mapReady]);

  // 纯marker数据，移除其他无关信息
  const markersPure = useMemo(() => {
    const hasCustomCalloutInfo = markers.some((item) => item.customCalloutInfo);
    if (!hasCustomCalloutInfo && ref.current.mapCtx?.addMarkers) {
      // 注意此种方式可能存在兼容问题；
      if (process.env.NODE_ENV !== 'development') {
        ref.current.mapCtx?.addMarkers({
          clear: true,
          markers,
        });
        return [];
      }
    }
    return markers.map(({ data, customCalloutInfo, ...restItem }) => restItem);
  }, [markers]);

  // 自定义气泡数据
  const markersCustomCallout = useMemo(
    () =>
      markers
        .filter((item) => item.customCalloutInfo)
        .map(({ id, customCalloutInfo }) => ({ ...customCalloutInfo, id })),
    [markers],
  );

  return {
    lineMapID,
    center,
    markers: markersPure,
    markersCustomCallout,
    polyline,
    onInitComplete,
    onTap,
    onMarkerTap,
    onCalloutTap,
  };
}
