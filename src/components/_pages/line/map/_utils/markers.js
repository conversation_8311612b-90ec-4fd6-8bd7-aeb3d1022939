import isArray from 'lodash/isArray';
import arrowIcon from '~/assets/images/map/arrow.png';
import arrowActiveIcon from '~/assets/images/map/arrow_active.png';
import posBlueIcon from '~/assets/images/map/pos_blue.png';
import posBottomIcon from '~/assets/images/map/pos_bottom.png';
import posRedIcon from '~/assets/images/map/pos_red.png';
import markerIcon from '~/assets/images/map/transparent.png';
import { rpxToPx } from '~base/utils/unit';
import { getSystemInfoSync } from '~base/utils/utils';
import { createMarkersIcon, fontSizeRpx } from './markers.canvas';
import { taskPolyLineProps } from './polyline';

/**
 *
 * @description 计算文字宽度
 * @param {*} text
 * @returns
 */
export function calculateTextWidth(text = '', fontSize = rpxToPx(fontSizeRpx)) {
  let width = 0;
  const len = text.length;

  for (let i = 0; i < len; i++) {
    const code = text.charCodeAt(i);

    width +=
      (code >= 0x4e00 && code <= 0x9fff) || (code >= 0xff00 && code <= 0xffef)
        ? 2
        : code === 0x3000
        ? 2
        : 1;
  }

  return (fontSize / 2) * width;
}

/**
 *
 * @description 生成标记点
 * @param {*} points
 * @param {any} markerProps
 * @returns
 */
export async function createMarkers(points, markerProps) {
  if (!isArray(points)) return [];
  const { platform } = getSystemInfoSync();
  const fontSize = rpxToPx(fontSizeRpx);

  const hasTag = false;

  // 球左右宽度1XP + 5rpx，长度 50，右侧留白 20；
  const patchWidth = rpxToPx(6 + (hasTag ? 50 : 0) + 40) + 3; // 1 为左侧固定像素
  let {
    joinCluster = false,
    customCallout = {},
    startIndex = 0,
    ...restMarkerProps
  } = markerProps || {};

  if (joinCluster) {
    // 配置了，仍然受以下规则影响
    joinCluster = platform === 'android' && points.length > 10; // 超过10点，安卓机要聚合显示
  }

  const markers = await Promise.all(
    points.map(async (item, index) => {
      const { content, data, key, id, latitude, longitude, active } = item;
      const contentWidth = calculateTextWidth(content);
      const width = contentWidth + patchWidth;

      // marker点
      const markerItem = {
        data,
        id,
        latitude,
        longitude,
        iconPath: markerIcon,
        width: 1,
        height: 1,
        joinCluster,
        zIndex: index === 0 ? 2 : 1,
        ...restMarkerProps,
      };

      if (customCallout) {
        const {
          display = 'ALWAYS',
          anchorY = rpxToPx(5) + 1,
          anchorX = 0,
          ...customCalloutInfo
        } = customCallout;

        // 自定义气泡：地图相关配置
        markerItem.customCallout = {
          display,
          anchorY,
          anchorX,
        };

        // 自定义气泡：信息相关配置
        markerItem.customCalloutInfo = {
          key,
          active,
          content,
          arrowIcon: active ? arrowActiveIcon : arrowIcon,
          styles: !customCalloutInfo?.icon ? { width, fontSize } : {},
          ...customCalloutInfo,
        };
      }

      const markerItemPatch = await createMarkersIcon(markerItem);
      return markerItemPatch;
    }),
  );

  return markers;
}

/**
 *
 * @description 生成任务停靠点marker
 */
export async function createTaskStopMarkers(p) {
  const points = p?.map((item) => ({ ...item, active: true }));

  const markerProps = {
    iconPath: posBottomIcon,
    width: taskPolyLineProps.width,
    height: taskPolyLineProps.width,
    anchor: { x: 0.5, y: 0.5 },
  };

  // 起始停靠点
  const startStopMarkers = await createMarkers(points?.slice(0, 1), {
    ...markerProps,
    customCallout: {
      icon: posRedIcon,
      anchorY: 8,
    },
  });

  // 其他停靠点
  const otherStopMarkers = await createMarkers(points?.slice(1), {
    ...markerProps,
    customCallout: {
      icon: posBlueIcon,
      anchorY: 8,
    },
    startIndex: 1,
  });

  return startStopMarkers.concat(otherStopMarkers);
}
