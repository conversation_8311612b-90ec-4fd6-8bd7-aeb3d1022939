import carIcon from '~/assets/images/map/car.png';
import { createMarkers } from './markers';
import { taskPolyLineProps } from './polyline';
import { getDistance, reversePointsOrder } from './polyline.walked';

/**
 * 计算从 p1 指向 p2 的方位角（正北为0°，顺时针增加）
 * @param {Object} p1 - 起点，包含 latitude 和 longitude 属性
 * @param {Object} p2 - 终点，包含 latitude 和 longitude 属性
 * @returns {number} 角度，范围 [0, 360)
 */
function getRotateFromTwoPoints(p1, p2) {
  // 计算经纬度差值（注意：纬度差是 p2.lat - p1.lat，因为纬度向北增加）
  const dLon = p2.longitude - p1.longitude;
  const dLat = p2.latitude - p1.latitude;

  // 计算角度（以正北为0°，顺时针增加）
  const rad = Math.atan2(dLon, dLat); // 注意参数顺序：atan2(Δ经度, Δ纬度)
  let deg = (rad * 180) / Math.PI;

  // 确保角度在 [0, 360) 范围内
  return (deg + 360) % 360;
}

function getDirectionWithEnoughDistance(
  baseIndex,
  pts,
  direction = 'forward',
  requiredDistance = 30,
) {
  let total = 0;
  let p1 = pts[baseIndex];
  let p2 = null;

  if (direction === 'forward') {
    for (let i = baseIndex + 1; i < pts.length; i++) {
      total += getDistance(p1, pts[i]);
      if (total >= requiredDistance) {
        p2 = pts[i];
        break;
      }
    }
  } else {
    for (let i = baseIndex - 1; i >= 0; i--) {
      total += getDistance(p1, pts[i]);
      if (total >= requiredDistance) {
        p2 = pts[i];
        break;
      }
    }
  }

  if (p1 && p2) {
    return {
      rotate: getRotateFromTwoPoints(p1, p2),
      patchCurrentPoint: { ...p1 },
    };
  }

  return null;
}

/**
 *
 * @description 车辆
 * @param {*} walkedPoints
 */
export async function createCarMarkers(
  currentPoint,
  walkedPoints,
  pointsListParams,
  isBack = false,
) {
  if (!currentPoint) return [];

  let rotate = 0;
  let patchCurrentPoint = null;
  let pointsList = pointsListParams;

  // 往回走
  if (isBack) {
    pointsList = reversePointsOrder(pointsList);
  }

  if (walkedPoints.length > 0) {
    const lastSeg = walkedPoints[walkedPoints.length - 1];
    const lastPoint = lastSeg.points[lastSeg.points.length - 1];

    for (let segIndex = 0; segIndex < pointsList.length; segIndex++) {
      const seg = pointsList[segIndex];
      const pts = seg.points;

      for (let i = 0; i < pts.length; i++) {
        const pt = pts[i];
        const isSame = pt.latitude === lastPoint.latitude && pt.longitude === lastPoint.longitude;

        if (isSame) {
          // 尝试向前找一段足够距离
          const forward = getDirectionWithEnoughDistance(i, pts, 'forward');
          if (forward) {
            rotate = forward.rotate;
            patchCurrentPoint = forward.patchCurrentPoint;
            break;
          }

          // 否则尝试往后找
          const backward = getDirectionWithEnoughDistance(i, pts, 'backward');
          if (backward) {
            rotate = backward.rotate;
            patchCurrentPoint = backward.patchCurrentPoint;
            break;
          }
        }
      }
    }
  }

  const carMarkers = await createMarkers(
    [
      {
        ...currentPoint,
        ...patchCurrentPoint,
        id: Date.now(),
      },
    ],
    {
      iconPath: carIcon,
      width: taskPolyLineProps.width,
      height: (taskPolyLineProps.width / 24) * 52,
      rotate,
      zIndex: 3,
      anchor: {
        x: 0.5,
        y: 0.5,
      },
      customCallout: null,
    },
  );

  return carMarkers;
}
