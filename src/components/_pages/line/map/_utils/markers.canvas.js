import Taro from '@tarojs/taro';
import { getCurrentStopInfo } from '~/components/_pages/index/server/order/components/_utils/line';
import { rpxToPx } from '~base/utils/unit';
import { getSystemInfoSync } from '~base/utils/utils';

export const fontSizeRpx = 28;

// 水滴 marker（圆润 + 黑色圆环底座）
function createMarkerWaterDropIcon(text = '', color = '#f60') {
  return new Promise((resolve, reject) => {
    const { pixelRatio } = getSystemInfoSync();
    const dpr = pixelRatio || 1;

    const radius = rpxToPx(25); // 圆头半径
    const arrowHeight = rpxToPx(10); // 底部高度
    const padding = rpxToPx(0);
    const fontSize = rpxToPx(fontSizeRpx); // 字体大小
    const bottomRadius = rpxToPx(12); // 黑色圆环半径
    const bottomLineWidth = rpxToPx(3); // 底座线宽度

    const width = (radius * 2 + padding * 2) * dpr;
    const height =
      (radius * 2 + arrowHeight + padding * 2 + bottomRadius + bottomLineWidth * 2) * dpr;

    const canvas = Taro.createOffscreenCanvas({ type: '2d', width, height });
    const ctx = canvas.getContext('2d');
    ctx.scale(dpr, dpr);
    ctx.clearRect(0, 0, width, height);

    const centerX = width / dpr / 2;
    const circleCenterY = padding + radius;

    // === 底部黑边圆环 ===
    const ringCenterY = circleCenterY + radius + arrowHeight;

    // 黑边 + 白底
    ctx.beginPath();
    ctx.arc(centerX, ringCenterY, bottomRadius, 0, Math.PI * 2);
    ctx.fillStyle = '#fff';
    ctx.fill();
    ctx.lineWidth = bottomLineWidth;
    ctx.strokeStyle = '#000';
    ctx.stroke();

    // 中心黑点
    ctx.beginPath();
    ctx.arc(centerX, ringCenterY, bottomRadius / 2.5, 0, Math.PI * 2);
    ctx.fillStyle = '#000';
    ctx.fill();

    // === 白色描边层 ===
    ctx.beginPath();
    ctx.arc(centerX, circleCenterY, radius, Math.PI, 0, false); // 上半圆
    ctx.quadraticCurveTo(
      centerX + radius,
      circleCenterY + radius,
      centerX,
      circleCenterY + radius + arrowHeight,
    );
    ctx.quadraticCurveTo(centerX - radius, circleCenterY + radius, centerX - radius, circleCenterY);
    ctx.closePath();
    ctx.fillStyle = '#fff';
    ctx.fill();

    // === 彩色内部水滴 ===
    const border = 1;
    ctx.beginPath();
    ctx.arc(centerX, circleCenterY, radius - border, Math.PI, 0, false);
    ctx.quadraticCurveTo(
      centerX + (radius - border),
      circleCenterY + (radius - border),
      centerX,
      circleCenterY + (radius - border) + arrowHeight,
    );
    ctx.quadraticCurveTo(
      centerX - (radius - border),
      circleCenterY + (radius - border),
      centerX - (radius - border),
      circleCenterY,
    );
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // === 中心文字 ===
    ctx.font = `${fontSize}px sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillStyle = '#fff';
    ctx.fillText(text, centerX, circleCenterY);

    // === 输出 ===
    Taro.canvasToTempFilePath({
      canvas,
      x: 0,
      y: 0,
      width,
      height,
      destWidth: canvas.width,
      destHeight: canvas.height,
      success(res) {
        resolve({
          iconPath: res.tempFilePath,
          width: width / dpr,
          height: height / dpr,
          anchor: { x: 0.5, y: 1 },
        });
      },
      fail(err) {
        reject(err);
      },
    });
  });
}

// label式marker
function createMarkerLabelIcon(text = '', index = '', color = '#f60') {
  return new Promise((resolve, reject) => {
    const { pixelRatio } = getSystemInfoSync();
    const dpr = pixelRatio || 1;

    const circleRadius = rpxToPx(30);
    const arrowHeight = rpxToPx(10);
    const fontSize = rpxToPx(fontSizeRpx);

    const padding = rpxToPx(12);
    const gap = rpxToPx(10);
    const rightSpace = rpxToPx(30);

    const canvas = Taro.createOffscreenCanvas({ type: '2d', width: 1, height: 1 });
    const ctx = canvas.getContext('2d');

    ctx.font = `${fontSize}px sans-serif`;
    const textWidth = ctx.measureText(text).width;

    const hasIndex = !!index;
    const leftOffset = hasIndex ? circleRadius * 2 + gap : rightSpace;
    const contentWidth = leftOffset + textWidth + rightSpace;
    const contentHeight = circleRadius * 2;
    const totalWidth = (contentWidth + padding * 2) * dpr;
    const totalHeight = (contentHeight + arrowHeight + padding) * dpr;

    canvas.width = totalWidth;
    canvas.height = totalHeight;
    ctx.scale(dpr, dpr);
    ctx.clearRect(0, 0, totalWidth, totalHeight);

    const x = padding;
    const y = padding;
    const bgRadius = circleRadius;
    const centerX = x + circleRadius;
    const centerY = y + circleRadius;

    // 背景边框（白色）
    ctx.beginPath();
    ctx.moveTo(x + bgRadius, y);
    ctx.lineTo(x + contentWidth - bgRadius, y);
    ctx.quadraticCurveTo(x + contentWidth, y, x + contentWidth, y + bgRadius);
    ctx.lineTo(x + contentWidth, y + contentHeight - bgRadius);
    ctx.quadraticCurveTo(
      x + contentWidth,
      y + contentHeight,
      x + contentWidth - bgRadius,
      y + contentHeight,
    );
    ctx.lineTo(x + contentWidth / 2 + 8, y + contentHeight);
    ctx.lineTo(x + contentWidth / 2, y + contentHeight + arrowHeight);
    ctx.lineTo(x + contentWidth / 2 - 8, y + contentHeight);
    ctx.lineTo(x + bgRadius, y + contentHeight);
    ctx.quadraticCurveTo(x, y + contentHeight, x, y + contentHeight - bgRadius);
    ctx.lineTo(x, y + bgRadius);
    ctx.quadraticCurveTo(x, y, x + bgRadius, y);
    ctx.closePath();
    ctx.fillStyle = '#fff';
    ctx.fill();

    // 背景主体（橙色）
    const inset = 1;
    ctx.beginPath();
    ctx.moveTo(x + bgRadius + inset, y + inset);
    ctx.lineTo(x + contentWidth - bgRadius - inset, y + inset);
    ctx.quadraticCurveTo(
      x + contentWidth - inset,
      y + inset,
      x + contentWidth - inset,
      y + bgRadius + inset,
    );
    ctx.lineTo(x + contentWidth - inset, y + contentHeight - bgRadius - inset);
    ctx.quadraticCurveTo(
      x + contentWidth - inset,
      y + contentHeight - inset,
      x + contentWidth - bgRadius - inset,
      y + contentHeight - inset,
    );
    ctx.lineTo(x + contentWidth / 2 + 8 - inset, y + contentHeight - inset);
    ctx.lineTo(x + contentWidth / 2, y + contentHeight + arrowHeight - inset);
    ctx.lineTo(x + contentWidth / 2 - 8 + inset, y + contentHeight - inset);
    ctx.lineTo(x + bgRadius + inset, y + contentHeight - inset);
    ctx.quadraticCurveTo(
      x + inset,
      y + contentHeight - inset,
      x + inset,
      y + contentHeight - bgRadius - inset,
    );
    ctx.lineTo(x + inset, y + bgRadius + inset);
    ctx.quadraticCurveTo(x + inset, y + inset, x + bgRadius + inset, y + inset);
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();

    // 左侧编号圆圈（可选）
    if (hasIndex) {
      ctx.beginPath();
      ctx.arc(centerX, centerY, circleRadius - 1, 0, Math.PI * 2);
      ctx.fillStyle = '#fff';
      ctx.fill();
      ctx.lineWidth = 2;
      ctx.strokeStyle = color;
      ctx.stroke();

      ctx.fillStyle = color;
      ctx.font = `${fontSize}px sans-serif`;
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(index.toString(), centerX, centerY);
    }

    // 右侧文字
    ctx.fillStyle = '#fff';
    ctx.font = `${fontSize}px sans-serif`;
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, x + leftOffset, centerY);

    Taro.canvasToTempFilePath({
      canvas,
      x: 0,
      y: 0,
      width: totalWidth,
      height: totalHeight,
      destWidth: canvas.width,
      destHeight: canvas.height,
      success(res) {
        resolve({
          iconPath: res.tempFilePath,
          width: totalWidth / dpr,
          height: totalHeight / dpr,
        });
      },
      fail(err) {
        reject(err);
      },
    });
  });
}

// label式marker （气泡+竖线+底座）
function createMarkerWithBubble(text = '', color = '#f60') {
  return new Promise((resolve, reject) => {
    const bgColor = '#fff';
    const fontColor = '#000';
    const { pixelRatio } = Taro.getSystemInfoSync();
    const dpr = pixelRatio || 1;

    const fontSize = rpxToPx(28);
    const paddingX = rpxToPx(20);
    const paddingY = rpxToPx(17); // 上下多5像素
    const radius = rpxToPx(16);
    const lineHeight = rpxToPx(24);
    const outerDotRadius = rpxToPx(14); // 白圆半径
    const innerDotRadius = outerDotRadius / 2; // 彩色圆半径
    const spacing = rpxToPx(8);

    const canvas = Taro.createOffscreenCanvas({ type: '2d', width: 1, height: 1 });
    const ctx = canvas.getContext('2d');
    ctx.font = `${fontSize}px sans-serif`;

    const maxTextWidth = rpxToPx(200);
    let displayText = text;
    let textWidth = ctx.measureText(displayText).width;
    if (textWidth > maxTextWidth) {
      while (ctx.measureText(displayText + '...').width > maxTextWidth && displayText.length > 0) {
        displayText = displayText.slice(0, -1);
      }
      displayText += '...';
      textWidth = ctx.measureText(displayText).width;
    }

    const boxWidth = textWidth + paddingX * 2;
    const boxHeight = fontSize + paddingY * 2;
    const totalWidth = boxWidth * dpr;
    const totalHeight = (boxHeight + spacing + lineHeight + outerDotRadius) * dpr;

    canvas.width = totalWidth;
    canvas.height = totalHeight;
    ctx.scale(dpr, dpr);
    ctx.clearRect(0, 0, totalWidth, totalHeight);

    const centerX = boxWidth / 2;
    const innerCircleCenterY = boxHeight + spacing + lineHeight;

    // 1. 气泡框
    ctx.beginPath();
    ctx.moveTo(radius, 0);
    ctx.lineTo(boxWidth - radius, 0);
    ctx.quadraticCurveTo(boxWidth, 0, boxWidth, radius);
    ctx.lineTo(boxWidth, boxHeight - radius);
    ctx.quadraticCurveTo(boxWidth, boxHeight, boxWidth - radius, boxHeight);
    ctx.lineTo(radius, boxHeight);
    ctx.quadraticCurveTo(0, boxHeight, 0, boxHeight - radius);
    ctx.lineTo(0, radius);
    ctx.quadraticCurveTo(0, 0, radius, 0);
    ctx.closePath();
    ctx.fillStyle = bgColor;
    ctx.fill();

    // 2. 文本
    ctx.fillStyle = fontColor;
    ctx.font = `${fontSize}px sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(displayText, centerX, boxHeight / 2);

    // 3. 白色圆
    ctx.beginPath();
    ctx.arc(centerX, innerCircleCenterY, outerDotRadius, 0, Math.PI * 2);
    ctx.fillStyle = '#fff';
    ctx.fill();

    // 4. 彩色圆
    ctx.beginPath();
    ctx.arc(centerX, innerCircleCenterY, innerDotRadius, 0, Math.PI * 2);
    ctx.fillStyle = color;
    ctx.fill();

    // 5. 竖线（最后绘制 → 保证在圆上方）
    ctx.beginPath();
    ctx.strokeStyle = color;
    ctx.lineWidth = rpxToPx(4);
    ctx.moveTo(centerX, boxHeight);
    ctx.lineTo(centerX, innerCircleCenterY);
    ctx.stroke();

    // 6. 输出文件
    Taro.canvasToTempFilePath({
      canvas,
      x: 0,
      y: 0,
      width: totalWidth,
      height: totalHeight,
      destWidth: canvas.width,
      destHeight: canvas.height,
      success(res) {
        resolve({
          iconPath: res.tempFilePath,
          width: totalWidth / dpr,
          height: totalHeight / dpr,
        });
      },
      fail(err) {
        reject(err);
      },
    });
  });
}

/**
 *
 * @description 绘制marker图片：有customCallout 时，安卓手机绘制效果不佳；需要兼容为纯marker图片
 * @param {*} marker
 */
export async function createMarkersIcon(marker) {
  // 行程停靠点类型，1:起点，2:停靠点，3:终点
  const stopColorsMap = {};
  const { customCallout, customCalloutInfo, ...restMarker } = marker || {};
  if (customCallout && customCalloutInfo) {
    const { icon, tag } = customCalloutInfo;
    try {
      const iconInfo = icon
        ? await createMarkerWaterDropIcon(tag, tag >= 1 ? '#03a9fd' : '#f64a49')
        : await createMarkerWithBubble(
            customCalloutInfo.content,
            getCurrentStopInfo(restMarker.data?.stop_type || '2')?.color,
          );
      return {
        ...restMarker,
        ...iconInfo,
        joinCluster: false,
      };
    } catch (error) {
      console.log('createMarkersIcon-error', error);
    }
  }
  return marker;
}
