import isArray from 'lodash/isArray';

// 任务详情，路径规划连线样式
export const taskPolyLineProps = {
  color: '#0FC7CD',
  width: 15,
  borderWidth: 1,
  borderColor: '#0FC7CD',
};

// 任务详情 - 车辆导航路径样式
export const taskNavigationPolyLineProps = {
  ...taskPolyLineProps,
  color: '#5a84ff',
  borderColor: '#415db2',
};

/**
 *
 * @description 生成连线
 * @param {*} points
 * @param {{dottedLine?:boolean;width?:boolean;color?:string;arrowLine?:boolean;borderWidth?:number;borderColor?:string;colorList?:string[];arrowIconPath?:string;level?:'abovelabels'|'abovebuildings'|'aboveroads';textStyle?:{textColor?:string;strokeColor?:string;fontSize?:string;segmentTexts?:{name?:string;startIndex?:number;endIndex?:number;}}}} lineProps //  连线属性
 * @param {boolean} isClose // 是否闭环
 */
export function createPolyline(points, lineProps, isClose = true) {
  if (!isArray(points)) return [];
  let linePoints = [];

  // 每个点里面包含多组点，拉平点数据
  points.forEach((item) => {
    const { points: itemPoints, latitude, longitude } = item;
    const patchPoints = isArray(itemPoints)
      ? itemPoints.map((point) => ({
          latitude: point.latitude,
          longitude: point.longitude,
        }))
      : [];
    patchPoints.length > 0
      ? linePoints.push(...patchPoints)
      : linePoints.push({ latitude, longitude });
  });

  // 满足闭合条件时：点数≥3且开启自动闭合
  if (points.length >= 3 && isClose) {
    linePoints = [...linePoints, linePoints[0]]; // 追加第一个点形成闭环
  }

  const polyline = [
    {
      color: '#FF7300',
      width: 8,
      arrowLine: true,
      borderWidth: 0,
      ...lineProps,
      points: linePoints,
    },
  ];

  return polyline;
}
