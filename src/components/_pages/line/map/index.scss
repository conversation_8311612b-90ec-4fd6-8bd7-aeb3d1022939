$line-callout-color: $color-brand;
$line-callout-color-active: #FF7300;
$line-callout-size: 50px;
$line-callout-arrow-size: 10px;

.line-map {

    &,
    &__wrapper {
        width: 100%;
        height: 100%;
    }

    &__callout {

        position: relative;
        padding-bottom: $line-callout-arrow-size;

        &-circle {
            color: $line-callout-color;
            background-color: $color-white;
            width: $line-callout-size;
            height: $line-callout-size;
            text-align: center;
            line-height: $line-callout-size;
            border-radius: $border-radius-circle;
            flex-shrink: 0;
            z-index: 1;
            position: relative;
            &-placeholder{
                width: 20px;
            }
        }

        &-content {
            width: 100%;
            flex-grow: 1;
            line-height: $line-callout-size;
            font-size: inherit;
            position: relative;
            z-index: 1;
            margin-left: $spacing-h-xs;
        }

        &-bg {
            background-color: $line-callout-color;
            padding: $width-base;
            border: $width-base solid $color-white;
            color: $color-white;
            border-radius: $line-callout-size;
            position: relative;
            display: flex;
            align-items: center;
        }

        &-arrow {
            position: absolute;
            width: 16px;
            height: $line-callout-arrow-size;
            left: 50%;
            bottom: 5px;
            transform: translateX(-50%);
            transform-origin: center;
            z-index: 0;
        }

        &-active &-bg{
            background-color: $line-callout-color-active;
        }

        &-active &-circle {
            color: $line-callout-color-active;
        }

        &-simple {
            text-align: center;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;

            &,
            &-wrapper {
                width: 50px;
                height: 55px;
            }

            .simple {
                &-icon {
                    width: 100%;
                    height: 100%;
                    position: relative;
                    z-index: 0;
                }

                &-tag {
                    color: $color-white;
                    text-align: center;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    transform: translate(-50%, -50%);
                    z-index: 1;
                    margin-top: -5px;
                }
            }
        }
    }

}