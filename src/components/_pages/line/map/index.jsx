import { Map, View, CoverView, CoverImage } from "@tarojs/components";
import { useCreateMap } from "./_utils";
import { useEffect } from "react";
import classNames from "classnames";
import posRedIcon from '~/assets/images/map/pos_red.png';
import './index.scss';

/**
 * 
 * @description 
 * line - 线路模式：线路详情
 * task - 任务模式：任务详情
 * @param {{
 *   type?:'line'|'task';
 *   points:{ 
 *       content?:string;
 *       active?:boolean;
 *       data?:any;
 *       longitude:number;
 *       latitude:number;
 *   }[];
 *   onCalloutTap?:(e:any)=>void;
 * }} props 
 * @returns 
 */
const LineMap = (props) => {
    const { lineMapID, center, markers, markersCustomCallout, polyline, onTap, onCalloutTap, onMarkerTap, onInitComplete } = useCreateMap(props);

    return (
        <View className="line-map__wrapper">
            <Map className="line-map"
                id={lineMapID}
                longitude={center?.longitude}
                latitude={center?.latitude}
                markers={markers}
                polyline={polyline}
                scale="16"
                showLocation={false}
                onCalloutTap={onCalloutTap}
                onMarkerTap={onMarkerTap}
                onTap={onTap}
                onUpdated={onInitComplete}
            >
                {
                    markersCustomCallout.length > 0 && (
                        <CoverView slot="callout">
                            {
                                markersCustomCallout.map((item, index) => (
                                    item.icon
                                        ? (
                                            <CoverView
                                                key={item.key}
                                                markerId={item.id}
                                                className='line-map__callout-simple'
                                                style={item.styles}
                                            >
                                                <CoverImage className="simple-icon" src={item.icon} />
                                                <CoverView className="simple-tag">
                                                    {item.tag}
                                                </CoverView>
                                            </CoverView>
                                        )
                                        : (
                                            <CoverView
                                                key={item.key}
                                                markerId={item.id}
                                                className={classNames('line-map__callout', {
                                                    ["line-map__callout-active"]: item.active,
                                                })}
                                                style={item.styles}
                                            >
                                                <CoverView className="line-map__callout-bg">
                                                    {
                                                        item.tag ? (
                                                            <CoverView className="line-map__callout-circle">
                                                                {item.tag}
                                                            </CoverView>
                                                        ) : (
                                                            <CoverView className="line-map__callout-circle-placeholder"></CoverView>
                                                        )
                                                    }
                                                    {
                                                        item.content && (
                                                            <CoverView className="line-map__callout-content">
                                                                {item.content}
                                                            </CoverView>
                                                        )
                                                    }
                                                </CoverView>
                                                <CoverImage src={item.arrowIcon} className="line-map__callout-arrow" />
                                            </CoverView>
                                        )
                                ))
                            }
                        </CoverView>
                    )
                }
            </Map>
        </View>
    )

}

export default LineMap;