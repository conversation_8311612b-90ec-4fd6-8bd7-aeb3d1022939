import { View, Text } from "@tarojs/components";
import classNames from "classnames";
import { useEffect, useRef } from "react";
import ContactsSelector from "~/components/_pages/contacts/edit/selector";
import OperateDepartSelector from "~/components/_pages/order/detail/operate/depart/modal/form/selector";

/**
 * 
 * @param {{
*   label:string;
*   value?:{point:{value:string;label:string};contact:{value:string;label:string}};
*   onChange?:(value:{point:{value:string;label:string};contact:{value:string;label:string}})=>void;
*   required?:boolean;
*   params?:any
*   className?:string
*   action?:'normal'|'line-edit';
* }} props 
* @returns 
*/
const PointAndContactSelector = (props) => {
    const ref = useRef({ value: {} });
    const { action, label, required, value, onChange, params, className } = props;
    const cls = classNames(className, {
        ['kb-form__item--required']: !!required
    });

    useEffect(() => {
        ref.current.value = value;
    }, [value]);

    const handleChange = (type, val) => {
        ref.current.value = {
            ...ref.current.value,
            [type]: val
        };
        onChange?.(ref.current.value);
    }

    return (
        <View className="kb-form">
            <OperateDepartSelector action={action} params={params} label={label} value={value?.point} className={cls} onChange={(val) => handleChange('point', val)} />
            <ContactsSelector value={value?.contact} className={cls} onChange={(val) => handleChange('contact', val)} />
        </View>
    )

}

export default PointAndContactSelector;