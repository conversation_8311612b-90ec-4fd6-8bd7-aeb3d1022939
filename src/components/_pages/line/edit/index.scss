.line-edit-form {
    &__tools {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: $spacing-v-md $spacing-h-md;

        &--btn {
            background-color: $color-white;
            font-size: $font-size-base;
            padding: $spacing-v-sm $spacing-h-lg;
            border-radius: $border-radius-arc;
        }
    }

    &__footer-tools {
        font-size: $font-size-base;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-bottom: $spacing-v-xxl;
        padding-right: $spacing-h-md;

        &-bar {
            color: #576B95;
        }
    }

    &__stop-item {
        .item-title {
            padding-left: 12px;
        }
    }

    .kb-from__group {
        padding: 0 $spacing-h-md $spacing-v-md $spacing-h-md;

        .kb-form {
            margin-bottom: $spacing-v-md;
            border-radius: $border-radius-xxl;
            overflow: hidden;

            .item {

                &-content,
                &-placeholder {
                    text-align: right;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}