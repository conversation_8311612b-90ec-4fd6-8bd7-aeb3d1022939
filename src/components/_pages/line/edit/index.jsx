import { Text, View } from "@tarojs/components"
import React, { useEffect, useImperativeHandle, useMemo } from "react"
import { AtButton, AtIcon, AtInput } from "taro-ui"
import FormSelectorField from "~base/components/form/selector";
import { useForm } from "~base/hooks/form";
import PointAndContactSelector from "./pointAndContactSelector";
import StopPointSelector from "./stopPointSelector";
import Taro from "@tarojs/taro";
import { useCheckLineSamePoints } from "./_utils/check";
import './index.scss';

const validatorPointAndContact = (val, label) => {
    if (!val?.point) {
        return `请选择${label}`
    }

    if (!val?.contact) {
        return `请选择${label}联系人`
    }

    return null;
}

/**
 * 
 * @param {{
*   toolsRender?:React.ReactNode;
*   showName?:boolean; // 是否显示名称：路线编辑时显示
*   clearable?:boolean; // 是否可清空
*   enableEditName?: boolean; // 是否可编辑名称
*   request?: ()=>Promise<any>; // 请求
*   enableStartAsEnd?: boolean;
*   params?: any;
*   onChange?:(formData:any)=>void;
*   action?:'normal'|'line-edit';
*   enableMore?:boolean;
*   labels?:{start:string;stop:string;end:string;};
*   actionRef?: React.MutableRefObject<{submit:(data:any)=>Promise(boolean);clean:()=>void;update:(data:any)=>void;}>
* }} props 
* @returns 
*/
const LineEdit = (props) => {
    const { labels, enableMore, action, params, enableStartAsEnd = false, defaultValue, enableEditName = false, showName, clearable = true, actionRef, toolsRender, request, onChange } = props;
    const { start: startLabel = '装货点', end: endLabel = '卸货点' } = labels || {}
    const formConfig = useMemo(() => {
        const { start: startDefault, stop: stopDefault, end: endDefault, id: idDefault, name: nameDefault } = defaultValue || {}
        const form = {
            start: { value: startDefault, validator: validatorPointAndContact },
            stop: { value: stopDefault, required: false },
            end: { value: endDefault, validator: validatorPointAndContact }
        }
        if (enableEditName) {
            form.name = {
                value: nameDefault
            }
        }
        if (idDefault) {
            // 编辑
            form.id = { value: idDefault }
        }
        return {
            request,
            form
        }
    }, [request, clearable, defaultValue]);
    const { data: { data: formData, disabled, loading }, setFieldsValue, cleanFieldsValue, onFinish } = useForm(formConfig);

    // 输入或选择变更
    const handleChange = (name, value) => {
        setFieldsValue({
            [name]: value
        });
    }

    // 清空
    const handleClear = () => cleanFieldsValue();

    // 使用发车点作为返程点
    const handleUserStartPoint = () => {
        const { start } = formData;
        if (!start) {
            Taro.kbToast({
                text: '请先选择发车点'
            });
            return;
        }
        setFieldsValue({
            end: start
        });
    }

    // 检查线路点位是否一致
    useCheckLineSamePoints(formData);

    useEffect(() => {
        onChange?.({
            data: formData,
            disabled,
            loading
        });
    }, [formData, disabled, loading])

    // 绑定action
    useImperativeHandle(actionRef, () => ({
        update: (d) => setFieldsValue(d),
        clean: () => cleanFieldsValue(),
        submit: (d) => onFinish(d),
        getFieldsValue: () => formData,
    }));

    return (
        <View className="line-edit-form">
            <View className="line-edit-form__tools">
                <View className="line-edit-form__tools--extra">{toolsRender}</View>
                {clearable && (
                    <View className="line-edit-form__tools--btn" hoverClass="kb-hover" onClick={handleClear}>
                        <AtIcon prefixClass="kb-icon" className="kb-color__red kb-icon-size__base" value='clear' />
                        <Text className="kb-icon__text--ml">清空</Text>
                    </View>
                )}
            </View>
            <View className="kb-from__group">
                {
                    enableEditName && (
                        <View className="kb-form">
                            <View className="kb-form__item kb-form__item--required">
                                <View className="item-title">路线名称</View>
                                <View className="item-content">
                                    <AtInput placeholder="请输入路线名称" clear value={formData?.name} onChange={(val) => handleChange('name', val)} />
                                </View>
                            </View>
                        </View>
                    )
                }

                <PointAndContactSelector enableMore={enableMore} action={action} params={params} label={startLabel} value={formData?.start} required onChange={(val) => handleChange('start', val)} />
                <StopPointSelector enableMore={enableMore} action={action} className='line-edit-form__stop-item' params={params} label='停靠点' value={formData?.stop} onChange={(val) => handleChange('stop', val)}>
                    <PointAndContactSelector enableMore={enableMore} action={action} params={params} label={endLabel} value={formData?.end} required onChange={(val) => handleChange('end', val)} />
                </StopPointSelector>
                {
                    enableStartAsEnd && (
                        <View className="line-edit-form__footer-tools">
                            <View className="line-edit-form__footer-tools-bar" hoverClass="kb-hover-opacity" onClick={handleUserStartPoint}>
                                使用发车点作为返程点
                            </View>
                        </View>
                    )
                }
            </View>
        </View>
    )
}

export default LineEdit;