import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import uniqBy from 'lodash/uniqBy';
import { useMemo } from 'react';

/**
 *
 * @description 点位检查：
 * 若所选发车点和停靠点或者停靠点之间相同，则toast提示“发车点和停靠点相同，请检查”或者“存在相同的停靠点”，只是提示，不做发车限制，相同也可以发车；
 */
export function useCheckLineSamePoints(formData) {
  const { start, stop } = formData;

  const hasSame = useMemo(() => {
    const { value: startPoint } = start?.point || {};

    const stopFiltered = isArray(stop) ? stop.filter((item) => !!item?.point?.value) : [];

    if (stopFiltered.length) {
      if (startPoint) {
        // 发车点
        if (stopFiltered.some((item) => item?.point?.value === startPoint)) {
          Taro.kbToast({
            text: '装货点和停靠点相同，请检查',
          });
          return true;
        }
      }
      const uniqueList = uniqBy(stopFiltered, (item) => item.point?.value);
      if (uniqueList.length !== stopFiltered.length) {
        Taro.kbToast({
          text: '存在相同的停靠点',
        });
        return true;
      }
    }

    return false;
  }, [start, stop]);

  return {
    hasSame,
  };
}
