import isArray from 'lodash/isArray';
import {
  checkIsStartPoint,
  checkIsStopPoint,
} from '~/components/_pages/index/server/order/components/_utils/line';
import { randomCode } from '~base/utils/utils';

/**
 * @param {{point:{value:string;label:string};contact:{name:string;phone:string}}[]} values
 * @description 空值补充默认项；没有key的补充key
 */
export function patchStopsValue(values) {
  if (!isArray(values)) {
    return [
      {
        key: randomCode(),
        point: {
          value: '',
          label: '',
        },
        contact: {
          value: '',
          label: '',
        },
      },
    ];
  }
  return values.map((item) => ({
    key: randomCode(),
    ...item,
  }));
}

/**
 *
 * @returns
 */
export function reformatLineEditForm(formData) {
  const { start, stop, end } = formData || {};
  const routes = [
    {
      ...start,
      stop_type: '1',
    },
    ...(isArray(stop)
      ? stop.map((item) => ({
          ...item,
          stop_type: '2',
        }))
      : []),
    {
      ...end,
      stop_type: '3',
    },
  ]
    .filter((item) => item?.point?.value)
    .map(({ point: { info: pointInfo } = {}, contact: { info: contactInfo } = {}, ...rest }) => ({
      ...rest,
      company_name: pointInfo?.vehicle_owner_info?.company_name,
      vehicle_owner_id: pointInfo?.vehicle_owner_id,
      stop_id: pointInfo?.stop_id,
      stop_name: pointInfo?.stop_name,
      contact_name: contactInfo?.name,
      contact_phone: contactInfo?.phone,
      contact_id: contactInfo?.id,
    }));

  return routes;
}

// 格式化表单数据，
export function formatLineEditForm(routes) {
  const formData = {
    start: {}, // 发车点
    stop: [], // 停靠点
    end: {}, // 返程点
  };

  const patchPointAndContact = (item) => {
    const {
      stop_id,
      stop_name,
      // /VehicleTask/getLastRoute、/CargoHistory/getList 此接口返回的是 name和phone
      name,
      phone,
      contact_id,
      contact_name = name,
      contact_phone = phone,
    } = item;
    return {
      point: {
        value: stop_id,
        label: stop_name,
        info: item,
      },
      contact: {
        value: contact_id,
        label: [contact_name, contact_phone].filter((item) => !!item).join('/'),
        info: {
          ...item,
          id: contact_id,
          name: contact_name,
          phone: contact_phone,
        },
      },
    };
  };

  if (isArray(routes) && routes.length > 0) {
    routes.forEach((item) => {
      const { type } = item;
      const formItem = patchPointAndContact(item);
      if (checkIsStopPoint(type)) {
        formData.stop.push(formItem);
      } else {
        formData[checkIsStartPoint(type) ? 'start' : 'end'] = formItem;
      }
    });
    return formData;
  }
}
