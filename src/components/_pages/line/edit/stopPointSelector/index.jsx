import { View, Text } from "@tarojs/components";
import classNames from "classnames";
import { useEffect, useMemo, useRef } from "react";
import { AtIcon } from "taro-ui";
import { patchStopsValue } from "../_utils";
import PointAndContactSelector from "../pointAndContactSelector";
import { randomCode } from "~base/utils/utils";
import './index.scss';

/**
 * 
 * @param {{
 *   label:string;
 *   value?:{point:{value:string;label:string};contact:{value:string;label:string}}[];
 *   onChange?:(value:{point:{value:string;label:string};contact:{value:string;label:string}}[])=>void;
 *   required?:boolean;
 *   params?:any
 *   className?:string;
 * }} props 
 * @returns 
 */
const StopPointSelector = (props) => {
    const { action } = props;
    const isLineEdit = action === 'line-edit';
    const ref = useRef({ value: [] });
    const min = isLineEdit ? 1 : 0;
    const max = 5;
    const { name, label, required, value, onChange, children, params, className } = props;

    // 联系方式与点位选择：没有传入val标识移除
    const handleChange = (key, val) => {
        const { value: currentValue } = ref.current;
        const index = currentValue.findIndex(item => item.key === key);
        if (index >= 0) {
            if (val) {
                currentValue[index] = {
                    ...currentValue[index],
                    ...val
                };
            } else {
                currentValue.splice(index, 1);
            }
        } else {
            currentValue.push({
                key,
                ...val
            });
        }
        onChange?.(currentValue);
    }

    // 添加
    const handleAdd = () => {
        const [addItem] = patchStopsValue();
        handleChange(addItem.key, addItem);
    }

    // 删除
    const handleDel = (key) => handleChange(key);

    // 补充后的value
    const patchedValue = useMemo(() => patchStopsValue(value || (min > 0 ? null : [])), [value]);

    const len = patchedValue.length || 0;
    const enableAdd = len < max;
    const enableMinus = len > min;

    useEffect(() => {
        ref.current.value = patchedValue;
    }, [patchedValue]);

    const renders = useMemo(() => {
        const list = [
            children,
            enableAdd && (
                <View className="line-edit-form__footer-tools">
                    <View hoverClass="kb-hover-opacity" onClick={handleAdd}>
                        <AtIcon prefixClass="kb-icon" className="kb-color__grey kb-icon-size__base" value='plus-circle' />
                        <Text className="kb-icon__text--ml kb-color__grey">添加停靠点</Text>
                    </View>
                </View>
            )
        ];

        if (isLineEdit) {
            list.reverse();
        }

        return list;
    }, [isLineEdit, enableAdd, children]);

    return (
        <>
            {
                patchedValue.map((item, index) => {
                    return (
                        <View key={item.key} className="line-edit-stop-point__item">
                            {
                                enableMinus && (
                                    <View hoverClass="kb-hover-opacity" className="line-edit-stop-point__item-icon" onClick={() => handleDel(item.key)}>
                                        <AtIcon prefixClass="kb-icon" className="kb-color__red kb-icon-size__base" value='jian' />
                                    </View>
                                )
                            }
                            <PointAndContactSelector action={action} required={required} className={className} params={params} label={label} value={item} onChange={(val) => handleChange(item.key, val)} />
                        </View>
                    )
                })
            }
            {renders}
        </>
    )
}

export default StopPointSelector;