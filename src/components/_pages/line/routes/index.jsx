import { View } from "@tarojs/components";
import { checkIsStopPoint, getCurrentStopInfo } from "../../index/server/order/components/_utils/line";
import './index.scss';

const LineRoutes = (props) => {
    const { routes } = props;
    const hasRoutes = routes?.length > 0;

    return (
        hasRoutes
            ? (
                <View className="line-routes">
                    {
                        routes.map(item => (
                            <View key={item.id} className="line-routes__item">
                                <View className="line-routes__item--label kb-color__grey">{getCurrentStopInfo(item.type)?.label}</View>
                                <View className="line-routes__item--content">
                                    <View>{item.stop_name}</View>
                                    {
                                        checkIsStopPoint(item.type) && (
                                            <View className="kb-size__base kb-color__grey kb-spacing-sm-t">{item.phone} {item.name}</View>
                                        )
                                    }
                                </View>
                            </View>
                        ))
                    }
                </View>
            )
            : null
    )
}

export default LineRoutes;