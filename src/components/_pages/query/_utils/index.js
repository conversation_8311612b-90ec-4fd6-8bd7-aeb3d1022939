/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@base/utils/request';
// import { check } from '@base/utils/rules';
import { createCompletePath } from '@base/utils/navigator';
import { check } from '@base/utils/rules';
import { createGroup, getPage } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import isFunction from 'lodash/isFunction';
import isNumber from 'lodash/isNumber';
import isString from 'lodash/isString';
import { createGridBars } from './diff';
import { fixQueryInfoStatus } from './query.detail';

export function createItemInfo(data) {
  const { brand = '', waybill = '' } = data;
  return `${brand}-${waybill}`;
}

// 物流列表统计
export function getStateCount(data) {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/waybill/record/expressListCount',
      data: {
        state: 'mini_unsign',
        ...data,
      },
      toastLoading: false,
      onThen: ({ data }) => {
        // 严格处理统计结果，防止data是string时出现问题
        resolve(data && (isNumber(data) || isString(data)) ? 1 * data : 0);
      },
    });
  });
}

// 格式化物流信息
export function formatFlowData(str) {
  if (!str) return '';
  return `${str}`.replace(/\d{4}(-\d{1,2})+\s(\d{1,2}:?)+|\[|\]/g, '').trim();
}

// 触发更新物流信息
function splitUpdatedFlow(str) {
  const data = {};
  try {
    str = str.replace(/\r\n/g, '').replace(/\s/g, ' ');
    str.split('#,#').map((item) => {
      const [key, value] = item.split('#:#');
      data[key] = fixQueryInfoStatus(formatFlowData(value));
    });
  } catch (err) {
    console.log(err);
  }
  return data;
}

/**
 *
 * @description 更新物流信息
 * @param {*} list
 * @param {*} trigger
 * @returns
 */
export function updateFlow(list, trigger = false) {
  if (!trigger) {
    const filterList = isArray(list)
      ? list
          .filter(({ brand, waybill, status }) => waybill && brand && status !== '已签收')
          .map(createItemInfo)
      : [];
    const size = 30;
    const groups = createGroup(filterList, size).slice(0, 1);
    return Promise.all(groups.map((item) => updateFlow(item, true))).then((list) => {
      let updated = null;
      list.map((item) => {
        if (item) {
          updated = {
            ...updated,
            ...item,
          };
        }
      });
      return updated;
    });
  }
  return new Promise((resolve) => {
    if (list.length > 0) {
      request({
        url: '/v1/express/chaxunstat',
        toastLoading: false,
        data: {
          query_data: list.join(','),
        },
        onThen: ({ data }) => {
          const { s, desc } = data;
          if (s && desc) {
            const statusData = splitUpdatedFlow(s);
            const flowData = splitUpdatedFlow(desc);
            const result = {};
            list.map((item) => {
              const status = statusData[item];
              const lastLogistics = flowData[item];
              if (status || lastLogistics) {
                result[item] = {
                  status,
                  lastLogistics,
                };
              }
            });
            resolve(result);
          } else {
            resolve();
          }
        },
      });
    } else {
      resolve();
    }
  });
}

// 找人代取分享物流
export function checkExpressInfo(data) {
  const {
    dak_id,
    support_apply,
    isMarked,
    dakId = dak_id,
    is_self,
    isAppointment,
    home_deliver,
  } = data || {};
  if (isAppointment || home_deliver == '1') {
    return { action: 'appointment', label: '预约取件' };
  }
  return support_apply && dakId > 0 && is_self
    ? isMarked
      ? { action: '', label: '' }
      : { action: 'pickup', label: '找人代取' }
    : { action: 'waybill', label: '分享物流' };
}

// 根据搜索结果单条还是多条，跳转详情或者匹配列表页
export function jumpToExpressInfo({ word, list, dakId, errMsg, nomatch }, onMatch) {
  if (list.source == 'cabinet') {
    // 柜子查询
    onMatch({ list: [list], word, dakId, errMsg });
    return;
  }
  const listLength = isArray(list) ? list.length : 0;
  const isExactMatch = listLength === 1;
  const { $router: { path } = {} } = getPage();
  const target = path === createCompletePath('query/match') ? 'self' : 'blank';
  if (process.env.MODE_ENV === 'wkd') {
    // 微快递搜索手机号
    const isSearchByMobile = isExactMatch ? list.find((item) => item.brand === 'phone') : false;
    if (isSearchByMobile && word) {
      Taro.navigator({
        url: 'query/list',
        options: {
          phone: word,
        },
        target,
      });
      return;
    }
  }
  // 长度为1代表精确匹配，可以直接跳转结果页
  // 否则跳转单号匹配页面
  if (isExactMatch) {
    let optionsDakId = dakId;
    let pickupCode = '';
    let [{ brand, waybill, inn: itemInn, list: itemList } = {}] = list;
    const onlyOne = isArray(itemList) ? itemList.length : 0;
    if (onlyOne === 1) {
      // 查取件码：新数据结构，判断是否有单条物流信息
      const [{ brand: itemBrand, waybill_no: itemWaybill, pickup_code }] = itemList;
      const { inn_id } = itemInn || {};
      const isSeemPickupCode = new RegExp(/^\*+$/g).test(pickup_code);
      console.info('isSeemPickupCode=====>173', isSeemPickupCode);
      brand = itemBrand;
      waybill = itemWaybill;
      pickupCode = isSeemPickupCode ? '' : pickup_code;
      if (inn_id) {
        optionsDakId = inn_id;
      }
    }
    if (brand && waybill) {
      Taro.navigator({
        url: 'query/detail',
        options: {
          brand,
          waybill,
          dakId: optionsDakId,
          pickupCode,
        },
        target,
      });
      return;
    }
  }

  const matchRes = {
    nomatch,
    word,
    dakId,
    list: isArray(list) ? list : [],
  };

  // 未匹配到结果
  matchRes.errMsg =
    errMsg || (matchRes.list.length === 0 && word ? matchWaybillAndBrandErrorMsg(word) : '');

  if (isFunction(onMatch)) {
    onMatch(matchRes);
    return;
  }
  word &&
    target !== 'self' &&
    Taro.navigator({
      url: 'query/match',
      key: 'query-match-data',
      options: matchRes,
    });
}

/**
 *
 * @description 搜索条请求接口与参数
 * @param {*} word
 * @returns
 */
export function getApiUrlAndDataQueryMatch(word, dakId, isCabinet) {
  if (process.env.MODE_ENV === 'wkd') {
    return {
      url: '/v1/synthesis/index',
      data: { input: word, chennel: 'mina' },
    };
  } else {
    return {
      url: '/api/weixin/mini/DakMini/Record/queryPickup',
      data: { word, supporMobile: '1', cm_id: dakId, is_cabinet: +isCabinet },
    };
  }
}

/**
 *
 * @description 查询匹配
 */
export function formatResponseQueryMatch(res, word) {
  if (process.env.MODE_ENV === 'wkd') {
    const { brand } = (res && res.data && res.data.data) || {};
    return {
      data: brand
        ? Object.keys(brand).map((item) =>
            item === 'phone'
              ? {
                  brand: item,
                  phone: brand[item],
                }
              : {
                  brand: item,
                  brandName: brand[item],
                  waybill: word,
                },
          )
        : null,
    };
  }
}

/**
 *
 * @description 检查是否为完整单号
 * @param {*} word
 * @returns
 */
export function checkIsFullWaybillNo(word) {
  return word.length > 4 && check('phone', word).code !== 0;
}

/**
 *
 * @description 匹配错误信息
 */
export function matchWaybillAndBrandErrorMsg(word) {
  let tag = '';
  if (check('phone', word).code === 0) {
    tag = '手机号';
  } else if (word.length === 4) {
    tag = '单号尾号';
  } else {
    return '未匹配到快递品牌';
  }
  return `未找到${tag}${word}的待取包裹`;
}

/**
 *
 * @description 匹配运单号与品牌
 */
export function matchWaybillAndBrand(word, apiOpts, dakId, isCabinet) {
  return new Promise((resolve) => {
    if (!word) {
      resolve({});
      return;
    }
    request({
      ...getApiUrlAndDataQueryMatch(word, dakId, isCabinet),
      toastLoading: false,
      ...apiOpts,
      formatResponse: (res) => formatResponseQueryMatch(res, word),
      onThen: ({ data, code, msg }) =>
        resolve({
          data,
          errMsg: code > 0 ? msg : '',
          nomatch: checkIsFullWaybillNo(word) && isEmpty(data),
        }),
    });
  });
}
/**
 *
 * @description 创建头部bars
 * @param isVip
 */
export { createGridBars };
