/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { formatShopInfo } from '@/components/_pages/store-card/_utils';
import { requestPayment } from '@/utils/qy';
import request from '@base/utils/request';
import { isAvailableValue } from '@base/utils/utils';
import Taro from '@tarojs/taro';
import { parse } from 'querystring';

/**
 *
 * @description 分享预约取件
 * @param {*} param0
 * @returns
 */
export function proxyPickupBatchCreate({ list, dakId }) {
  return new Promise((resolve, reject) => {
    let waybillInfoList = list
      .filter(({ isLike, isMark, isMarked = isMark == '1' }) => isLike != 1 && !isMarked)
      .map(({ waybill, brand }) => ({
        waybill,
        brand,
      }));
    if (process.env.MODE_ENV === 'wkd') {
      waybillInfoList = JSON.stringify(waybillInfoList);
    }
    request({
      toastLoading: false,
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/g_wkd/v2/mina/Dak/proxyPickupBatchCreate'
          : '/api/weixin/mini/DakMini/Record/proxyPickupBatchCreate',
      data: {
        dakId,
        waybillInfoList,
      },
      nonceKey: process.env.MODE_ENV === 'yz' ? 'md5:dakId,waybillInfoList' : '',
      toastError: true,
      onThen: ({ code, data, msg }) => {
        if (code == 0 && data) {
          resolve(data);
        } else {
          reject(new Error(msg));
        }
      },
    });
  });
}

function complaintStatusString(status) {
  return isAvailableValue(status) ? status.toString() : '';
}

export function formatComplaintDataAndInfo(data, action) {
  let {
    // 投诉必要数据
    dakId,
    brand,
    waybill,
    // 投诉状态与描述
    id,
    deal_desc = '',
    is_deal = '',
    complaintStatus,
    dealDesc,
    // 格式化的状态与描述
    status,
    desc = dealDesc || deal_desc,
    complaintId,
  } = data || {};
  status =
    complaintStatusString(status) ||
    complaintStatusString(complaintStatus) ||
    complaintStatusString(is_deal);
  // complaint.get重新获取投诉信息后更新状态
  if ((!status && action === 'update') || id) {
    status = id ? '1' : '0';
  }
  let complaintInfo = null;
  if (status || desc) {
    complaintInfo = {
      status,
      desc,
      complaintId,
    };
  }
  const complaintData = { dakId, brand, waybill, id };
  return {
    complaintData,
    complaintInfo,
  };
}

/**
 *
 * @description 获取带取件的驿站信息
 * @param {*} data
 */
export function getStoreInfoByWaybill(data) {
  return new Promise((resolve) => {
    request({
      url: '/g_wkd/v2/mina/Dak/dakPickupInfo',
      toastLoading: false,
      data,
      onThen: ({ code, data }) => {
        if (code == 0 && data) {
          const { inn_info, recordMarkStatus } = data;
          const {
            express_status: expressStatus,
            collect_code,
            dak_auth_status,
            record_num,
            ...restInnInfo
          } = inn_info;
          const pickupCode = expressStatus != '0' ? collect_code : '';
          const shopInfo = formatShopInfo(restInnInfo);
          const isMarked = recordMarkStatus == '1';

          resolve({
            ...shopInfo,
            canTogether: record_num > 0 && dak_auth_status == '1',
            record_num,
            dak_auth_status,
            expressStatus,
            pickupCode,
            isMarked,
          });
        }
      },
    });
  });
}

export const scanScreenPickupPopUp = (req) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/waybill/record/scanScreenPickupPopUp',
      data: req,
      onThen(res) {
        const { code, data, msg } = res;
        if (code === 0) {
          resolve(data);
        } else {
          reject(msg);
        }
      },
    });
  });
};

export const getScanScreen = (query) => {
  if (process.env.MODE_ENV === 'wkd') return Promise.resolve(false);

  const { qrcode_expired } = query || {};
  return new Promise((resolve) => {
    // 非扫码进入预约取件
    if (!qrcode_expired) {
      resolve(false);
    } else {
      scanScreenPickupPopUp(query)
        .then((data) => {
          resolve(data);
        })
        .catch((msg) => {
          Taro.kbToast({ text: msg });
        });
    }
  });
};

// 跑腿改变，废弃
export const getLegWorkMoney = (dakId) => {
  if (process.env.MODE_ENV === 'wkd') return Promise.reject();
  if (!dakId) return Promise.reject();
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/DakMini/Record/getLegWorkMoney',
      data: { dak_id: dakId },
      onThen({ data, code }) {
        if (code === 0 && data > 0) {
          resolve(data);
        } else {
          reject();
        }
      },
    });
  });
};

export const getPickUpApi = (data) => {
  const { brand, dak_id, list, waybill, ...rest } = data || {};
  let url;
  let requestData = {
    ...rest,
    brand,
    dak_id,
    waybill,
    action: waybill && brand ? 'one' : 'all',
  };
  if (process.env.MODE_ENV === 'wkd') {
    url = '/g_order_core/v2/mina/Dak/yzSigned';
    if (list) {
      const waybill_List = list
        .filter((item) => item.isMark == '0')
        .map(({ waybill, brand }) => ({ waybill, brand }));
      requestData = {
        ...requestData,
        waybill_List: JSON.stringify(waybill_List),
        action: '',
      };
      if (waybill_List.length === 1) {
        requestData = {
          ...requestData,
          ...waybill_List[0],
          action: 'one',
        };
      }
    }
  } else {
    url = '/api/weixin/mini/waybill/logistic/signExpress';
  }
  console.log('requestData', requestData);
  return { url, data: requestData };
};
export function signExpressPaySign(param) {
  return new Promise((resolve, reject) => {
    if (process.env.MODE_ENV === 'wkd') return reject();
    request({
      url: '/api/weixin/mini/minpost/Pay/legWorkPaySign',
      data: { ...param },
      onThen(res) {
        const { code, data, msg } = res;
        if (code === 0) {
          requestPayment(data)
            .then(() => {
              resolve();
            })
            .catch((error) => {
              reject(error.message);
            });
        } else {
          reject(msg);
        }
      },
    });
  });
}

export function getpicktype(params = {}) {
  // 区分是否从身份码页面跳转进入
  const { q = '', picktype, source = '' } = params;

  if (picktype) {
    return {
      picktype,
      source,
    };
  }
  const { picktype: type } = parse(decodeURIComponent(q).split('?')[1]);
  return type ? { picktype: type, source: '' } : {};
}

export function getLightList({ dak_id, waybill, source = '1' }) {
  return new Promise((resolve, reject) => {
    request({
      url: '/api/weixin/mini/waybill/record/getLightBarByWaybills',
      data: {
        dak_id,
        waybill,
        source,
      },
      onThen(res) {
        const { code, data } = res;
        if (code === 0 && Array.isArray(data)) {
          resolve(data);
        } else {
          reject();
        }
      },
    });
  });
}

export function openLights(params) {
  return new Promise((resolve) => {
    request({
      url: '/api/weixin/mini/waybill/record/openLightBar',
      data: params,
      onThen(res) {
        resolve(res);
      },
    });
  });
}
