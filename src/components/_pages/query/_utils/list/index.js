/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { formatFlowData } from '@/components/_pages/query/_utils';
import { fixQueryInfoStatus } from '@/components/_pages/query/_utils/query.detail';
import { dateCalendar } from '@base/utils/utils';
import isArray from 'lodash/isArray';

export const getStatusColor = (status) => {
  switch (status) {
    case '签收':
    case '已签收':
      return 'green';
      break;
    case '暂无物流':
      return 'grey';
    case '运输中':
      return 'orange';
      break;
    default:
      return 'brand';
  }
};

export const getApiConfig = (page) => {
  // 列表请求配置
  const { channel, checkType, shop_id, phone, customerId, token, is_self } = page.$router.params;
  page.pageKey = 'page';
  let url = '/g_wkd/v2/Account/getWsShopExpress',
    data = { shop_id, phone };
  let formatResponse = ({ data }) => {
    let list;
    if (process.env.MODE_ENV == 'wkd') {
      list = (data && data.data) || [];
    } else {
      list = data;
    }
    if (isArray(list) && list.length > 0) {
      return {
        code: 0,
        data: {
          list: list.map(({ time, currentFlow, status, ...rest }) => {
            let fixStatus = fixQueryInfoStatus(status);
            return {
              time: dateCalendar(time, {
                timer: true,
              }),
              status: fixStatus,
              color: getStatusColor(fixStatus),
              currentFlow: formatFlowData(currentFlow),
              ...rest,
            };
          }),
        },
      };
    }
    return {
      data: void 0,
    };
  };

  if (customerId) {
    // 小邮筒，按照大客户id查找
    url = url.replace('WsShop', 'Customer');
    data = {
      customerId,
      channel,
    };
  } else if (checkType == 'kdy') {
    // 快递员查件
    url = '/v1/WeApp/getOrderListByPhone';
    data = {
      phone,
      random: token,
    };
    page.pageKey = 'page_number';
  } else if (checkType === 'yz') {
    page.pageKey = 'page_number';
    url = '/api/weixin/mini/express/Info/getPackageByPhone';
    data = {
      random: token,
      phone,
      is_self,
    };
  }
  return {
    url,
    data,
    formatResponse,
  };
};
