import { View } from '@tarojs/components';
import { useCreateBars } from './_utils';

const UserNavigator = () => {
  const { groups, onOperator } = useCreateBars();

  return (
    <View>
      {
        groups.map(group => (
          <View className='kb-box kb-navigator__group' key={group.key}>
            {group.bars.map((item) => {
              return (
                <View
                  className='kb-navigator kb-navigator-xl'
                  key={item.key}
                  hoverClass='kb-hover'
                  onClick={() => onOperator(item)}
                >
                  <View className='kb-navigator__label'>{item.label}</View>
                </View>
              );
            })}
          </View>
        ))
      }
    </View>
  );
};

export default UserNavigator;
