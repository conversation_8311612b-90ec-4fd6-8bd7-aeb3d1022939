import { navigator } from '~base/utils/navigator';
import { makePhoneCall } from '~base/utils/utils';
import { bindPartnerLabel } from '../../partner/_utils';

export function useCreateBars() {
  const barsFirst = [
    {
      key: 'partner',
      label: `合作${bindPartnerLabel}`,
      path: 'user/partner',
    },
  ];

  const bars = [
    {
      key: 'service',
      label: '客服中心',
      phoneNumber: '13120728261',
    },
    {
      key: 'service',
      label: '意见反馈',
      path: 'user/feedback',
    },
  ];
  if (process.env.MODE_ENV === 'client') {
    bars.unshift({
      key: 'adr',
      label: '联系人',
      path: 'contacts',
    });
  }

  const onOperator = ({ path, phoneNumber }) => {
    if (path) {
      navigator({
        url: path,
      });
    } else if (phoneNumber) {
      makePhoneCall(phoneNumber);
    }
  };

  const groups = [
    {
      key: '0',
      bars: barsFirst,
    },
    {
      key: '1',
      bars,
    },
  ];

  return {
    groups,
    onOperator,
  };
}
