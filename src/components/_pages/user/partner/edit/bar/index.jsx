import { View } from "@tarojs/components";
import { AtButton, AtIcon } from "taro-ui";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { PartnerEditModalName } from "../modal/_utils";
import { bindPartnerLabel } from "../../_utils";

/**
 * 
 * @param {{
 *   data?:any;
 *   size?:'normal'|'small'|'large';
 *   label?:string;
 *   onSuccess?:any;
 * }} props 
 */
const PartnerEditBar = (props) => {
    const { data, onSuccess, label = `绑定${bindPartnerLabel}`, size } = props;
    const isEdit = !!data;

    const { onOpenChange, form } = useActionContext(PartnerEditModalName, {
        onSuccess
    });

    const handleClick = () => {
        form?.setFieldsValue?.({ id: data?.id, name: data?.name, phone: data?.phone });
        onOpenChange(true)
    }

    return (
        <AtButton type='primary' circle onClick={handleClick} size={size}>{label}</AtButton>
    )
}


export default PartnerEditBar;