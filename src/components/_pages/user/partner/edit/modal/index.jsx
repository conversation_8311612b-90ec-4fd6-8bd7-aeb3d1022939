import { View } from "@tarojs/components";
import { AtButton, AtFloatLayout, AtInput } from "taro-ui";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { PartnerEditModalName } from "./_utils";
import rules, { check } from "~base/utils/rules";
import { serviceURL } from "~/services/_utils";
import CountDown from "~base/components/count-down";
import { getBindPartnerSafeCode } from "~/services/user/partner";
import Taro from "@tarojs/taro";
import './index.scss';
import { bindPartnerLabel, bindPartnerMiniAppName } from "../../_utils";

const PartnerEditModal = () => {

    const { open, onOpenChange, form } = useActionContext(PartnerEditModalName, {
        form: {
            id: { required: false },
            name: {
                tag: `${bindPartnerLabel}姓名`
            },
            phone: {
                reg: rules.phone,
                tag: `${bindPartnerLabel}手机号`
            },
            sms_code: {
                tag: '验证码'
            }
        },
        api: {
            url: serviceURL('/BindRelation/bind'),
            toastLoading: false,
            toastSuccess: true,
            toastError: true,
        }
    });

    // 关闭
    const handleClose = () => onOpenChange(false);

    // 输入
    const handleChange = (name, value) => {
        form?.setFieldsValue({ [name]: value });
    }

    // 确认
    const handleConfirm = async () => {
        await form.onFinish();
    }

    // 获取验证码
    const handleClickSafeCode = async () => {
        const { phone } = form.data;
        const checkRes = check('phone', phone, true);
        if (checkRes.code > 0) {
            Taro.kbToast({
                text: checkRes.msg
            });
            return;
        }
        return getBindPartnerSafeCode({
            phone
        });
    }

    const isEdit = !!form?.data?.id;
    const title = `${isEdit ? '编辑' : '绑定'}${bindPartnerLabel}`;

    return (
        <AtFloatLayout
            isOpened={open}
            onClose={handleClose}
            className="partner-edit-modal"
        >
            {
                open && (
                    <View className="kb-form">
                        <View className="kb-form__title">{title}</View>
                        <View className="kb-spacing-md-lr">
                            <View className="kb-form__item kb-form__item--required">
                                <View className="item-title">{bindPartnerLabel}姓名</View>
                                <View className="item-content">
                                    <AtInput clear value={form?.data?.name} cursorSpacing={150} maxLength={12} onChange={(v) => handleChange('name', v)} placeholder="请输入" />
                                </View>
                            </View>
                            <View className="kb-form__item--tips">
                                姓名限制1~12个字符以内，一个汉字为一个字符
                            </View>
                            <View className="kb-form__item kb-form__item--required">
                                <View className="item-title">手机号</View>
                                <View className="item-content">
                                    <AtInput clear value={form?.data?.phone} cursorSpacing={150} maxLength={11} type='mobile' onChange={(v) => handleChange('phone', v)} placeholder="请输入" />
                                </View>
                            </View>
                            <View className="kb-form__item--tips">
                                请填写{bindPartnerLabel}【{bindPartnerMiniAppName}】小程序的登录手机号
                            </View>
                            <View className="kb-form__item kb-form__item--required">
                                <View className="item-title">验证码</View>
                                <View className="item-content">
                                    <AtInput clear value={form?.data?.sms_code} cursorSpacing={150} maxLength={8} type='number' onChange={(v) => handleChange('sms_code', v)} placeholder="请输入">
                                        <CountDown onClick={handleClickSafeCode} />
                                    </AtInput>
                                </View>
                            </View>
                        </View>
                        <View className="kb-form__btn at-row at-row__align--center">
                            <View className="at-col">
                                <AtButton circle onClick={handleClose}>取消</AtButton>
                            </View>
                            <View className="at-col kb-spacing-md-l">
                                <AtButton circle type='secondary' onClick={handleConfirm} loading={form?.loading}>确定</AtButton>
                            </View>
                        </View>
                    </View>
                )
            }
        </AtFloatLayout>
    )
}

export default PartnerEditModal;