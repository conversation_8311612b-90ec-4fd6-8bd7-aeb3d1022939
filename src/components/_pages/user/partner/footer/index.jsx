import { View } from "@tarojs/components";
import PartnerEditBar from "../edit/bar";
import { AtButton } from "taro-ui";

/**
 * 
 * @param {{
 *   size?:string;
 *   label?:string;
 *   onSuccess?:()=>void;
 *   onClick?:(e:any)=>void;
 *   className?:string;
 *   isChooseBind?:boolean;
 * }} props 
 * @returns 
 */
const UserPartnerFooter = (props) => {
    const { isChooseBind, size, label, className, onClick, onSuccess } = props;

    return (
        <>
            {
                process.env.MODE_ENV === 'server' || isChooseBind
                    ? (
                        <View className={className}>
                            {
                                process.env.MODE_ENV === 'server'
                                    ? (
                                        <PartnerEditBar size={size} label={label} onSuccess={onSuccess} />
                                    )
                                    : (
                                        <AtButton size={size} circle type='primary' onClick={onClick}>查看附近车主</AtButton>
                                    )
                            }
                        </View>
                    ) : null
            }
        </>
    )
}

export default UserPartnerFooter;