import mapIcon from '@/assets/images/<EMAIL>';
import { Image, Text, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import omit from 'lodash/omit';
import { useMemo } from 'react';
import { AtIcon, AtNoticebar } from 'taro-ui';
import { orderNext } from '~/components/_pages/index/client/_utils/orderProcess';
import { unbindPartner } from '~/services/user/partner';
import { serviceURL } from '~/services/_utils';
import LongList from '~base/components/long-list';
import { useLongList } from '~base/components/long-list/hooks';
import PageLayout from '~base/components/page/layout';
import KbSwipeAction from '~base/components/swipe-action';
import { formatNumeral } from '~base/utils/numeral';
import { dateCalendar, makePhoneCall, randomCode } from '~base/utils/utils';
import PartnerEditModal from '../edit/modal';
import UserPartnerFooter from '../footer';
import { bindPartnerLabel } from '../_utils';
import './index.scss';
import { checkTruckOwnerIsCompany, partnerListRefreshKey } from './_utils';

const swipeActionOptions = [
  {
    key: 'unbind',
    text: '解绑',
    style: {
      backgroundColor: '#E34D59',
    },
  },
];

/**
 *
 * @param {{
 *   action?: 'normal'|'choose'|'choose-nearby';
 * }} props
 * @returns
 */
const UserPartnerList = (props) => {
  const { action = 'normal', orderUseType = 'normal', locationInfo } = props;
  const isChooseBind = action === 'choose'; // 选择绑定的车主
  const isNearby = action === 'nearby'; // 无绑定车主，查看附近，
  const isChoose = isChooseBind; // 下单选择
  const isRental = orderUseType === 'rental';
  const { config, list } = useLongList(
    serviceURL(isNearby ? '/User/getNearbyVehicle' : '/BindRelation/list'),
    {
      refresherKey: partnerListRefreshKey,
      formatItem: (item) => {
        const infoKey =
          process.env.MODE_ENV === 'client' ? 'vehicle_owner_info' : 'goods_owner_info';
        return {
          ...item,
          ...omit(item[infoKey], 'id'),
        };
      },
    },
  );

  const hasList = list.length > 0;
  const active = useMemo(
    () =>
      isNearby
        ? {
            lng: locationInfo?.longitude,
            lat: locationInfo?.latitude,
          }
        : true,
    [locationInfo, isNearby],
  );

  // 拨打电话
  const handelMakePhoneCall = (p, e) => {
    e.stopPropagation();
    makePhoneCall(p);
  };

  // 点击
  const handleSwipeActionClick = (item) => {
    Taro.kbModal({
      title: `确定与该${bindPartnerLabel}解绑？`,
      content: (
        <View className='kb-text__center'>
          {process.env.MODE_ENV === 'client'
            ? `解绑后无法再向该${bindPartnerLabel}下单`
            : `解绑后该${bindPartnerLabel}无法再向您下单`}
        </View>
      ),
      cancelText: '取消',
      onConfirm: () => {
        unbindPartner({
          id: item.id,
        }).then((res) => {
          const isSuccess = `${res.code}` === '0';
          if (isSuccess) {
            config.loader();
          }
        });
      },
    });
  };

  // 设置停靠点
  const handleSetStopPoints = (item, pos) => {
    if (process.env.MODE_ENV === 'server') {
      if (pos === 'content') {
        // 车主，仅点击设置按钮才调整
        return;
      }
    } else {
      if (isNearby) return;
      if (isChoose) {
        // 订单编辑-选择车主信息
        const params = {
          url: 'line/delivery',
          data: {
            truckOwner: item,
          },
          options: {
            vehicle_owner_id: item.vehicle_owner_id,
            vehicle_owner_phone: item.contact_phone,
          },
        };
        if (isRental) {
          params.url = 'truck/choose/rental';
          params.key = randomCode();
        }
        orderNext(params);
        return;
      }
    }

    Taro.navigator({
      url: 'user/partner/stop-points',
      options: {
        id: item.id,
        goods_owner_id: item.goods_owner_id,
        vehicle_owner_id: item.vehicle_owner_id,
        name: item.contact_name,
        phone: item.contact_phone,
      },
    });
  };

  // 刷新
  const handleSuccess = () => {
    config.loader();
  };

  // 跳转附近车主
  const handleClickNearby = () => {
    Taro.navigator({
      url: 'user/partner/nearby',
    });
  };

  // 允许翻页
  const enableMore = !isNearby;

  return (
    <PageLayout
      renderFooter={
        <>
          {hasList && (
            <UserPartnerFooter
              className='kb-spacing-md kb-background__white'
              onSuccess={handleSuccess}
              onClick={handleClickNearby}
              isChooseBind={isChooseBind}
            />
          )}
        </>
      }
      renderHeader={
        <>
          {process.env.MODE_ENV === 'client' && isNearby && (
            <AtNoticebar icon='amaze'>
              您可以联系车主，对方绑定您后（绑定时需要您提供短信验证码），可正常下单用车
            </AtNoticebar>
          )}
        </>
      }
    >
      <LongList
        active={active}
        data={config}
        renderEmptyFooter={
          <UserPartnerFooter
            size='small'
            label='去绑定'
            onSuccess={handleSuccess}
            onClick={handleClickNearby}
            className='footer-inner'
            isChooseBind={isChooseBind}
          />
        }
        className='user-partner-list'
        enableMore={enableMore}
      >
        <View className='kb-list'>
          {list.map((item) => (
            <View className='kb-list__item--wrapper kb-box' key={item.id}>
              <KbSwipeAction
                onClick={() => handleSwipeActionClick(item)}
                onClickContent={() => handleSetStopPoints(item, 'content')}
                rightButtons={swipeActionOptions}
                className='kb-radius-tr'
                disabled={isChoose || isNearby}
                hoverClass={isNearby ? 'none' : 'kb-hover'}
              >
                <View className='kb-list__item'>
                  <View className='item-content'>
                    <View className='at-row at-row__align--center at-row__justify--between'>
                      <View>
                        <Text className='kb-icon__text--mr'>
                          {process.env.MODE_ENV === 'client'
                            ? item.company_name
                            : item.contact_name}
                        </Text>
                        {isChoose && (
                          <AtIcon
                            className='kb-icon-size__xs'
                            prefixClass='kb-icon'
                            value='arrow'
                          />
                        )}
                      </View>
                      <View
                        hoverClass='kb-hover-opacity'
                        onClick={(e) => handelMakePhoneCall(item.contact_phone, e)}
                      >
                        <AtIcon
                          className='kb-color__brand kb-icon-size__base'
                          prefixClass='kb-icon'
                          value='tel'
                        />
                      </View>
                    </View>
                    {process.env.MODE_ENV === 'client' && checkTruckOwnerIsCompany(item) && (
                      <View className='kb-size__base kb-color__grey kb-spacing-sm-t'>
                        <Text className='user-partner-list__label'>联系人</Text>
                        <Text>{item.contact_name}</Text>
                      </View>
                    )}
                    <View className='kb-size__base kb-color__grey kb-spacing-sm-t'>
                      <Text className='user-partner-list__label'>联系电话</Text>
                      <Text>{item.contact_phone}</Text>
                    </View>
                    {item.cooperation_num > 0 && (
                      <View className='kb-size__base kb-spacing-sm-t'>
                        <Text className='kb-color__brand user-partner-list__label'>
                          合作过 {item.cooperation_num}次
                        </Text>
                        <Text className='kb-color__grey'>
                          最后一次合作：{dateCalendar(item.last_cooperation_time, { timer: true })}
                        </Text>
                      </View>
                    )}
                    {process.env.MODE_ENV === 'client' && (
                      <View className='user-partner-list__addr'>
                        <Image
                          className='user-partner-list__addr-img'
                          src={mapIcon}
                          mode='widthFix'
                        />
                        <Text className='user-partner-list__addr-text user-partner-list__addr-text-ellipsis'>
                          {item.district}
                          {item.address}
                        </Text>
                        {item.distance > 0 && (
                          <Text className='user-partner-list__addr-text kb-color__brand'>
                            距您{formatNumeral(item.distance / 1000)}km
                          </Text>
                        )}
                      </View>
                    )}
                    {process.env.MODE_ENV === 'server' && (
                      <View
                        className='user-partner-list__btn'
                        hoverClass='kb-hover'
                        onClick={() => handleSetStopPoints(item, 'btn')}
                      >
                        <Image
                          className='user-partner-list__btn-img'
                          src={mapIcon}
                          mode='widthFix'
                        />
                        <Text className='user-partner-list__btn-text'>设置停靠点</Text>
                      </View>
                    )}
                  </View>
                </View>
              </KbSwipeAction>
            </View>
          ))}
        </View>
      </LongList>
      <PartnerEditModal />
    </PageLayout>
  );
};

export default UserPartnerList;
