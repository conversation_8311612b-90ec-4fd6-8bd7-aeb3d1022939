import { View } from "@tarojs/components";
import { AtButton, AtIcon } from "taro-ui";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { PartnerStopPointsEditModalName } from "../modal/_utils";
import { bindPartnerLabel } from "../../../_utils";
import OperateDepartSelector from "~/components/_pages/order/detail/operate/depart/modal/form/selector";
import { useMemo } from "react";
import isArray from "lodash/isArray";
import { assignPartnerStopPoints } from "~/services/user/partner/stop-points";

/**
 * 
 * @param {{
 *   data?:any;
 *   size?:'normal'|'small'|'large';
 *   label?:string;
 *   onSuccess?:any;
 * }} props 
 */
const PartnerStopPointsEditBar = (props) => {
    const { params, data, onSuccess, label = "分配停靠点", size } = props;

    const value = useMemo(() => {
        return isArray(data) ? data.map(item => ({ value: item.stop_id, label: item.stop_name })) : null;
    }, [data])

    const actionSheetProps = useMemo(() => ({
        optionRender: text => text,
        multiple: true
    }), []);

    // 确认
    const handleChange = async (val) => {
        const res = await assignPartnerStopPoints({
            relation_id: params?.id,
            stop_ids: val.map(item => item.value),
        });
        const isSuccess = `${res.code}` === '0';
        if (isSuccess) {
            onSuccess?.();
        }
        return isSuccess;
    }

    return (
        <OperateDepartSelector
            label="停靠点"
            actionSheetProps={actionSheetProps}
            value={value}
            onChange={handleChange}
        >
            <AtButton type='primary' circle size={size}>{label}</AtButton>
        </OperateDepartSelector>
    )
}


export default PartnerStopPointsEditBar;