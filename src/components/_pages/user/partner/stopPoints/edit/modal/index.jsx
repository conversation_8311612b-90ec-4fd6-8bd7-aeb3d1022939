import { View } from "@tarojs/components";
import { AtButton, AtFloatLayout, AtInput } from "taro-ui";
import { useActionContext } from "~base/components/actionContext/_utils/hooks";
import { PartnerStopPointsEditModalName } from "./_utils";
import rules, { check } from "~base/utils/rules";
import { serviceURL } from "~/services/_utils";
import CountDown from "~base/components/count-down";
import { getBindPartnerSafeCode } from "~/services/user/partner";
import Taro from "@tarojs/taro";
import './index.scss';
import { bindPartnerLabel, bindPartnerMiniAppName } from "../../../_utils";

const PartnerStopPointsEditModal = () => {

    const { open, onOpenChange, form } = useActionContext(PartnerStopPointsEditModalName, {
        form: {
            id: { required: false },
            name: {
                tag: `${bindPartnerLabel}姓名`
            },
            phone: {
                reg: rules.phone,
                tag: `${bindPartnerLabel}手机号`
            },
            safe_code: {
                tag: '验证码'
            }
        },
        api: {
            url: serviceURL('/User/updateBindPartner'),
            toastLoading: false,
            toastSuccess: true,
            toastError: true,
        }
    });

    // 关闭
    const handleClose = () => onOpenChange(false);

    // 输入
    const handleChange = (name, value) => {
        form?.setFieldsValue({ [name]: value });
    }

    // 确认
    const handleConfirm = async () => {
        await form.onFinish();
    }

    // 获取验证码
    const handleClickSafeCode = async () => {
        const { phone } = form.data;
        const checkRes = check('phone', phone, true);
        if (checkRes.code > 0) {
            Taro.kbToast({
                text: checkRes.msg
            });
            return;
        }
        getBindPartnerSafeCode({
            phone
        });
    }

    const isEdit = !!form?.data?.id;
    const title = `${isEdit ? '编辑' : '绑定'}${bindPartnerLabel}`;

    return (
        <AtFloatLayout
            isOpened={open}
            onClose={handleClose}
            className="partner-edit-modal"
            title='请选择停靠点'
        >
            <View></View>
        </AtFloatLayout>
    )
}

export default PartnerStopPointsEditModal;