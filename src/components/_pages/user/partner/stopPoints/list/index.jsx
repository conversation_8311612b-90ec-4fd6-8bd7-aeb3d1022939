import { View } from "@tarojs/components";
import { AtButton, AtIcon } from "taro-ui";
import { serviceURL } from "~/services/_utils";
import LongList from "~base/components/long-list";
import { useLongList } from "~base/components/long-list/hooks";
import './index.scss';
import Taro from "@tarojs/taro";
import { partnerStopPointsListRefreshKey } from "./_utils";
import PartnerStopPointsEditBar from "../edit/bar";
import PageLayout from "~base/components/page/layout";
import { bindPartnerLabel } from "../../_utils";
import { unbindPartner } from "~/services/user/partner";
import pick from "lodash/pick";
import { useMemo } from "react";
import { makePhoneCall } from "~base/utils/utils";
import { deletePartnerStopPoints, getPartnerStopPointsApi } from "~/services/user/partner/stop-points";

const UserPartnerStopPointsList = (props) => {
    const { params } = props;
    const { config, list } = useLongList(getPartnerStopPointsApi, { refresherKey: partnerStopPointsListRefreshKey });

    const active = useMemo(() => pick(params, process.env.MODE_ENV === 'client' ? ['vehicle_owner_id'] : ['goods_owner_id']), [params]);

    // 刷新
    const handleSuccess = () => {
        config.loader();
    }

    // 解绑
    const handleUnbind = (item) => {

        Taro.kbModal({
            title: '确定移除停靠点',
            content: (
                <View className="kb-text__center">
                    “{item.stop_name}”？
                </View>
            ),
            cancelText: '取消',
            onConfirm: async () => {
                const res = await deletePartnerStopPoints({ relation_id: params.id, stop_ids: [item.stop_id] });
                const isSuccess = `${res.code}` === '0';
                if (isSuccess) {
                    config.loader();
                }
            }
        });

    }

    // 联系车主
    const handleContactServer = () => {
        makePhoneCall(params?.phone);
    }

    const hasList = list.length > 0;

    return (
        <PageLayout
            renderFooter={
                <>
                    {
                        process.env.MODE_ENV === 'server' && hasList && (
                            <View className='kb-spacing-md kb-background__white'>
                                <PartnerStopPointsEditBar params={params} data={list} onSuccess={handleSuccess} />
                            </View>
                        )
                    }
                </>
            }
        >
            <LongList
                active={active}
                data={config}
                renderEmptyFooter={
                    <View className='footer-inner'>
                        {
                            process.env.MODE_ENV === 'server'
                                ? (
                                    <PartnerStopPointsEditBar params={params} size='small' onSuccess={handleSuccess} />
                                )
                                : (
                                    <AtButton size='small' circle type='primary' onClick={handleContactServer}>联系车主</AtButton>
                                )
                        }
                    </View>
                }
                className="user-partner-stop-points-list"
            >
                <View className="kb-list kb-list-border">
                    <View className="kb-color__grey-1-1">{process.env.MODE_ENV === 'server' ? bindPartnerLabel : '我'}的停靠点（{list.length}）</View>
                    {
                        list.map(item => (
                            <View className="kb-list__item" key={item.stop_id}>
                                <View className="item-content">
                                    <View className="at-row at-row__align--center at-row__justify--between">
                                        <View>{item.stop_name}</View>
                                        {
                                            process.env.MODE_ENV === 'server' && (
                                                <View className="user-partner-stop-points-list__close" hoverClass="kb-hover-opacity" onClick={() => handleUnbind(item)}>
                                                    <AtIcon value='close' />
                                                </View>
                                            )
                                        }
                                    </View>
                                </View>
                            </View>
                        ))
                    }
                </View>
            </LongList>
        </PageLayout>
    )
}

export default UserPartnerStopPointsList;