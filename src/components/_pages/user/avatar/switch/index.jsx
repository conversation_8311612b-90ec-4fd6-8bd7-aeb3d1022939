import { Button, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import isFunction from 'lodash/isFunction';
import { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { AtIcon } from 'taro-ui';
import KbAvatar from '~/components/_pages/user/avatar';
import { useStorage } from '~base/utils/storage';
import { chooseImageByActionSheet, getSystemInfoSync } from '~base/utils/utils';
import './index.scss';
import { cropAvatar, updateUserInfo } from '../../info/edit/_utils';

/**
 *
 * @description 头像切换组件
 * @param {{showIcon?:boolean;onChange?:()=>void;}} props
 * @returns
 */
const AvatarSwitch = (props) => {
  const ref = useRef({});
  const { loginData } = useSelector((state) => state.global);
  const { userInfo: { avatar_url } = {} } = loginData || {};
  const [currentAvatarUrl, setCurrentAvatarUrl] = useState('');
  const { platform } = getSystemInfoSync();
  const { data: dataStorage, update: updateStorage } = useStorage('canUseAvatarComponent');
  const { onChange, size, showIcon = !avatar_url } = props;

  const canUseAvatarComponent = platform === 'ios' || (dataStorage && dataStorage.data); // 检查是否可用

  const stopActionSheet = () => clearTimeout(ref.current.delayTime);

  const handleChooseAvatar = (e) => {
    const { avatarUrl } = e.detail;
    if (avatarUrl) {
      stopActionSheet();
      if (isFunction(onChange)) {
        cropAvatar(avatarUrl).then((newAvatarUrl) => {
          setCurrentAvatarUrl(newAvatarUrl);
          onChange({ avatarUrl: newAvatarUrl });
        });
        return;
      }
      updateUserInfo({ avatarUrl });
    } else {
      Taro.kbToast({
        text: '获取不到头像信息',
      });
    }
  };

  useEffect(() => {
    if (avatar_url) {
      setCurrentAvatarUrl(avatar_url);
    }
  }, [avatar_url]);

  const handleClick = (e) => {
    e.stopPropagation();
    stopActionSheet();

    if (!canUseAvatarComponent) {
      // 点击头像按钮，兼容安卓，首次启动无法使用的问题
      chooseImageByActionSheet()
        .then((res) => {
          const { tempFilePaths } = res;
          handleChooseAvatar({
            detail: {
              avatarUrl: tempFilePaths[0],
            },
          });
        })
        .catch(() => { });
    }
  };

  useEffect(() => {
    updateStorage(1);
  }, []);

  return (
    <Button
      openType={canUseAvatarComponent ? 'chooseAvatar' : ''}
      onChooseavatar={handleChooseAvatar}
      className='avatar-switch__btn'
      hoverStopPropagation
      onClick={handleClick}
    >
      <KbAvatar src={currentAvatarUrl} size={size} />
      {/* {showIcon ? (
        <View className='avatar-switch__btn--icon'>
          <AtIcon prefixClass='kb-icon' value='camera' />
        </View>
      ) : null} */}
    </Button>
  );
};

export default AvatarSwitch;
