import Taro from '@tarojs/taro';
import request from '~base/utils/request';

/**
 *
 * @description 获取并切割头像
 * @param {string} src
 */
export function cropAvatar(src) {
  return new Promise((resolve) => {
    // 获取图片信息
    Taro.getImageInfo({
      src,
      success: (res) => {
        const { width, height, path } = res;
        if (width !== height) {
          // 非正方形，切割一下
          Taro.cropImage({
            src: path,
            cropScale: '1:1',
            success: ({ tempFilePath }) => resolve(tempFilePath),
            fail: () => resolve(src),
          });
          return;
        }
        resolve(src);
      },
      fail: () => resolve(src),
    });
  });
}

// 更新头像
const updateAvatar = (src) => {
  return new Promise((resolve) => {
    const { userInfo: { user_id } = {} } = Taro.kbLoginData || {};

    // 触发上传
    const run = (filePath) => {
      request({
        url: '/g_autovd/v2/Common/uploadAvatar',
        requestDataType: 'file',
        toastLoading: false,
        data: {
          name: 'file',
          filePath,
        },
        onThen: ({ code, data, msg }) => {
          const { url } = data || {};
          if (code == 0 && url) {
            resolve({ avatar_url: `${url}?ts=${new Date().getTime()}` });
            return;
          }
          resolve({ msg });
        },
      });
    };

    // 获取图片信息
    cropAvatar(src).then(run);
  });
};

// 更新昵称
const updateNickname = (text) => {
  return new Promise((resolve) => {
    request({
      url:
        process.env.MODE_ENV === 'wkd'
          ? '/v1/user/update'
          : '/api/weixin/mini/user/Config/updateUserNickName',
      toastLoading: false,
      data: {
        nickname: text,
      },
      onThen: ({ code, msg }) => {
        if (code == 0) {
          resolve({ nickname: text });
        } else {
          resolve({ msg });
        }
      },
    });
  });
};

export async function updateUserInfo(userInfo) {
  const { avatarUrl, nickname } = userInfo;

  const toastIns = Taro.kbToast({ status: 'loading' });

  const updatePromise = [];
  if (avatarUrl) {
    updatePromise.push(updateAvatar(avatarUrl));
  }
  if (nickname) {
    updatePromise.push(updateNickname(nickname));
  }

  // 触发更新
  const allRes = await Promise.all(updatePromise).then((res) => {
    const errMsg = [];
    const newUserInfo = res
      .filter(({ msg }) => {
        if (msg) {
          errMsg.push(msg);
        }
        return !msg;
      })
      .reduce((prev, curr) => ({ ...prev, ...curr }), {});

    Taro.kbUpdateUserInfo(newUserInfo);

    if (errMsg.length > 0) {
      toastIns.update({
        text: errMsg.join('、'),
      });
      return false;
    }

    toastIns.update({
      text: '个人信息更新成功',
    });
    return true;
  });

  return allRes;
}
