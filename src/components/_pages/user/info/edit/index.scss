.user-info-edit {
  padding: $spacing-h-xl2 $spacing-h-md;

  &__hidden {
    visibility: hidden;
  }

  &__title,
  &__input {
    padding-bottom: $spacing-h-md;
  }

  &__tips {
    padding-bottom: 64px;
  }

  &__title {
    padding-top: $spacing-v-md;
  }

  &__input {
    .at-input {
      margin-left: 0;
      padding: 15px 0;
      background-color: #f0f1f4;
      border-radius: $border-radius-base;

      &__title {
        width: auto;
        padding-left: $spacing-h-lg;
      }
    }
  }

  &__tips {
    color: #646566;
    font-size: $font-size-base;
  }
  &__footer {
    padding: 0 8px;
  }
}
