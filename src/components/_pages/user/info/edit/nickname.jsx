import { View } from "@tarojs/components";
import { AtInput } from "taro-ui";

const InfoEditNickname = (props) => {
    const { value, onChange } = props;

    return (
        <View>
            <View className="user-info-edit__title">
                联系人
            </View>
            <View className="user-info-edit__input">
                <AtInput border={false} clear value={value} type='nickname' onChange={onChange} cursorSpacing={150} maxLength={12} title='姓名' placeholder="请输入" cursor={-1} />
            </View>
            <View className="user-info-edit__tips">
                姓名限制1~12个字符以内，一个汉字为一个字符
            </View>
        </View>
    )
}

export default InfoEditNickname;