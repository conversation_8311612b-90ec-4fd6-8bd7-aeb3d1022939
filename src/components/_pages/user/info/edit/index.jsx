import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useImperativeHandle, useMemo, useRef, useState } from 'react';
import { AtButton, AtFloatLayout } from 'taro-ui';
import { useForm } from '~base/hooks/form';
import './index.scss';
import InfoEditPhone from './mobile';
import InfoEditNickname from './nickname';

const formConfigMap = {
  contact_name: {
    value: '',
  },
  contact_phone: {
    value: '',
    reg: 'phone',
  },
};

const InfoEdit = (props) => {
  const { actionRef } = props;
  const ref = useRef({ callbacksMap: {} });
  const [info, setInfo] = useState();
  const [isOpened, setIsOpened] = useState(false);

  const formConfig = useMemo(
    () => ({
      form: {
        [info?.key]: {
          ...formConfigMap[info?.key],
          value: info?.value,
        },
      },
      api: {
        url: '/g_autovd/v2/Common/updateContact',
      },
    }),
    [info?.key],
  );

  const {
    onFinish,
    data: { loading, data: formData, disabled = true },
    setFieldsValue,
  } = useForm(formConfig);

  // 关闭
  const handleClose = () => setIsOpened(false);

  // 输入
  const handleChange = (key, val) =>
    setFieldsValue({
      [key]: val,
    });

  // 提交
  const handleFinish = async () => {
    try {
      const res = await onFinish();
      const isSuccess = `${res.code}` === '0';
      if (isSuccess) {
        ref.current.callbacksMap.onSuccess?.();
        handleClose();
      }
    } catch (error) {}
  };

  useImperativeHandle(actionRef, () => ({
    on: (key, callback) => {
      ref.current.callbacksMap[key] = callback;
    },
    open: (item) => {
      setInfo(item);
      handleChange(item.key, item.value);
      setIsOpened(true);
    },
    close: handleClose,
  }));

  const contentCls = classNames('user-info-edit', {
    'user-info-edit__hidden': !isOpened,
  });

  return (
    <AtFloatLayout isOpened={isOpened} onClose={handleClose}>
      <View className={contentCls}>
        {info?.key === 'contact_name' ? (
          <InfoEditNickname
            onChange={(val) => handleChange(info?.key, val)}
            value={formData?.[info?.key]}
          />
        ) : info?.key === 'contact_phone' ? (
          <InfoEditPhone
            onChange={(val) => handleChange(info?.key, val)}
            value={formData?.[info?.key]}
          />
        ) : null}
        <View className='user-info-edit__footer'>
          <AtButton
            loading={loading}
            type='primary'
            disabled={disabled}
            circle
            onClick={handleFinish}
          >
            保存
          </AtButton>
        </View>
      </View>
    </AtFloatLayout>
  );
};

export default InfoEdit;
