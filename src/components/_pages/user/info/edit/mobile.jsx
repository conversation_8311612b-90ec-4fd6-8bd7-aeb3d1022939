import { View } from "@tarojs/components";
import { AtInput } from "taro-ui";

const InfoEditPhone = (props) => {
    const { value, onChange } = props;

    return (
        <View>
            <View className="user-info-edit__title">
                联系电话
            </View>
            <View className="user-info-edit__input">
                <AtInput border={false} clear value={value} onChange={onChange} cursorSpacing={150} maxLength={11} type='mobile' title='电话' placeholder="请输入" cursor={-1} />
            </View>
            <View className="user-info-edit__tips">
                电话仅输入数字，不能包含汉字，字母与特殊字符
            </View>
        </View>
    )
}

export default InfoEditPhone;