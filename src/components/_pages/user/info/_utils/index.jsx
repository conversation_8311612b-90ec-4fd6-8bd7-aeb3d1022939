import { View } from '@tarojs/components';
import { useMemo, useRef } from 'react';
import { getUserInfoDetail } from '~/services/user/info';
import { useRequest } from '~base/utils/request/hooks';

export function useCreateDescription() {
  const { data: info, run } = useRequest(getUserInfoDetail)
  const infoEditRef = useRef();

  // 服务地址
  const addressData = useMemo(() => {
    return {
      ...info
    }
  }, [info]);

  // 其他信息
  const items = useMemo(() => {
    if (process.env.MODE_ENV === 'client') {
      return [
        {
          label: '头像',
          type: 'avatar',
        },
        {
          key: 'contact_name',
          label: '昵称',
          value: info?.contact_name,
          placeholder: '请输入',
        },
        {
          key: 'contact_phone',
          label: '联系电话',
          value: info?.contact_phone,
          placeholder: '请输入',
        },
      ]
    } else {
      const labelsMap = {
        '1': ['企业名称', '社会统一信用代码'],
        '2': ['身份证姓名', '身份证号']
      }
      const [authNameLabel, authCodeLabel] = labelsMap[info?.auth_type || '1'] || [];
      return [
        {
          label: '头像',
          type: 'avatar',
        },
        {
          label: authNameLabel,
          value: info?.auth_name,
        },
        {
          label: authCodeLabel,
          value: info?.auth_code,
        },
        {
          key: 'contact_name',
          label: '联系人',
          value: info?.contact_name,
          placeholder: '请输入',
        },
        {
          key: 'contact_phone',
          label: '联系人电话',
          value: info?.contact_phone,
          placeholder: '请输入',
        },
      ];
    }
  }, [info]);

  const onOperator = (item) => {
    if (item.placeholder) {
      // 有placeholder认为可编辑；
      infoEditRef?.current?.on('onSuccess', (res) => {
        run();
      });
      infoEditRef?.current?.open(item);
    }
  };

  return {
    items,
    addressData,
    infoEditRef,
    onOperator,
  };
}
