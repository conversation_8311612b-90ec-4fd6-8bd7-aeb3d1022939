import { View } from '@tarojs/components';
import classNames from 'classnames';
import ServerAddress from '~/components/_pages/address/server';
import AvatarSwitch from '../../avatar/switch';
import InfoEdit from '../edit';
import { useCreateDescription } from '../_utils';
import './index.scss';

const UserInfoCard = () => {
  const { items, infoEditRef, onOperator, addressData } = useCreateDescription();

  return (
    <>
      <View className='kb-box__group user-info-card'>
        <View className='kb-box user-info-card__base'>
          {items.map((item) => (
            <View
              className={classNames('kb-navigator kb-navigator-xl', {
                'kb-navigator-noarrow': !item.placeholder,
              })}
              hoverClass={item.placeholder ? 'kb-hover' : 'none'}
              key={item.label}
              onClick={() => onOperator(item)}
            >
              <View className='kb-navigator__label'>{item.label}</View>
              {item.type === 'avatar' ? (
                <View className='kb-navigator__content'>
                  <AvatarSwitch size='small' />
                </View>
              ) : item.value ? (
                <View className='kb-navigator__content'>{item.value}</View>
              ) : item.placeholder ? (
                <View className='kb-navigator__placeholder'>{item.placeholder}</View>
              ) : null}
            </View>
          ))}
        </View>
        {process.env.MODE_ENV === 'server' && <ServerAddress data={addressData} />}
      </View>
      <InfoEdit actionRef={infoEditRef} />
    </>
  );
};

export default UserInfoCard;
