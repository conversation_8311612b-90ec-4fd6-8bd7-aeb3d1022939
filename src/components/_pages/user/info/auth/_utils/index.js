export const authTypes = [
  {
    key: '1',
    label: '企业身份',
    form: {
      id_name: '企业名称',
      id_code: '企业社会统一信用代码',
      id_code_type: 'text',
      maxLength: 18,
      id_code_config: {
        reg: 'creditCode',
        tag: '企业社会统一信用代码',
      },
    },
  },
  {
    key: '2',
    label: '个人身份',
    form: {
      id_name: '姓名',
      id_code: '身份证号码',
      id_code_type: 'idcard',
      maxLength: 18,
      id_code_config: {
        reg: 'cardId',
        tag: '身份证号码',
      },
    },
  },
];

export function getCurrentAuthType(type) {
  return authTypes.find((item) => item.key === type) || {};
}
