import isArray from 'lodash/isArray';
import { requestGet, requestPost } from '~base/utils/request/once';
import { serviceURL } from '../_utils';

export const contactsListApi = serviceURL('/StopContact/list');

// 获取列表
export function getContactsListForSelector(data) {
  const { page, pageSize: page_size, ...req } = data || {};

  return requestGet({
    url: contactsListApi,
    data: {
      ...req,
      page,
    },
  }).then(({ data: resData, ...rest }) => {
    let { list } = resData || {};
    if (isArray(list)) {
      list = list.map((item) => ({
        value: item.id,
        label: `${item.name} ${item.phone}`,
        info: item
      }));
    }
    return {
      ...rest,
      data: list,
    };
  });
}

// 删除联系人
export function removeContacts(data) {
  return requestPost({
    url: serviceURL('/StopContact/delete'),
    data,
  });
}
