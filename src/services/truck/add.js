import isArray from 'lodash/isArray';
import { truckFormSelectors } from '~/components/_pages/truck/list/item/add/_utils/form';
import {
  getTruckTypeKindLabel,
  getTruckTypeLabel,
  renderTruckload,
} from '~/components/_pages/truck/list/item/card/type/_utils';
import { requestGet, requestGetOnce } from '~base/utils/request/once';
import { isAvailableValue } from '~base/utils/utils';
import { serviceURL } from '../_utils';

// 获取该品牌下所有车辆配置列表
async function getModelsList(brand) {
  const list = await requestGetOnce({
    url: serviceURL('/Vehicle/getModelsList'),
    data: { brand: brand?.value },
  }).then(({ data: resData }) => (isArray(resData) ? resData : []));
  return list;
}

// 检查是否能获取下一列表项
function checkCanGetItems(type, data) {
  const value = data?.[type];
  const hasValue = !!value?.value;
  if (!hasValue) {
    return {
      code: 9001,
      msg: `请先选择${truckFormSelectors.find((item) => item.key === type)?.label}`,
    };
  }
}

// 忽略品牌，拉取平台所有支持的车辆类型：增加缓存
export function getTruckTypeListIgnoreBrand(data, force = false) {
  return requestGetOnce(
    { url: serviceURL('/Common/getVehicleType', true), data, mastHasMobile: false },
    force,
  ).then(({ data: resData }) =>
    isArray(resData) ? resData.map((item) => ({ value: item.type, label: item.name })) : [],
  );
}

// 忽略品牌，拉取平台所有支持的车辆类型：增加缓存
export function getCarTypeListIgnoreBrand(force = false) {
  return requestGetOnce(
    { url: serviceURL('/Common/getCarType', true), mastHasMobile: false },
    force,
  ).then(({ data: resData }) =>
    isArray(resData) ? resData.map((item) => ({ value: item.vehicle_type, label: item.name })) : [],
  );
}
export function getRentDayListIgnoreBrand() {
  return new Promise((resolve) => {
    resolve([
      {
        label: '100元以内',
        value: '0-100',
      },
      {
        label: '100~150元',
        value: '100-150',
      },
      {
        label: '150元以上',
        value: '150',
      },
    ]);
  });
}

// 忽略品牌，拉取平台所有支持的车辆类型对应的容积
export function getTruckVolumeListIgnoreBrand(data, force) {
  return requestGetOnce(
    { url: serviceURL('/Common/getLoadByType', true), data, mastHasMobile: false },
    force,
  ).then(({ data: resData }) =>
    isArray(resData) ? resData.map((item) => ({ value: item, label: renderTruckload(item) })) : [],
  );
}

// 获取下一个选项
async function getCurrentItems(currentType, preType, data) {
  const types = preType.split('.');
  const res = checkCanGetItems(types.slice(-1)[0], data);
  if (res) return res;
  const list = await getModelsList(data?.brand);

  // 车辆类型，需要远程获取类型对应关系
  let truckLabels = [];
  if (currentType === 'type') {
    truckLabels = await getTruckTypeListIgnoreBrand();
  }

  const items = [];
  list
    .filter((item) =>
      types.every((t) => {
        const curValue = data[t];
        return `${item[t]}`.split(',').includes(curValue?.value || curValue);
      }),
    )
    .forEach((item) => {
      const value = item[currentType];
      if (isAvailableValue(value) && value !== '') {
        const values = `${value}`.split(',');
        values.forEach((v) => {
          if (!items.some((item) => item.value === v)) {
            items.push({
              value: v,
              label:
                currentType === 'type'
                  ? getTruckTypeLabel(v, truckLabels)
                  : currentType === 'vehicle_type'
                  ? getTruckTypeKindLabel(v)
                  : v,
            });
          }
        });
      }
    });
  return items;
}

/**
 *
 * @description 车辆品牌列表
 * @returns
 */
export function getTruckBrandList() {
  return requestGetOnce({
    url: serviceURL('/Vehicle/getBrands'),
  }).then(({ data }) => (isArray(data) ? data.map((item) => ({ value: item, label: item })) : []));
}

/**
 *
 * @description 车辆类型列表 常规、冷链
 * @param {*} data
 * @returns
 */
export function getTruckKindList(data) {
  return getCurrentItems('vehicle_type', 'brand', data);
}

/**
 *
 * @description 型号列表
 * @param {*} data
 * @returns
 */
export function getTruckModeList(data) {
  return getCurrentItems('model', 'brand.vehicle_type', data);
}

/**
 *
 * @description 类型列表
 * @param {*} data
 * @returns
 */
export function getTruckTypeList(data) {
  return getCurrentItems('type', 'brand.vehicle_type.model', data);
}

/**
 *
 * @description 容积列表
 * @param {*} data
 * @returns
 */
export function getTruckVolumeList(data) {
  return getCurrentItems('truckload', 'brand.vehicle_type.model.type', data);
}

/**
 *
 * @description 颜色列表
 * @param {*} data
 * @returns
 */
export function getTruckColorList(data) {
  return getCurrentItems('color', 'brand.vehicle_type.model.type.truckload', data);
}

/**
 *
 * @description 获取组件列表
 */
export function getTruckRentList(data) {
  return getCurrentItems('rent', 'brand.vehicle_type.model.type.truckload', data);
}

/**
 *
 * @description 根据车牌号，三方拉取车辆信息
 */
export function getTruckInfoByVehicleNo(params) {
  return requestGet({
    url: serviceURL('/Vehicle/getVehicle'),
    data: params,
    toastError: true,
  }).then((res) => {
    const { data } = res;
    return {
      full_load_endurance: data?.full_load_endurance || '',
      max_speed: data?.max_speed || '',
      vehicle_size: data?.vehicle_size || '',
    };
  });
}
