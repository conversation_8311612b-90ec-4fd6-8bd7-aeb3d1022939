import isArray from 'lodash/isArray';
import { requestGet } from '~base/utils/request/once';
import { getPartnerStopPointsApi } from '../user/partner/stop-points';
import { serviceURL } from '../_utils';

const getOrderDepartPointApi = serviceURL('/Vehicle/getStopList'); // 所有停靠点
const getLineEditPointApi = serviceURL('/UserRoute/getStopList'); // 常用路线——停靠点列表

/**
 *
 * @description 获取常用路线——停靠点列表 - 按车主分类
 * @param {*} params
 * @returns
 */
export function getLineEditStopPoint(params) {
  const { company_name } = params;
  return requestGet({
    url: getLineEditPointApi,
    data: params,
  }).then((res) => {
    const { list } = res?.data || {};
    if (isArray(list)) {
      return {
        ...res,
        data: list
          .filter( // 只能选同一个企业的
            (item) =>
              !company_name || `${item.vehicle_owner_info?.company_name}` === `${company_name}`,
          )
          .map(({ stop_list = [], ...restItem }) => ({
            title: restItem.vehicle_owner_info?.company_name,
            children: stop_list.map((point) => ({
              value: point.stop_id,
              label: point.stop_name,
              info: {
                ...restItem,
                ...point,
              },
            })),
          })),
      };
    }
    return {
      ...res,
      data: [],
    };
  });
}

/**
 *
 * @description 获取发车点
 */
export function getOrderDepartPoint(data) {
  const { page, pageSize: page_size, vehicle_owner_id, ...req } = data || {};
  return requestGet({
    url: vehicle_owner_id ? getPartnerStopPointsApi : getOrderDepartPointApi,
    toastLoading: false,
    toastSuccess: false,
    toastError: false,
    data: {
      ...req,
      vehicle_owner_id,
      page,
    },
  }).then(({ data: resData, ...rest }) => {
    let { list = resData } = resData || {};
    if (isArray(list)) {
      list = list.map((item) => ({
        value: item.stop_id,
        label: item.stop_name,
        info: item,
      }));
    }
    return {
      ...rest,
      data: list,
    };
  });
}
