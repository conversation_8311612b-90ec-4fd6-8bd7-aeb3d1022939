import isArray from 'lodash/isArray';
import { requestPost } from '~base/utils/request/once';

export const truckDetailApi = '/g_lqm/api/driverlesscar/KdCar/detail';
const upAndDownApi = '/g_autovd/v2/Vehicle/Vehicle/opBizStatus';

function pickVehicleNo(data) {
  const { vehicle_no, vehicle_nos } = data || {};
  if (vehicle_no) return { vehicle_no };
  return {
    vehicle_no: isArray(vehicle_nos) ? vehicle_nos.join(',') : '',
  };
}

// 上架
export function upTruck(data) {
  return requestPost({
    url: upAndDownApi,
    data: { status: '1', ...pickVehicleNo(data) },
  });
}

// 下架
export function downTruck(data) {
  return requestPost({
    url: upAndDownApi,
    data: { status: '0', ...pickVehicleNo(data) },
  });
}

// 删除
export function removeTruck(data) {
  return requestPost({
    url: '/g_autovd/v2/Vehicle/Vehicle/del',
    data: pickVehicleNo(data),
  });
}
