import request from '~base/utils/request';

/**
 *
 * @description 下架
 */
export function truckDelisting(data) {
  return new Promise((resolve) => {
    request({
      url: '/',
      data,
      toastError: true,
      toastSuccess: true,
      quickTriggerThen: true,
      onThen: resolve,
    });
  });
}

/**
 *
 * @description 删除
 */
export function truckDelete(data) {
  return new Promise((resolve) => {
    request({
      url: '/',
      data,
      toastError: true,
      toastSuccess: true,
      quickTriggerThen: true,
      onThen: resolve,
    });
  });
}
