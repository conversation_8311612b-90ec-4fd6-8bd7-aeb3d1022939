import { requestGet, requestPost } from '~base/utils/request/once';
import { serviceURL } from '../_utils';

// 车辆调度 - 统计
export function getTruckDispatchStatistics(params) {
  return requestGet({
    url: serviceURL('/VehicleTask/overview'),
    data: params,
  });
}

// 开关机
export function truckOpenOrClose(params) {
  const { action, ...req } = params;
  const url = serviceURL(action === 'close' ? '/VehicleTask/command' : '/');
  return requestPost({
    url,
    data: req,
  });
}
