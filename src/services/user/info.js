import Taro from '@tarojs/taro';
import request from '~base/utils/request';

export const userInfoApi = '/g_autovd/v2/Auth/userInfo';

/**
 *
 * @description 获取详细信息
 */
export function getUserInfoDetail(opts = {}) {
  return new Promise((resolve) => {
    request({
      url: userInfoApi,
      toastLoading: false,
      ...opts,
      onThen: ({ data }) => {
        const {
          avatar_url,
          province,
          city,
          landmark,
          withdraw_money,
          is_auth,
          contact_name,
          contact_phone,
        } = data || {};
        // 更新最新的is_auth 信息
        Taro.kbUpdateUserInfo({
          is_auth,
          avatar_url,
          nickname: contact_name,
          contact_name,
          contact_phone,
          hasServiceAddress: !!(process.env.MODE_ENV == 'server' && province && city && landmark),
          withdraw_money,
        });
        resolve({ ...data });
      },
    });
  });
}
