/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '~base/utils/request';

/**
 *
 * @description 获取钱包数据
 * @returns
 */
// 静默登录
export const loginApi = '/g_autovd/v2/Auth/openReg';
// 登录
export const bindPhoneLoginApi = '/g_autovd/v2/Auth/login';
// 获取验证码
export const getSmsCode = (data) => {
  return new Promise((resolve, reject) => {
    request({
      toastLoading: true,
      toastError: true,
      mastLogin: false,
      mastHasMobile: false,
      url: '/g_autovd/v2/Auth/getCode',
      data,
      onThen: (res) => {
        const { code } = res || {};
        if (code == 0) {
          resolve();
        } else {
          reject();
        }
      },
    });
  });
};
