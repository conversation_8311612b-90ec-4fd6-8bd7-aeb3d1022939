import { serviceURL } from '~/services/_utils';
import { requestGet, requestPost } from '~base/utils/request/once';

export const getPartnerStopPointsApi = serviceURL('/BindStops/list');

// 获取停靠点列表
export function getPartnerStopPoints(params) {
  return requestGet({
    url: getPartnerStopPointsApi,
    data: params,
  });
}

// 分配停靠点
export function assignPartnerStopPoints(params) {
  return requestPost({
    url: serviceURL('/BindStops/add'),
    data: params,
  });
}

// 删除停靠点
export function deletePartnerStopPoints(params) {
  return requestPost({
    url: serviceURL('/BindStops/delete'),
    data: params,
  });
}
