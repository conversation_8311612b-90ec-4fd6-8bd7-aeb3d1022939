/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestGet, requestPost } from '~base/utils/request/once';

// 设置服务地址
export const setAddress = (data) => {
  return requestPost({
    url: '/g_autovd/v2/Vehicle/User/setSrvAddr',
    data,
  }).then((res) => res?.code == 0);
};

// 搜索地址
export const searchAddressApi = '/g_autovd/v2/Location/searchPoi';

// 支持的地址
export const supportAddressApi = '/g_autovd/v2/Location/support';

// 地址簿列表
export const addressListApi = '/g_autovd/v2/Cargo/User/addrList';

// 删除地址
export const deleteAddress = (data) => {
  return requestPost({
    url: '/g_autovd/v2/Cargo/User/delAddr',
    data,
  }).then((res) => res?.code == 0);
};

// 编辑地址
export const saveAddress = (data) => {
  return requestPost({
    url: '/g_autovd/v2/Cargo/User/saveAddr',
    data,
  }).then((res) => res?.code == 0);
};

export const getLocationByLat = (data) => {
  return requestPost({
    url: '/g_autovd/v2/Location/regeo',
    data,
    toastSuccess: false,
    toastError: false,
  }).then((res) => res?.data);
};

let supportLocationMap = null;
export const getSupportLocation = () => {
  return new Promise((resolve) => {
    if (supportLocationMap) {
      resolve(supportLocationMap);
      return;
    }
    requestGet({
      url: supportAddressApi,
    }).then((res) => {
      const data = Array.isArray(res?.data) ? res?.data : [];
      supportLocationMap = data;
      resolve(supportLocationMap);
    });
  });
};
