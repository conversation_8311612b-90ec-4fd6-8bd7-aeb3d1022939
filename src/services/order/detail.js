import isArray from 'lodash/isArray';
import { requestGet, requestPost } from '~base/utils/request/once';
import { serviceURL } from '../_utils';

// 删除订单
export function removeOrder(data) {
  return requestPost({
    url: serviceURL('/Order/delete'),
    data,
  });
}

// 获取订单车辆路线
export function getOrderVehiclePath(data) {
  return requestGet({
    url: serviceURL('/Order/vehiclePath'),
    data,
  }).then(({ data }) => (isArray(data) ? data : []));
}

// 订单-接单状态
export function getOrderWaitTakeStatus(data) {
  return requestGet({
    url: serviceURL('/Order/waitTakeStatus'),
    data,
  }).then(({ data }) => `${data?.waiting}` === '1');
}

// 发车
export function orderVehicleGo(data) {
  return requestGet({
    url: serviceURL('/Order/vehicleGo'),
    data,
  });
}

// 解锁车门
export function orderOpenDoor(data) {
  return requestGet({
    url: serviceURL('/Order/openDoor'),
    data,
  });
}

// 检查车辆是否发车
export function orderCheckRoutingStatus(data) {
  return requestGet({
    url: serviceURL('/Order/routingStatus'),
    data,
  }).then((res) => {
    // 等待规划。0:不用等待，1:需要等待
    const { waiting = '0' } = res.data || {};
    return `${waiting}` === '1';
  });
}

// 切换车辆停靠点
export function orderModifyTruckStopPoint(data) {
  return requestPost({
    url: serviceURL('/Order/setStop'),
    data,
  });
}
