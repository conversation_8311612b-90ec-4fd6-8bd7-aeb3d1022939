import omit from 'lodash/omit';
import pick from 'lodash/pick';
import request from '~base/utils/request';
import { serviceURL } from '../_utils';

export const orderListApi = serviceURL('/Order/getList');

// 处理货主和线路数据
function patchTruckOwnerAndRoutes({ truckOwner, routes }) {
  return {
    vehicle_owner_id: truckOwner.vehicle_owner_id,
    routes: routes.map((item) =>
      pick({ ...item, type: item.stop_type }, ['type', 'stop_id', 'contact_id']),
    ),
  };
}

// 货主 创建订单
export const createOrder = (params) => {
  return new Promise((resolve, reject) => {
    // amount 后台暂时不需要此字段了
    request({
      url: '/g_autovd/v2/Cargo/Order/create',
      data: {
        ...patchTruckOwnerAndRoutes(params),
        ...omit(params, ['truckOwner', 'amount', 'routes']),
      },
      toastError: true,
      onThen: (res) => {
        if (res?.code == 0) {
          resolve(res?.data);
        } else {
          reject();
        }
      },
    });
  });
};

// 报价
export const loadQuotation = (params) => {
  return new Promise((resolve, reject) => {
    request({
      url: '/g_autovd/v2/Cargo/Order/calcPrice',
      data: {
        ...patchTruckOwnerAndRoutes(params),
        ...omit(params, ['truckOwner', 'note', 'routes']),
      },
      toastError: true,
      onThen: (res) => {
        if (res?.code == 0) {
          resolve(res?.data);
        } else {
          reject(new Error(res?.msg));
        }
      },
    });
  });
};
