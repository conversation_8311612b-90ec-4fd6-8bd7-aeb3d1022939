/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { requestGet, requestPost } from '~base/utils/request/once';
import { serviceURL } from '../_utils';

export const rentalTruckListApi = '/g_autovd/v2/Cargo/LeaseOrder/getVehicleModels';

// 订单报价
export const getRentQuotation = (data) =>
  requestGet({
    url: '/g_autovd/v2/Cargo/LeaseOrder/calcPrice',
    data,
  }).then((res) => res.data || {});

// 创建订单
export const createRentOrder = (data) =>
  requestPost({
    url: '/g_autovd/v2/Cargo/LeaseOrder/create',
    data,
    toastSuccess: false,
  }).then((res) => res.data || {});

// 支付订单
export const handlePayRentOrder = (data) =>
  requestPost({
    url: '/g_autovd/v2/Cargo/LeaseOrder/payment',
    data,
    toastSuccess: false,
  }).then((res) => res?.data);

// 确认收车
export const handleReceiveRent = (data) =>
  requestPost({
    url: '/g_autovd/v2/Cargo/LeaseOrder/receivedVehicle',
    data,
  }).then((res) => res.code == 0);

export const handleDepartRent = (data) =>
  requestPost({
    url: '/g_autovd/v2/Vehicle/LeaseOrder/depart',
    data,
  }).then((res) => res.code == 0);

// 取消订单
export const cancelRentOrder = (data) =>
  requestPost({
    url: '/g_autovd/v2/Cargo/LeaseOrder/cancelLease',
    data,
  }).then((res) => res.code == 0);

// 可用租车列表
export const getMatchRentOrderTruckListApi = '/g_autovd/v2/Vehicle/LeaseOrder/matchVehicleList';

export const rentOrderDetailApi = serviceURL('/LeaseOrder/detail');

// 车主-申请结算
export const rentOrderSettle = (data) =>
  requestPost({
    url: serviceURL('/LeaseOrder/settlement'),
    data,
  }).then((res) => res.code == 0);

// 确认收车
export const rentOrderReceived = (data) =>
  requestPost({
    url: serviceURL('/LeaseOrder/confirmReceipt'),
    data,
  }).then((res) => res.code == 0);
