import Taro, { useEffect, useState } from '@tarojs/taro';
import dayjs from 'dayjs';
import isFunction from 'lodash/isFunction';

export const now = () => {
  return new Date().getTime();
};

/**
 *
 * @param {string} expires
 * @description 获取缓存，检查是否过期
 */
function checkExpires(data) {
  const { expires } = data || {};
  if (expires) {
    // 存储时有过期配置：如已经过期，则移除缓存，并清除data
    if (dayjs(expires).isBefore(dayjs())) {
      return {
        errMsg: `getStorage:fail data is expires ${expires}`,
      };
    }
  }
  return null;
}

/**
 *
 * @description 设置时补充数据
 */
function patchStorageData({ data, expires }) {
  const mergeData = {
    data,
    ts: now(),
  };
  if (expires) {
    mergeData.expires = expires;
  }
  return mergeData;
}

/**
 *
 * @description 异步设置缓存
 * @param {{
 *    key:string;
 *    data:any;
 *    expires?:string;
 *    success?:()=>void;
 *    fail?:()=>void;
 *    complete?:()=>void;
 * }} params
 * @returns
 */
export const setStorage = (params) => {
  const { key, data, expires, ...rest } = params;
  return Taro.setStorage({
    key,
    data: patchStorageData({ data, expires }),
    ...rest,
  });
};

/**
 * @description 异步获取缓存
 * @param {{
 *    key:string;
 *    success?:(res:{data?:{expires?:string;ts:number;data?:any;};errMsg:string;})=>void;
 *    fail?:()=>void;
 *    complete?:(res:{data?:{expires?:string;ts:number;data?:any;};errMsg:string;})=>void;
 * }} params
 */
export const getStorage = async (params) => {
  try {
    const res = await Taro.getStorage(params);
    return checkExpires(res.data) || res;
  } catch (error) {
    return error;
  }
};

// 判断是否设置暂存时间，如果暂存时间触达就判断为空缓存
export const getStorageSpell = (params) => {
  const { key } = params;
  return new Promise((resolve, reject) => {
    getStorage({ key })
      .then((res) => {
        const { data, ts } = res.data;
        if (data) {
          const { day, ms } = data;
          if (day) {
            if (dayjs().diff(dayjs(ts), 'day') >= day) {
              reject();
            } else {
              resolve(data);
            }
          } else if (ms) {
            if (dayjs().diff(dayjs(ts)) >= ms) {
              reject();
            } else {
              resolve(data);
            }
          } else {
            resolve(data);
          }
        } else {
          reject();
        }
      })
      .catch(() => {
        reject();
      });
  });
};

// 异步移除缓存
export const removeStorage = Taro.removeStorage;

/**
 *
 * @description 同步设置缓存
 * @param {string} key
 * @param {any} data
 * @param {string} expires
 * @returns
 */
export const setStorageSync = (key, data, expires) =>
  Taro.setStorageSync(key, patchStorageData({ data, expires }));

/**
 * @description 获取缓存
 * @param {string} key
 */
export const getStorageSync = (key) => {
  try {
    const res = Taro.getStorageSync(key);
    return checkExpires(res) ? '' : res;
  } catch (error) {
    return error;
  }
};

// 移除缓存
export const removeStorageSync = Taro.removeStorageSync;

/**
 *
 * @description 缓存处理
 * dynamicUpdate 动态更新：即调用update后，立即更新data
 * @param {string} key
 */
export function useStorage(key, dynamicUpdate = false) {
  const [data, setData] = useState();

  const getData = async () => {
    const res = await getStorage({ key });
    // null 和 undefined 可用于区分没有取到storage，还是初始状态；
    const { data: resData = null } = res || {};
    return resData;
  };

  useEffect(() => {
    if (key) {
      getData().then(setData);
    }
  }, [key]);

  const update = async (data, expires) => {
    if (key) {
      if (isFunction(data)) {
        const pre = await getStorage({ key });
        const newData = data(pre?.data);
        await update(newData, expires);
      } else {
        await setStorage({
          key,
          data,
          expires,
        });
      }

      if (dynamicUpdate) {
        getData().then(setData);
      }
    }
  };

  const remove = async () => {
    if (key) {
      await removeStorage({ key });
      if (dynamicUpdate) {
        getData().then(setData);
      }
    }
  };

  return {
    data,
    getData,
    update,
    remove,
  };
}
