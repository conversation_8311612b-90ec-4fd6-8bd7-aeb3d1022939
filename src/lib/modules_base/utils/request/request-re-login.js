/**
 *
 * @description 锁定重登陆回调：避免已经触发重新登录，但仍有登录时效的回调被触发；
 */

import Taro from '@tarojs/taro';
import { loginCallbacksCollect } from './pool';

class RequestReLogin {
  constructor() {
    this.locked = false; // 锁定：重登成功前，无法再请求需要登录状态的接口
    this.reLoginCount = 0;
    this.reLoginMax = 5; // 最大重试次数
  }

  // 登录失效时，收集登录回调
  collectCallbacks(params, page) {
    return loginCallbacksCollect(params, page);
  }

  // 锁定回调
  lock() {
    this.locked = true;
  }

  // 解锁回调
  unlock(force = false) {
    // 暂定3分钟后解锁，避免并发请求异常问题；
    // 允许强制解锁
    if (force) {
      this.locked = false;
    }
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      this.locked = false;
    }, 3 * 60 * 1000);
  }

  // 检查是否锁定
  checkCanGo() {
    return !this.locked && this.reLoginCount <= this.reLoginMax;
  }

  // 重登陆
  reLogin(res, params, page) {
    const isReLoginCode = ['1011', '1010'].includes(`${res?.code}`);
    if (isReLoginCode) {
      // 需要重登

      if (this.collectCallbacks(params, page)) {
        // 登录回调已收集，直接返回
        return true;
      }

      // 尝试重登
      if (this.checkCanGo()) {
        this.lock();
        Taro.kbLogin(true).then(() => {
          this.unlock();
          this.reLoginCount++;
        });
      }
    }
    return false;
  }
}

const RequestReLoginManager = new RequestReLogin();

export default RequestReLoginManager;
