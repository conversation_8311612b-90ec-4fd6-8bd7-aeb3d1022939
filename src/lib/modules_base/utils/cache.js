import merge from 'lodash/merge';
import { now, randomCode } from './utils';

class CacheMap {
  /**
   *
   * @param {{
   *   key:string
   * }} options
   */
  constructor(options) {
    this.options = options;
    this.map = new Map();
  }

  //  key
  _key() {
    const { _keyCache, options } = this;
    if (_keyCache) return _keyCache;
    const { key = randomCode() } = options || {};
    this._keyCache = key;
    return key;
  }

  // 设置缓存
  set(data) {
    const key = this._key();
    this.map.set(key, {
      data,
      ts: now(),
    });
  }

  // 合并更新
  merge(data) {
    const preData = this.get();
    this.set(merge(preData?.data, data));
  }

  // 获取缓存
  get() {
    const key = this._key();
    return this.map.get(key);
  }

  // 是否存在
  has() {
    const key = this._key();
    return this.map.has(key);
  }

  // 移除缓存
  delete() {
    if (this.has()) {
      const key = this._key();
      this.map.delete(key);
    }
  }

  // 清理缓存
  clear() {
    this.map.clear();
  }
}

export default CacheMap;
