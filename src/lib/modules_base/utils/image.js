import Taro from '@tarojs/taro';

// 选择图片
async function chooseImage(opts) {
  let res = {
    tempFiles: [],
  };
  try {
    if (Taro.chooseMedia) {
      res = await Taro.chooseMedia({
        mediaType: ['image'],
        ...opts,
      });
    } else {
      res = await Taro.chooseImage(opts).then(
        ({
          apFilePaths,
          tempFilePaths = apFilePaths,
          tempFiles = tempFilePaths.map((item) => ({ path: item, size: 0 })),
        }) => ({
          tempFiles: tempFiles.map(({ path, ...restItem }) => ({
            ...restItem,
            tempFilePath: path,
          })),
        }),
      );
    }
  } catch (error) {
    throw error;
  }
  return res;
}

// 计算压缩质量
function countQuality({ size, maxSize }) {
  if (!size || size === 0) return 100; // 防止除以0
  const ratio = maxSize / size;
  const quality = Math.min(Math.max(Math.round(ratio * 100), 1), 100);
  return quality;
}

// 压缩图片
async function compressImage(src, opts) {
  const { maxSize, compressedWidth, compressedHeight } = opts;
  try {
    if (maxSize || compressedWidth || compressedHeight) {
      const compressOpts = {
        compressedWidth,
        compressedHeight,
      };
      if (maxSize) {
        const { size } = await Taro.getFileInfo({ filePath: src });
        // 质量压缩
        if (size > maxSize) {
          const quality = countQuality({ maxSize, size });
          if (quality) {
            compressOpts.quality = quality;
          }
        }
      }

      const res = await Taro.compressImage(compressOpts);

      return res;
    }
  } catch (error) {}

  return {
    tempFilePath: src,
  };
}

// 剪裁图片
async function cropImage(src, cropScale) {
  try {
    if (cropScale) {
      const res = await Taro.cropImage({ src, cropScale });
      return res;
    }
  } catch (error) {}

  return {
    tempFilePath: src,
  };
}

// 图片选择
export function chooseImageByActionSheet(opts, page) {
  return new Promise((resolve, reject) => {
    const sourceTypes = ['camera', 'album'];
    const {
      cropScale,
      count = 1,
      maxSize, // 最大尺寸，判断是否需要压缩
      compressedWidth,
      compressedHeight,
      ...restOpts
    } = opts || {};
    Taro.kbActionSheet(
      {
        items: ['拍照', '从相册选取'],
        onClick: async (index) => {
          try {
            const { tempFiles } = await chooseImage({
              count,
              sourceType: [sourceTypes[index]],
            });
            const tempFilePaths = tempFiles.map((item) => item.tempFilePath);

            if (count === 1) {
              let newFilePath = tempFilePaths[0];

              // 暂只支持，单个图片的压缩与剪裁

              // 先按照 compressedWidth或compressedHeight压缩一次
              const compress1Res = await compressImage(newFilePath, {
                compressedWidth,
                compressedHeight,
              });
              newFilePath = compress1Res.tempFilePath;

              // 先剪裁
              const cropRes = await cropImage(newFilePath, cropScale);
              newFilePath = cropRes.tempFilePath;

              // 后压缩
              const compress2Res = await compressImage(newFilePath, {
                maxSize,
              });
              newFilePath = compress2Res.tempFilePath;

              tempFilePaths[0] = newFilePath;
              tempFiles[0].tempFilePath = newFilePath;
            }

            resolve({
              tempFilePaths,
              tempFiles,
            });
          } catch (error) {
            reject(error);
          }
        },
      },
      page,
    );
  });
}
