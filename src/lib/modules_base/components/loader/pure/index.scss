.loader-pure {
    width: 60px;
    height: 60px;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    color: #000;

    &__small {
        width: 35px;
        height: 35px;
    }

    &__large {
        width: 85px;
        height: 85px;
    }

    &__item {
        position: absolute;
        top: 14%;
        left: 45%;
        width: 7%;
        height: 22%;
        background-color: currentColor;
        opacity: 0.2;
        border-radius: 20px;
        transform-origin: 50% calc(190%);
        /* 控制点在圆周边缘，偏离中心 */
        animation: spinner-fade 1.2s linear infinite;
    }

    /* 动态旋转 + 延迟 */
    &__item-0 {
        transform: rotate(0deg);
        animation-delay: -1.1s;
    }

    &__item-1 {
        transform: rotate(30deg);
        animation-delay: -1.0s;
    }

    &__item-2 {
        transform: rotate(60deg);
        animation-delay: -0.9s;
    }

    &__item-3 {
        transform: rotate(90deg);
        animation-delay: -0.8s;
    }

    &__item-4 {
        transform: rotate(120deg);
        animation-delay: -0.7s;
    }

    &__item-5 {
        transform: rotate(150deg);
        animation-delay: -0.6s;
    }

    &__item-6 {
        transform: rotate(180deg);
        animation-delay: -0.5s;
    }

    &__item-7 {
        transform: rotate(210deg);
        animation-delay: -0.4s;
    }

    &__item-8 {
        transform: rotate(240deg);
        animation-delay: -0.3s;
    }

    &__item-9 {
        transform: rotate(270deg);
        animation-delay: -0.2s;
    }

    &__item-10 {
        transform: rotate(300deg);
        animation-delay: -0.1s;
    }

    &__item-11 {
        transform: rotate(330deg);
        animation-delay: 0s;
    }
}


@keyframes spinner-fade {

    0%,
    39%,
    100% {
        opacity: 0.2;
    }

    40% {
        opacity: 1;
    }
}