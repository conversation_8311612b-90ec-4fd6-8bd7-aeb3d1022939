import { View } from "@tarojs/components";
import classNames from "classnames";
import './index.scss';

const loaderItems = new Array(12).fill(0).map((_, i) => `${i}`);
const cls = 'loader-pure';

/**
 * 
 * @param {{size?:'normal'|'small'|'large'}} props 
 * @returns 
 */
const LoaderPure = (props) => {
    const { size } = props;
    const rootCls = classNames(cls, {
        [`${cls}__${size}`]: !!size
    });

    return (
        <View className={rootCls}>
            {
                loaderItems.map((item) => <View key={item} className={classNames(`${cls}__item`, `${cls}__item-${item}`)} />)
            }
        </View>
    )
}

export default LoaderPure;