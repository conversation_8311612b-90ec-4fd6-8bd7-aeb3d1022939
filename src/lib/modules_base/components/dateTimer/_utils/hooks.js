import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { useEffect, useMemo, useRef, useState } from 'react';
import { secondsFormatter } from '.';
// 补零工具函数
const padZero = (n) => n.toString().padStart(2, '0');

dayjs.extend(duration);

/**
 *
 * @param {{
 *   onStart?: ()=>void;
 *   onStop?: ()=>void;
 *   formatter?: string;
 *   start?: number;
 *   interval?: number;
 *   type?: 'normal'|'fixed'|'countdown'; // normal 正计时、fixed 固定时间、countdown 倒计时
 * }} props
 * @returns
 */
export function useDateTimer(props) {
  const {
    type = 'normal',
    start = 0,
    formatter = secondsFormatter,
    interval = 1000,
    onStart,
    onStop,
  } = props;
  const [timer, setTimer] = useState(start);
  const ref = useRef({ timer: 0 });

  const stop = () => {
    clearInterval(ref.current.timer);
  };

  useEffect(() => {
    stop();
    if (type === 'fixed') return; // 固定时间
    onStart?.();
    const intervalUnit = interval / 1000;
    ref.current.timer = setInterval(() => {
      setTimer((pre) =>
        type === 'countdown' ? Math.max(pre - intervalUnit, 0) : pre + intervalUnit,
      );
    }, interval);

    return () => {
      stop();
    };
  }, []);

  const timerFormatted = useMemo(() => {
    const d = dayjs.duration(timer, 'seconds');
    const HH = Math.floor(d.asHours()); // 总小时数
    const mm = d.minutes(); // 分钟数（不含小时部分）
    const ss = d.seconds(); // 秒数（不含分钟部分）
    const map = { HH, mm, ss };
    const template = HH > 0 ? formatter : formatter.replace(/^HH.+mm/, 'mm');
    return template.replace(/HH|mm|ss/g, (match) => padZero(map[match] || '0'));
  }, [timer, formatter]);

  useEffect(() => {
    if (timer === 0) {
      onStop?.();
      stop();
    }
  }, [timer]);

  return {
    timer,
    timerFormatted,
  };
}
