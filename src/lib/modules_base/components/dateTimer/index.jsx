import { useEffect } from "react";
import { useDateTimer } from "./_utils/hooks";

/**
 * 
 * @param {{
 *   formatter?: string;
 *   start?: number;
 *   interval?: number;
 *   type?: 'normal'|'fixed'|'countdown'; // normal 正计时、fixed 固定时间、countdown 倒计时
 * }} props 
 * @returns 
 */
const DateTimer = (props) => {
    const { timerFormatted } = useDateTimer(props);

    return (
        <>
            {timerFormatted}
        </>
    )
}

export default DateTimer;