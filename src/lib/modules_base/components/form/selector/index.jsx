import { View } from "@tarojs/components"
import Taro from "@tarojs/taro";
import classNames from "classnames";
import isBoolean from "lodash/isBoolean";
import React, { useEffect, useRef } from "react";
import { AtIcon } from "taro-ui";

/**
 * 
 * @param {{
 *   value?:any;
 *   onChange?:(v:any)=>void|boolean;
 *   label:string;
 *   request:()=>Promise<{value:string|number;label:string}[]>
 *   actionSheetProps?:{items:(({label:string;value?:string;})|string)[];enableMore?:boolean;}
 *   labelInValue?:boolean;
 *   labelRender?:(val:any)=>React.ReactNode;
 *   placeholder?:string;
 *   className?: string;
 *   readOnly?: boolean;
 *   disabled?: boolean;
 *   hoverClass?: 'kb-hover'|'kb-hover-opacity';
 * }} props 
 * @returns 
 */
const FormSelectorField = (props) => {
    const { children, disabled, readOnly, className, labelInValue = true, hoverClass = 'kb-hover', labelRender, value: valueProps, label, placeholder = `请选择${label}`, actionSheetProps, active, onChange, request } = props;
    const actionSheetRef = useRef({});
    const clickable = !readOnly && !disabled;

    const handleClick = () => {
        if (!clickable) return;
        actionSheetRef.current = Taro.kbActionSheet({
            ...actionSheetProps,
            value: valueProps,
            active,
            request,
            onClick: async (_, item) => {
                const isSuccess = await onChange?.(labelInValue ? item : (item.value || item.label || item.text || item));
                if (isBoolean(isSuccess)) {
                    return !isSuccess;
                }
            }
        });
    }

    const { value = valueProps, label: contentLabel = value } = valueProps || {};
    const cls = classNames('kb-form__item', className, {
        ['kb-form__item--disabled']: !!disabled
    });

    const isValidChildren = React.isValidElement(children);

    if (isValidChildren) {
        return React.cloneElement(children, {
            onClick: (e) => handleClick(e),
        })
    }

    useEffect(() => {
        actionSheetRef.current?.update?.({ active });
    }, [active]);

    return (
        <View className={cls} hoverClass={clickable ? hoverClass : 'none'} onClick={handleClick}>
            {label && <View className='item-title'>{label}</View>}
            {
                contentLabel
                    ? (
                        <View className='item-content'>
                            {labelRender ? labelRender(valueProps) : contentLabel}
                        </View>
                    )
                    : <View className='item-placeholder'>
                        {placeholder}
                    </View>
            }
            <View className="item-icon">
                <AtIcon prefixClass="kb-icon" value='arrow' />
            </View>
        </View>
    )
}

export default FormSelectorField;