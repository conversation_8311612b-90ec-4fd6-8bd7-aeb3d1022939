import isFunction from 'lodash/isFunction';
import { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { save } from '~base/actions/global';
import { createFormWithState } from '~base/hooks/form';

const formInsMap = {}; // 表单对象
const formSuccessCallbacksMap = {}; // 表单成功回调
const formDataCacheMap = {}; // 表单数据缓存

// 创建表单
function createForm(name, cfg, setState) {
  if (!cfg?.form) return;
  formInsMap[name] = createFormWithState(
    {
      ...cfg,
      name,
    },
    (cur) => {
      setState({ form: isFunction(cur) ? cur() : cur });
    },
  );
}

// 清理回调
function cleanCallbacks(name) {
  formSuccessCallbacksMap[name] = null;
}

// 设置回调
function setCallbacks(name, callback) {
  if (isFunction(callback)) {
    formSuccessCallbacksMap[name] = callback;
  }
}

// 执行回调
function runCallbacks(name) {
  formSuccessCallbacksMap[name]?.();
}

// 执行表单对象方法
function funFormMethod(name, method, ...arg) {
  return formInsMap[name]?.[method]?.(...arg);
}

/**
 *
 * @param {string} name
 * @param {{
 *    form?:{[key:string]:{reg?:string;value?:any;clean?:boolean;tag?:string;required?:boolean;validator?:(value:any, data:any, currentRule:any)=>Promise<boolean>;}};
 *    api?:{
 *      url:string;
 *      toastLoading?:boolean;
 *      toastSuccess?:boolean;
 *      toastError?:boolean;
 *    },
 *    onSuccess?:()=>void;
 * }} opts
 * @returns {{
 * open:boolean;
 * form: {
 *  data:Record<string,any>;
 *  loading:boolean;
 *  disabled:boolean;
 *  onFinish:()=>Promise<any>;
 *  setFieldsValue:(d:Record<string,any>)=>void;
 * };
 * setSuccessCallback?:(c:()=>void)=>void;
 * onOpenChange?:(o:boolean)=>void;
 * }}
 */
export function useActionContext(name, opts) {
  const dispatch = useDispatch();
  const { actionCtxDataMap } = useSelector((state) => state.global);

  const { open, form: formData } = useMemo(() => {
    return {
      open: actionCtxDataMap?.[name]?.open || false,
      form: actionCtxDataMap?.[name]?.form || { disabled: true, loading: false, data: {} },
    };
  }, [actionCtxDataMap?.[name], name]);

  // 清理
  const clean = () => {
    if (!opts?.form) return;
    // 清理数据
    triggerSetDataMap(null);
    // 清理表单对象
    formInsMap[name] = null;
    // 清理表单成功回调
    cleanCallbacks();
  };

  const triggerSetDataMap = (cur) => {
    formDataCacheMap[name] = !cur
      ? null
      : {
          ...formDataCacheMap[name],
          ...cur,
        };

    dispatch(
      save({
        actionCtxDataMap: { ...formDataCacheMap },
      }),
    );
  };

  useEffect(() => {
    const { onSuccess, ...formCfg } = opts || {};
    setCallbacks(name, onSuccess);
    createForm(name, formCfg, triggerSetDataMap);

    return clean;
  }, []);

  const onOpenChange = (o) => triggerSetDataMap({ open: o });

  // 设置回调
  const setSuccessCallback = (callback) => setCallbacks(name, callback);

  // 提交
  const onFinish = async (req) => {
    try {
      const res = await funFormMethod(name, 'submit', {
        ...formData?.data,
        ...req,
      });
      if (`${res?.code}` === '0') {
        onOpenChange(false);
        runCallbacks(name);
      }
      return res;
    } catch (error) {}
  };

  // 设置表单数据
  const setFieldsValue = (data) => funFormMethod(name, 'update', data);

  const form = useMemo(
    () => ({
      ...formData,
      onFinish,
      setFieldsValue,
    }),
    [formData],
  );

  return {
    open,
    form,
    setSuccessCallback,
    onOpenChange,
  };
}
