import { View } from "@tarojs/components";
import { useMemo } from "react";
import classNames from 'classnames';
import { randomCode } from "~base/utils/utils";
import './index.scss';

/**
 * 
 * @param {{
 *   items:{key:string;label?:string|React.ReactNode;content?:string|React.ReactNode;className?:string}[];
 *   className?:string;
 *   centered?:boolean;
 *   size?:'small'|'large'|'normal';
 *   labelWidthAuto?:boolean;
 *   lineColorPure?:boolean;
 * }} props 
 * @returns 
 */
const TimeLineExtend = (props) => {
    const { items, className, centered = true, size, labelWidthAuto, lineColorPure = false } = props;
    const itemsLength = items?.length;
    const hasItems = itemsLength > 0;
    const itemsPatch = useMemo(() => {
        if (!hasItems) return [];
        return items?.map(item => ({
            key: randomCode(),
            ...item
        }));
    }, [items]);

    const rootCls = classNames('time-line', className, {
        'time-line-one': itemsLength === 1,
        'time-line-align-center': centered,
        'time-line-color-pure': lineColorPure,
        [`time-line__${size}`]: !!size
    });

    const labelCls = classNames("time-line__item-label", {
        "time-line__item-label-auto": labelWidthAuto
    });

    return (
        hasItems ? (
            <View className={rootCls}>
                {
                    itemsPatch.map((item) => (
                        <View key={item.key} className={classNames("time-line__item", item.className, {
                            "time-line__item-current": item.isCurrent,
                            "time-line__item-active": item.isActive
                        })}>
                            <View className={labelCls}>{item.label}</View>
                            <View className="time-line__item-trail-wrap">
                                <View className="time-line__item-trail" />
                            </View>
                            <View className="time-line__item-content">{item.content}</View>
                        </View>
                    ))
                }
            </View>
        )
            : null
    )
}

export default TimeLineExtend;