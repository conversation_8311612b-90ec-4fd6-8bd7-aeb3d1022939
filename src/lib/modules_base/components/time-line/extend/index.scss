$time-lint-trail-size: 16px;
$time-lint-trail-size-max: 2 * $time-lint-trail-size;
$time-lint-trail-color: #C8C9CC;
$time-lint-trail-active-color: $color-brand;
$time-lint-trail-top: 35px;
$time-lint-trail-top-max: $time-lint-trail-top - 0.5 * $time-lint-trail-size;
$time-line-trail-wrap-top: $spacing-h-md + $time-lint-trail-size-max * 0.5;
$time-line-trail-wrap-bottom: -1 * $time-line-trail-wrap-top;

@keyframes pulse {
    0% {
        transform: scale(0.6);
    }

    50% {
        transform: scale(1);
    }

    100% {
        transform: scale(0.6);
    }
}

.time {
    &-line {
        &__item {
            display: flex;
            justify-content: stretch;
            color: #969799;

            &-label,
            &-content {
                padding: $spacing-v-md 0;
                display: flex;
                align-items: baseline;
            }

            &-label {
                width: 185px;
                text-align: right;
                flex-shrink: 0;

                &-auto {
                    width: auto;
                }
            }

            &-trail {
                width: $width-base;
                position: absolute;
                top: 0;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                z-index: 2;

                &-wrap {
                    position: relative;
                    padding: 0 $spacing-h-md;
                    color: $time-lint-trail-color;

                    &::after {
                        content: '';
                        position: absolute;
                        top: $time-line-trail-wrap-top;
                        bottom: $time-line-trail-wrap-bottom;
                        left: 50%;
                        width: $width-base;
                        background-color: currentColor;
                        transform: translateX(-50%);
                        z-index: 1;
                    }
                }

                &::before,
                &::after {
                    content: '';
                    position: absolute;
                    left: 50%;
                    margin-left: -0.5 * $time-lint-trail-size;
                    border-radius: $border-radius-circle;
                    display: block;
                    background-color: currentColor;
                    top: $time-lint-trail-top;
                }

                &::before {
                    width: $time-lint-trail-size-max;
                    height: $time-lint-trail-size-max;
                    margin-left: -0.5 * $time-lint-trail-size-max;
                    opacity: 0.2;
                    z-index: 1;
                    display: none;
                    top: $time-lint-trail-top-max;
                }

                &::after {
                    width: $time-lint-trail-size;
                    height: $time-lint-trail-size;
                    z-index: 2;
                }
            }

            &-current {
                color: $color-black-1;
            }

            &-current,
            &-active {
                .time-line__item-trail {
                    &::before {
                        display: block;
                        animation: pulse 1s infinite ease-in-out;
                        transform-origin: center;
                    }
                }
            }

            &-one &-trail {
                background-color: transparent;
            }

            &-current {
                .time-line__item-trail-wrap {
                    color: $time-lint-trail-active-color;
                }
            }

            &:first-child .time-line__item-trail-wrap::after {
                top: $time-lint-trail-top;
            }

            &:last-child .time-line__item-trail-wrap::after {
                bottom: calc(100% - $time-lint-trail-top);
            }
        }


        &-one {
            .time-line__item-trail-wrap::after {
                background-color: transparent;
            }
        }

        &-align-center &__item {

            &-label,
            &-content {
                align-items: center;
            }

            &-trail {
                &-wrap {
                    &::after {
                        top: 0;
                        bottom: 0;
                    }
                }

                &::before,
                &::after {
                    top: 50%;
                    margin-top: -0.5 * $time-lint-trail-size;
                }

                &::before {
                    margin-top: -0.5 * $time-lint-trail-size-max;
                }
            }

            &:first-child .time-line__item-trail-wrap::after {
                top: 50%;
            }

            &:last-child .time-line__item-trail-wrap::after {
                bottom: 50%;
            }
        }

        &-color-pure &__item-trail-wrap {
            &::after {
                background-color: $time-lint-trail-color;
            }

        }
    }
}