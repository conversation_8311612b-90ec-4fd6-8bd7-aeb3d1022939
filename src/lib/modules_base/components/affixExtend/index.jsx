import { RootPortal, View } from "@tarojs/components"
import classNames from "classnames";
import { useMemo } from "react";
import isUndefined from "lodash/isUndefined";
import './index.scss';

/**
 * 
 * @param {{
 *    height?:string|number;
 *    offsetTop?:string|number;
 *    offsetBottom?:string|number;
 *    className?:string;
 *    zIndex?:number;
 * }} props 
 * @returns 
 */
const AffixExtend = (props) => {
    const { height, children, className, offsetTop, offsetBottom = !isUndefined(offsetTop) ? void (0) : 0, zIndex } = props;
    const cls = classNames('kb-affix-extend', className);
    const wrapperStyle = useMemo(() => ({ height }), [height]);
    const contentStyle = useMemo(() => ({
        top: offsetTop,
        bottom: offsetBottom,
        zIndex
    }), [offsetTop, offsetBottom]);

    return (
        <View className={cls} style={wrapperStyle}>
            <RootPortal>
                <View className="kb-affix-extend__content" style={contentStyle}>
                    {children}
                </View>
            </RootPortal>
        </View>
    )
}

export default AffixExtend;