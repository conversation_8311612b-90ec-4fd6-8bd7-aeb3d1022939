/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useState } from 'react';
import { AtInput } from 'taro-ui';
import './index.scss';

const SmsInput = (props) => {
  const { length = 4, type = 'line', onChange } = props;
  const [value, setValue] = useState('');
  const [focus, setFocus] = useState(false);

  const handleChange = (v) => {
    setValue(v);
    onChange?.(v);
  };

  return (
    <>
      <View
        className='kb-sms-input at-row at-row__align--center at-row__justify--between'
        onClick={() => setFocus(true)}
      >
        {Array.from({ length }).map((_, index) => (
          <View
            key={index}
            className={classNames('kb-sms-input__item', {
              [`kb-sms-input__item--${type}`]: !!type,
              'kb-sms-input__item--focus': index <= value.length - 1,
            })}
          >
            {value[index]}
          </View>
        ))}
      </View>
      <AtInput
        className='kb-sms-input__input'
        cursor={-1}
        type='number'
        maxLength={length}
        value={value}
        onChange={(v) => handleChange(v)}
        focus={focus}
        onInput={e => {console.log(e, '====')}}
        onBlur={() => setFocus(false)}
      />
    </>
  );
};

SmsInput.options = {
  addGlobalClass: true,
};
export default SmsInput;
