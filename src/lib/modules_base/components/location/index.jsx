import Taro from "@tarojs/taro";
import React, { useEffect, useMemo } from "react";
import KbLoader from "../loader";
import KbEmpty from "../empty";
import { useLocation } from "./_utils/useLocation";
import { View } from "@tarojs/components";
import { AtButton } from "taro-ui";

const Location = (props) => {
    const { scope, loading, locationInfo, errMsg, openSetting } = useLocation();
    const { children } = props;

    const renderChildren = useMemo(
        () => !locationInfo
            ? null
            : React.isValidElement(children)
                ? React.cloneElement(children, {
                    locationInfo,
                })
                : children,
        [locationInfo, children]
    )

    return (
        <>
            {
                renderChildren
                    ? renderChildren
                    : (
                        <View className="kb-scrollview">
                            {
                                (
                                    loading
                                        ? (
                                            <KbLoader centered loadingText='定位中...' />
                                        )
                                        : (
                                            <KbEmpty centered description={errMsg}>
                                                <>
                                                    {
                                                        !scope && (
                                                            <View className='footer-inner'>
                                                                <AtButton size='small' type='primary' onClick={openSetting}>开启定位</AtButton>
                                                            </View>
                                                        )
                                                    }
                                                </>
                                            </KbEmpty>
                                        )
                                )
                            }
                        </View>
                    )
            }
        </>
    )
}

export default Location;