/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import Taro from '@tarojs/taro';
import pick from 'lodash/pick';
import { useState } from 'react';
import { useDidShowCom } from '~base/hooks/page';

export function useLocation() {
  const [locationInfo, setLocationInfo] = useState(null);
  const [errMsg, setErrMsg] = useState('');
  const [loading, setLoading] = useState(false);
  const [scope, setScope] = useState(true);

  // 获取模糊定位信息
  const getLocation = () => {
    return new Promise((resolve, reject) => {
      Taro.getFuzzyLocation({
        type: 'wgs84',
        success: (res) => {
          setLocationInfo(pick(res, ['latitude', 'longitude']));
          resolve(res);
        },
        fail: (err) => {
          reject(err);
        },
      });
    });
  };

  // 获取权限
  const getSetting = () => {
    return new Promise((resolve, reject) => {
      Taro.getSetting({
        success: (res) => {
          const auth = res.authSetting['scope.userLocation'];
          if (auth) {
            resolve();
          } else {
            reject();
          }
        },
      });
    });
  };

  // 更新权限
  const updateScope = () => {
    return new Promise((resolve, reject) => {
      Taro.authorize({
        scope: 'scope.userFuzzyLocation',
        success: () => {
          setScope(true);
          getLocation().then(resolve).catch(reject);
        },
        fail: (err) => {
          setScope(false);
          reject(err);
        },
      });
    });
  };

  // 检查权限
  const checkAndGetLocation = async () => {
    setLoading(true);
    try {
      const res = await Taro.getSetting();
      const status = !!res.authSetting['scope.userFuzzyLocation'];
      if (status) {
        await getLocation();
      } else {
        await updateScope();
      }
      setLoading(false);
      setErrMsg('');
      return res;
    } catch (error) {
      setLoading(false);
      setErrMsg(error.errMsg || error.errorMsg || error.message);
      throw error;
    }
  };

  // 打开权限设置
  const openSetting = () => {
    return new Promise((resolve, reject) => {
      Taro.openSetting({
        success: (res) => {
          checkAndGetLocation().then(resolve).catch(reject);
        },
        fail: (err) => {
          reject(err);
        },
      });
    });
  };

  // 刷新
  const refresh = () => checkAndGetLocation();

  // 监听页面显示
  useDidShowCom(() => {
    checkAndGetLocation();
  });

  return {
    errMsg,
    loading,
    locationInfo,
    scope,
    openSetting,
    refresh,
  };
}

export const getLocationDesc = (location) => {
  let { city = '', district = '' } = location || {};
  if (city && city.slice(-1) === '市') {
    city = city.slice(0, -1);
  }
  if (district && district.slice(-1) === '区') {
    district = '';
  }
  if (district && district.slice(-1) === '县') {
    district = district.slice(0, -1);
  }
  return city ? `${city}${district?.length ? '-' + district : ''}` : '请选择地址';
};
