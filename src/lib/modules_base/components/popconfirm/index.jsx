import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import React, { useEffect, useRef, useState } from 'react';
import { AtButton } from 'taro-ui';

/**
 *
 * @param {{
 *   buttonProps:{circle?:boolean;type:'primary'|'secondary';disabled?:boolean;loading?:boolean;cancelText?:string;confirmText?:string;}
 *   title?:string;
 *   content?:string|React.ReactNode;
 *   cancelText?:string;
 *   confirmText?:string;
 *   onConfirm?:()=>void;
 *   onCancel?:()=>void;
 *   custom?: boolean;
 * }} props
 * @returns
 */
const Popconfirm = (props) => {
  const { custom, children, buttonProps, data, onConfirm, content, ...modalProps } = props;
  const [loading, setLoading] = useState(false);
  const ref = useRef({ modal: null, loading: false });

  const handleClick = (e) => {
    e.stopPropagation();
    ref.current.modal = Taro.kbModal({
      title: '温馨提示',
      cancelText: '取消',
      onConfirm: async () => {
        try {
          if (ref.current.loading) return false;
          setLoading(true);
          const res = await onConfirm(data);
          setLoading(false);
          return res;
        } catch (error) {
          setLoading(false);
        }
      },
      content: content && <View className='kb-text__center kb-color__black'>{content}</View>,
      ...modalProps,
    });
  };

  useEffect(() => {
    ref.current.loading = loading;
    ref.current.modal?.update?.({
      confirmButtonProps: { loading },
    });
  }, [loading]);

  return (
    custom
      ? (
        React.cloneElement(children, {
          onClick: handleClick,
        })
      )
      : (
        <AtButton type='primary' circle {...buttonProps} onClick={handleClick}>
          {children}
        </AtButton>
      )
  );
};

export default Popconfirm;
