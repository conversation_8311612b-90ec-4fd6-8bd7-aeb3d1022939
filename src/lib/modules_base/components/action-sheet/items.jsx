import { View } from "@tarojs/components";
import KbSearch from '~base/components/search';
import classNames from "classnames";
import LongList from "../long-list";
import { useState } from "react";
import { useRequestPatch } from "./_utils";
import ActionSheetItemsContent from "./content";

const ActionSheetItems = (props) => {
    const {
        renderFooter,
        renderEmptyFooter,
        selected,
        multiple,
        onClick,
        showSearch,
        searchProps,
        isOpened,
        enableMore,
        enableRefresh,
        direction,
        align,
        items: itemsProps,
        optionRender
    } = props;
    const [hasList, setHasList] = useState(false);
    const { config, items, active, onSearch, request } = useRequestPatch(props);

    const contentCls = classNames(
        'at-row at-row__justify--center',
        `at-row__direction--${direction}`,
        `at-row__align--${align}`
    );

    const enableItemsProps = !showSearch && itemsProps?.length > 0;

    // 数据加载
    const handleLoad = (l) => setHasList(l?.length > 0);

    return (
        <>
            {showSearch && isOpened && (
                <View className='kb-spacing-md'>
                    <KbSearch cursorSpacing={100} showIcon={false} trigger='blur' circle fixedButton id='action-sheet-search' searchButtonText={<View className='kb-icon kb-icon-search' />} {...searchProps} onSearch={onSearch} />
                </View>
            )}
            {
                enableItemsProps
                    ? (
                        <ActionSheetItemsContent selected={selected} multiple={multiple} onClick={onClick} items={itemsProps} contentCls={contentCls} optionRender={optionRender} />
                    )
                    : (
                        <View>
                            <LongList
                                renderEmptyFooter={renderEmptyFooter}
                                data={config}
                                active={active}
                                height='auto'
                                size='small'
                                request={request}
                                enableMore={enableMore}
                                enableRefresh={enableRefresh}
                                onLoad={handleLoad}
                            >
                                <ActionSheetItemsContent selected={selected} multiple={multiple} onClick={onClick} items={items} contentCls={contentCls} optionRender={optionRender} />
                            </LongList>
                            {
                                hasList && (
                                    <>
                                        {renderFooter}
                                    </>
                                )
                            }
                        </View>
                    )
            }
        </>
    )

}

export default ActionSheetItems;