import { <PERSON><PERSON>, <PERSON><PERSON>View, Text, View } from "@tarojs/components";
import isArray from 'lodash/isArray';
import isObject from 'lodash/isObject';
import KbSearch from '~base/components/search';
import KbLoader from "../loader";
import KbCheckbox from '../checkbox';
import KbEmpty from "../empty";
import { AtActionSheetItem } from "taro-ui";
import classNames from "classnames";
import LongList from "../long-list";
import { useCallback, useMemo, useRef, useState } from "react";
import { useLongList } from "../long-list/hooks";
import { checkIsChecked } from "./_utils";

const ActionSheetItemsContent = (props) => {
    const { onClick, items, contentCls, optionRender, multiple, selected } = props;
    const [hiddenKeys, setHiddenKeys] = useState([]);

    const cls = classNames({
        'kb-sheet__item--multiple': multiple
    });

    const groupTitleCls = classNames('kb-sheet__group--title');
    const groupContentCls = classNames('kb-sheet__group--content');
    const groupContentBodyCls = classNames('kb-sheet__group--body');

    // 分组时，可切换隐藏
    const handleSwitchHiddenKeys = key => {
        setHiddenKeys(pre => {
            const keys = [...pre];
            const i = keys.indexOf(key);
            i >= 0 ? keys.splice(i, 1) : keys.push(key);
            return keys;
        });
    }

    return (
        <>
            {
                items.map((item, index) => {
                    const contents = isArray(item) ? item : [item];
                    const checked = checkIsChecked(item, selected);
                    const itemKey = item.label || item.text || `i_${index}`;
                    const groupKey = item.title;
                    const groupCls = classNames('kb-sheet__group', {
                        'kb-sheet__group--inactive': hiddenKeys.includes(groupKey)
                    });

                    if (item.title && item.children) {
                        return (
                            <View key={groupKey} className={groupCls}>
                                <View className={groupTitleCls} hoverClass="kb-hover" onClick={() => handleSwitchHiddenKeys(groupKey)}>{item.title}</View>
                                <View className={groupContentCls}>
                                    <View className={groupContentBodyCls}>
                                        <ActionSheetItemsContent
                                            {...props}
                                            items={item.children}
                                        />
                                    </View>
                                </View>
                            </View>
                        )
                    }

                    return (
                        <AtActionSheetItem
                            onClick={onClick.bind(null, index, item)}
                            key={itemKey}
                            customStyle={{ padding: 0 }}
                            className={cls}
                        >
                            <Button
                                hoverClass='kb-sheet__button--hover'
                                openType={item.openType}
                                dataPage={item.page}
                                dataInfo={item.info}
                                dataImage={item.image}
                                data-page={item.page}
                                data-info={item.info}
                                data-image={item.image}
                                className='kb-sheet__button'
                            >
                                <View className={contentCls}>
                                    {contents.map((iitem, iindex) => {
                                        const text = iitem.label || iitem.value || iitem.text || iitem;

                                        return (
                                            <View key={iitem.label || iitem.text || `ii_${iindex}`} className={iitem.className || ''}>
                                                {
                                                    optionRender ? optionRender(text, iitem) : <Text className='kb-sheet__button-text'>{text}</Text>
                                                }
                                            </View>
                                        )
                                    })}
                                </View>
                            </Button>
                            {
                                multiple && (
                                    <KbCheckbox checked={checked} onChange={() => onClick(index, item)} />
                                )
                            }
                        </AtActionSheetItem>
                    );
                })
            }
        </>
    )
}

export default ActionSheetItemsContent;