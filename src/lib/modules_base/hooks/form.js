import Taro from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';
import Form from '~base/utils/form';
import { noop } from '~base/utils/utils';

export function createFormWithState(cfg, setState) {
  return new Form(cfg, {
    state: {},
    setState: (data, callback = noop) => {
      const cur = data?.[cfg.name];
      if (cur) {
        setState((pre) => ({
          ...pre,
          ...cur,
        }));
        setTimeout(callback, 0);
      }
    },
  });
}

export function checkFormCanSubmit(data) {
  if (data?.loading) {
    return false;
  }
  if (data?.disabled) {
    const [{ errorMsg } = {}] = Object.values(data.errKeys);
    if (errorMsg) {
      Taro.kbToast({
        text: errorMsg,
      });
    }
    return false;
  }
  return true;
}

export function useForm(formConfig) {
  const formRef = useRef({});
  const [data, setData] = useState({ loading: false, data: {}, disabled: true });

  // 注意formConfig应当引用不变的对象，可使用 useMemo
  useEffect(() => {
    formRef.current = createFormWithState(
      {
        name: 'form',
        ...formConfig,
      },
      setData,
    );
  }, [formConfig]);

  // 提交
  const onFinish = async (req) => {
    try {
      const res = await formRef.current.submit(req);
      return res;
    } catch (error) {}
  };

  // 设置表单数据
  const setFieldsValue = (data) => {
    formRef.current?.update?.(data);
  };

  // 重置
  const resetFieldsValue = () => {
    formRef.current?.resetFormData();
  };

  // 清空
  const cleanFieldsValue = () => {
    formRef.current?.clean();
  };

  return {
    onFinish,
    setFieldsValue,
    resetFieldsValue,
    cleanFieldsValue,
    data,
  };
}
