import { useEffect, useRef, useState } from 'react';
import isEqual from 'lodash/isEqual';

export function useStateExtend(init = [], timer = 0, resetState = init) {
  const ref = useRef(0);
  const [state, setState] = useState(init);

  const stop = () => clearTimeout(ref.current);

  const run = (s) => {
    stop();
    setState(resetState);
    ref.current = setTimeout(() => {
      setState(s);
    }, timer);
  };

  useEffect(() => stop, []);

  return [state, run];
}

/**
 *
 * @description 可以支持异步
 * @param {*} callback
 * @param {*} deps
 */
export function useEffectExtend(callback, deps = []) {
  useEffect(() => {
    (async () => {
      await callback();
    })();
  }, [...deps]);
}

function useDeepCompareMemoize(value) {
  const ref = useRef(value);
  const signalRef = useRef(0);

  if (!isEqual(value, ref.current)) {
    ref.current = value;
    signalRef.current += 1; // 修改 signal 来强制刷新 effect
  }

  // 返回 signalRef.current 是为了能被 useEffect 正确依赖
  return [ref.current, signalRef.current];
}

export function useDeepCompareEffect(effect, deps) {
  // 使用 memoizedDeps 替代原始 deps 进行依赖
  useEffect(effect, useDeepCompareMemoize(deps));
}