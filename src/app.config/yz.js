/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/index/index',
    'pages/scan/index',
    'pages/user/index',
    'pages/user/login/index',
    'pages/webview/index',
  ],
  window: {
    navigationBarTitleText: '无人车',
    backgroundColor: '#f2f2f2',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
    enablePullDownRefresh: false,
  },
  tabBar: {
    custom: true,
    color: '#666666',
    selectedColor: '#11c0c6',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        iconPath: 'assets/tab/home.png',
        selectedIconPath: 'assets/tab/home-active.png',
        text: '取件',
      },
      {
        pagePath: 'pages/scan/index',
        text: '扫一扫',
        className: 'scan',
        icon: { value: 'scan' },
        showText: false,
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/user.png',
        selectedIconPath: 'assets/tab/user-active.png',
        text: '我的',
      },
    ],
  },
};
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/index/index',
    pages: [
      'scan/target/index', // 扫码投件
      'scan/result/index', //投递完成
      'truck/index', // 无人车管理
      'truck/add/index', // 无人车管理 - 添加
      'truck/departure/index', // 发车
      'deliver/record/index', // 投递记录
      'line/manage/index', // 线路管理
      'line/detail/index', // 线路管理 - 详情
      'line/bind/index', // 绑定烟店

      'task/manage/index', //任务管理
      'task/detail/index', //任务详情
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: ['user/wallet/index', 'user/wallet/record/index', 'shop/index', 'shop/list/index'],
  },
  {
    root: 'pages',
    pages: ['examine/index'],
  },
];

module.exports = config;
