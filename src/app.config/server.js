/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/index/index',
    'pages/order/index',
    'pages/truck/index',
    'pages/user/index',
    'pages/webview/index',
  ],
  window: {
    navigationBarTitleText: '快宝智运',
    backgroundColor: '#F7F8FA',
    enablePullDownRefresh: false,
    navigationBarBackgroundColor: '#F7F8FA',
    navigationBarTextStyle: 'black',
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#11c0c6',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        iconPath: 'assets/tab/s/home.png',
        selectedIconPath: 'assets/tab/s/home-active.png',
        text: '接单',
      },
      {
        pagePath: 'pages/order/index',
        iconPath: 'assets/tab/s/order.png',
        selectedIconPath: 'assets/tab/s/order-active.png',
        text: '订单',
      },
      {
        pagePath: 'pages/truck/index',
        iconPath: 'assets/tab/s/truck.png',
        selectedIconPath: 'assets/tab/s/truck-active.png',
        text: '我的车辆',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/s/user.png',
        selectedIconPath: 'assets/tab/s/user-active.png',
        text: '我的',
      },
    ],
  },
};

// 分包
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/index/index',
    pages: [],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/index',
    pages: ['order/detail/index', 'order/settle/index'],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      'user/wallet/index', // 钱包
      'user/wallet/record/index', // 钱包记录
      'user/wallet/cash/index', // 提现
      'user/feedback/index', // 意见反馈
      'user/feedback/record/index', // 意见反馈记录
      'user/feedback/detail/index', // 意见反馈详情
      'user/login/index', // 登录
      'user/login/sms/index', // 短信验证码登录
      'address/choose/index', // 选择地址
      'user/info/index', // 个人信息
      'user/info/auth/index', // 个人信息-认证
      'user/service/address/index', // 服务地址
      'user/partner/index', // 合作车主
      'user/partner/stop-points/index', // 合作车主停靠点
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/truck/index',
    pages: [
      'truck/add/index',
      'truck/detail/index',
      'truck/search/index',
      'truck/choose/index', // 接单车辆
      'truck/choose/search/index', // 接单车辆-搜索
      'truck/choose/rental/index', // 租赁车辆选择
    ],
  },
];

module.exports = config;
