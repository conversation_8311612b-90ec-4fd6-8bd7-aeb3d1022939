/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const config = {
  pages: [
    'pages/index/index',
    'pages/truck/dispatch/index',
    'pages/order/index',
    'pages/user/index',
    'pages/webview/index',
  ],
  window: {
    navigationBarTitleText: '快宝智运',
    backgroundColor: '#F7F8FA',
    enablePullDownRefresh: false,
    navigationBarBackgroundColor: '#F7F8FA',
    navigationBarTextStyle: 'black',
  },
  tabBar: {
    color: '#666666',
    selectedColor: '#11c0c6',
    borderStyle: 'black',
    backgroundColor: '#ffffff',
    list: [
      {
        pagePath: 'pages/index/index',
        iconPath: 'assets/tab/home.png',
        selectedIconPath: 'assets/tab/home-active.png',
        text: '发单',
      },
      {
        pagePath: 'pages/truck/dispatch/index',
        iconPath: 'assets/tab/home.png',
        selectedIconPath: 'assets/tab/dispatch-active.png',
        text: '车辆调度',
      },
      {
        pagePath: 'pages/order/index',
        iconPath: 'assets/tab/order.png',
        selectedIconPath: 'assets/tab/order-active.png',
        text: '订单',
      },
      {
        pagePath: 'pages/user/index',
        iconPath: 'assets/tab/user.png',
        selectedIconPath: 'assets/tab/user-active.png',
        text: '我的',
      },
    ],
  },
};

// 分包
config.subPackages = [
  {
    root: 'pages',
    preloadRule: 'pages/index/index',
    pages: [
      'truck/choose/index', // 接单车辆
      'index/notice/index', // 订单备注
      'index/mobile/index', // 联系电话
      'address/index', // 地址簿
      'address/edit/index', // 地址编辑
      'truck/choose/rental/index', // 租赁车辆选择
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/order/index',
    pages: [
      'order/detail/index',
      'order/create/index', // 确认订单
      'order/create/rental/index', // 确认订单-租赁
      'order/renewal/index', // 续租
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/user/index',
    pages: [
      'user/feedback/index', // 意见反馈
      'user/feedback/record/index', // 意见反馈记录
      'user/feedback/detail/index', // 意见反馈详情
      'user/login/index', // 登录
      'user/login/sms/index', // 短信验证码登录
      'user/service/address/index', // 服务地址
      'address/choose/index', // 选择地址
      'user/info/index', // 个人信息
      'user/partner/index', // 合作车主
      'user/partner/stop-points/index', // 合作车主停靠点
      'user/partner/nearby/index', // 附近车主
    ],
  },
  {
    root: 'pages',
    preloadRule: 'pages/truck/dispatch/index',
    pages: [
      'truck/manage/index',
      'truck/dispatch/depart/index',
      'task/manage/index',
      'task/detail/index',
      'line/manage/index',
      'line/edit/index',
      'line/delivery/index',
      'line/delivery/history/index',
      'contacts/index',
    ],
  },
];

module.exports = config;
